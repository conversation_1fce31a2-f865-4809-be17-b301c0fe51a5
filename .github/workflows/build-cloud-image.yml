name: Build Cloud Image

on:
  workflow_dispatch:

jobs:
  Release:
    runs-on: ubuntu-latest
    steps:
      - name: Check out repository code
        uses: actions/checkout@v3

      - name: Set RELEASE env var from package.json
        run: echo RELEASE=$(node --print "require('./package.json').version") >> $GITHUB_ENV

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push
        uses: docker/build-push-action@v2
        with:
          context: .
          file: ./Dockerfile
          platforms: |
            linux/amd64
          push: true
          tags: |
            ghcr.io/activepieces/activepieces-cloud:${{ env.RELEASE }}.${{ github.sha }}

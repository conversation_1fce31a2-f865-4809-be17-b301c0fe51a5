name: CI
on:
  pull_request:

permissions:
  actions: read
  contents: read

jobs:
  main:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: actions/setup-node@v3
        with:
          node-version: 20
          cache: 'npm'

      - run: npx nx-cloud start-ci-run --distribute-on="3 linux-large-js" --agents 

      - run: npm ci

      - run: npx nx reset 

      - uses: nrwl/nx-set-shas@v4

      - name: List all nx targets
        run: npx nx show projects --all
      
      - name: Get changed files
        id: changed-files
        run: echo "files=$(git diff --name-only HEAD origin/main | tr '\n' ' ')" >> $GITHUB_OUTPUT
      
      - name: Check if framework or common pieces are changed
        id: check-framework-common
        run: |
          CHANGED_FILES="${{ steps.changed-files.outputs.files }}"
          if echo "$CHANGED_FILES" | grep -q "community/framework\|community/common"; then
            echo "framework_or_common_changed=true" >> $GITHUB_OUTPUT
          else
            echo "framework_or_common_changed=false" >> $GITHUB_OUTPUT
          fi
      
      - name: Extract pieces projects from changed files
        id: extract-pieces
        run: |
          PIECES=$(echo "${{ steps.changed-files.outputs.files }}" | grep -o "packages/pieces/[^/]*/[^/]*/" | awk -F'/' '{print "pieces-" $4}' | sort -u | tr '\n' ',' | sed 's/,$//')
          echo "pieces_projects=$PIECES" >> $GITHUB_OUTPUT

      - name: Lint affected projects excluding pieces
        run: npx nx affected --target=lint --exclude="pieces-*" --agents --parallel
      - name: Lint changed pieces projects
        if: steps.extract-pieces.outputs.pieces_projects != '' && steps.check-framework-common.outputs.framework_or_common_changed == 'false'
        run: npx nx run-many --target=lint --projects="${{ steps.extract-pieces.outputs.pieces_projects }}" --agents --parallel
      
      - name: Lint all pieces projects
        if: steps.check-framework-common.outputs.framework_or_common_changed == 'true'
        run: npx nx run-many --target=lint --projects="pieces-*" --agents --parallel
        
      - name: Build affected projects excluding pieces
        run: npx nx affected --target=build -c production --exclude="pieces-*" --agents --parallel
    
      - name: Build changed pieces projects
        if: steps.extract-pieces.outputs.pieces_projects != '' && steps.check-framework-common.outputs.framework_or_common_changed == 'false'
        run: npx nx run-many --target=build -c production --projects="${{ steps.extract-pieces.outputs.pieces_projects }}" --agents --parallel
      
      - name: Build all pieces projects
        if: steps.check-framework-common.outputs.framework_or_common_changed == 'true'
        run: npx nx run-many --target=build -c production --projects="pieces-*" --agents --parallel
        
      - name: Run all tests in parallel
        run: |
          npx nx run-many --target=test --projects=engine,shared --agents --parallel &
          npx nx run server-api:test-ce &
          npx nx run server-api:test-ee &
          npx nx run server-api:test-cloud &
          wait

name: Generate Translation Files for All Pieces

on:
  schedule:
    - cron: '5 15 * * *'
  workflow_dispatch:

jobs:
  generate-translations:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Generate translation files for all pieces
        run: npm run cli pieces generate-translation-file-for-all-pieces
      
      - name: Check for changes
        id: check-changes
        run: |
          if [ -n "$(git status --porcelain)" ]; then
            echo "changes=true" >> $GITHUB_OUTPUT
          else
            echo "changes=false" >> $GITHUB_OUTPUT
          fi
      
      - name: Create branch and commit changes
        id: create-branch
        if: steps.check-changes.outputs.changes == 'true'
        run: |
          # Configure git
          git config --local user.email "<EMAIL>"
          git config --local user.name "AbdulTheActivePiecer"
          BRANCH_NAME="auto/translation-files-$(date +%Y%m%d-%H%M%S)"
          git checkout -b $BRANCH_NAME
          git add .
          git commit -m "feat: generate translation files for all pieces [skip-changelog]"
          git push origin $BRANCH_NAME
          echo "branch_name=$BRANCH_NAME" >> $GITHUB_OUTPUT
      
      - name: Create Pull Request
        if: steps.check-changes.outputs.changes == 'true'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.CROWDIN_PRS }}
          script: |
            const branchName = '${{ steps.create-branch.outputs.branch_name }}';
            const { data: pullRequest } = await github.rest.pulls.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: 'New translation files',
              body: 'Automatically generated translation files for all pieces',
              head: branchName,
              base: 'main',
              draft: false
            });
            
            // Add labels to the PR
            await github.rest.issues.addLabels({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: pullRequest.number,
              labels: ['skip-changelog', 'translations', 'auto-merge']
            });
            
            // Auto merge the PR
            await github.rest.pulls.merge({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: pullRequest.number,
              merge_method: 'squash'
            });

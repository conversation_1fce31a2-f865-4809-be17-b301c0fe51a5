name: Release AP base

on:
  workflow_dispatch:
    inputs:
      tag:
        description: 'image tag'
        required: true

jobs:
  Release-AP-Base:
    runs-on: ubuntu-latest
    steps:
      - name: Check out repository code
        uses: actions/checkout@v3

      - name: Fail if tag already exists
        run: "! docker manifest inspect activepieces/ap-base:${{ inputs.tag }}"

      - name: Set up Depot CLI
        uses: depot/setup-action@v1

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push
        uses: depot/build-push-action@v1
        with:
          project: du7O4b0e8P
          token: ${{ secrets.DEPOT_PROJECT_TOKEN }}
          context: .
          file: ./ap-base.dockerfile
          platforms: |
            linux/amd64
            linux/arm64
            linux/arm/v7
          push: true
          tags: |
            activepieces/ap-base:${{ inputs.tag }}
            activepieces/ap-base:latest

name: "Pull Request Labels - Require At Least One Label"

on:
  pull_request_target:
    types:
      - opened
      - edited
      - synchronize

permissions:
  pull-requests: read

jobs:
  validate-labels:
    name: Check PR Labels
    runs-on: ubuntu-latest
    steps:
      - name: Check PR Labels
        uses: actions/github-script@v7
        with:
          script: |
            const labels = context.payload.pull_request.labels;
            if (!labels || labels.length === 0) {
              core.setFailed('Pull request must have at least one label');
            } 
name: Crowdin Action

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  synchronize-with-crowdin:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: crowdin action
        uses: crowdin/github-action@v2
        with:
          # Configuration file
          config: 'crowdin.yml'
          upload_sources: true
          upload_translations: false
          download_translations: true
          localization_branch_name: l10n_crowdin_translations
          create_pull_request: true
          pull_request_title: 'New Crowdin Translations'
          pull_request_body: 'New Crowdin translations by [Crowdin GH Action](https://github.com/crowdin/github-action)'
          pull_request_base_branch_name: 'main'
        env:
          # A classic GitHub Personal Access Token with the 'repo' scope selected (the user should have write access to the repository).
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          
          # A numeric ID, found at https://crowdin.com/project/<projectName>/tools/api
          CROWDIN_PROJECT_ID: ${{ secrets.CROWDIN_PROJECT_ID }}

          # Visit https://crowdin.com/settings#api-key to create this token
          CROWDIN_PERSONAL_TOKEN: ${{ secrets.CROWDIN_PERSONAL_TOKEN }}
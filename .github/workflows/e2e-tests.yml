name: E2E Tests

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
        description: 'Target environment for e2e tests'
        default: 'Pre-Prod'
    secrets:
      CHECKLY_API_KEY:
        required: true
      CHECKLY_ACCOUNT_ID:
        required: true

jobs:
  run-e2e-checkly-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v3
        with:
          node-version: 20
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci

      - name: Run Checkly tests (${{ inputs.environment }})
        id: checkly-tests
        run: |
          npx checkly test \
            --config=packages/tests-e2e/checkly.config.ts \
            --reporter=github \
            --record \
            -e E2E_CONFIG_MODE="${{ inputs.environment }}"
        env:
          CHECKLY_API_KEY: ${{ secrets.CHECKLY_API_KEY }}
          CHECKLY_ACCOUNT_ID: ${{ secrets.CHECKLY_ACCOUNT_ID }}
          E2E_EMAIL: ${{ secrets.E2E_EMAIL }}
          E2E_PASSWORD: ${{ secrets.E2E_PASSWORD }}
          E2E_CONFIG_MODE: ${{ inputs.environment }}

      - name: Create summary
        run: cat checkly-github-report.md > $GITHUB_STEP_SUMMARY
        if: always()

      - name: Notify Discord on failure
        if: failure()
        run: |
          if [ -f checkly-github-report.md ]; then
            SUMMARY=$(node tools/scripts/format-checkly-report.js checkly-github-report.md "${{ inputs.environment }}")
            curl -H "Content-Type: application/json" \
              -X POST \
              -d "{\"content\": $SUMMARY}" \
              https://discordapp.com/api/webhooks/1395411907413807184/yfzZsR0wDYvk3fgDXeyVz0yAheja9peyxqTlOl6uI8KfOjlzCGR-Thdso9L5fHrZiTmO
          fi
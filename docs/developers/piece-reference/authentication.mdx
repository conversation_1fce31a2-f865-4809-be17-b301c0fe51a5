---
title: "Piece Auth"
description: "Learn about piece authentication"
icon: 'key'
---

Piece authentication is used to gather user credentials and securely store them for future use in different flows. The authentication must be defined as the `auth` parameter in the `createPiece`, `createTrigger`, and `createAction` functions. 

This requirement ensures that the type of authentication can be inferred correctly in triggers and actions.

<Tip>
Friendly Tip: Only at most one authentication is allowed per piece.
</Tip>

### Secret Text

This authentication collects sensitive information, such as passwords or API keys. It is displayed as a masked input field.

**Example:**

```typescript
PieceAuth.SecretText({
    displayName: 'API Key',
    description: 'Enter your API key',
    required: true,
    // Optional Validation
    validate: async ({auth}) => {
        if(auth.startsWith('sk_')){
            return {
                valid: true,
            }
        }
        return {
            valid: false,
            error: 'Invalid Api Key'
        }
    }
})
```

### Username and Password

This authentication collects a username and password as separate fields.

**Example:**

```typescript
PieceAuth.BasicAuth({
    displayName: 'Credentials',
    description: 'Enter your username and password',
    required: true,
    username: {
        displayName: 'Username',
        description: 'Enter your username',
    },
    password: {
        displayName: 'Password',
        description: 'Enter your password',
    },
    // Optional Validation
    validate: async ({auth}) => {
        if(auth){
            return {
                valid: true,
            }
        }
        return {
            valid: false,
            error: 'Invalid Api Key'
        }
    }
})
```

### Custom

This authentication allows for custom authentication by collecting specific properties, such as a base URL and access token.

**Example:**

```typescript
PieceAuth.CustomAuth({
    displayName: 'Custom Authentication',
    description: 'Enter custom authentication details',
    props: {
        base_url: Property.ShortText({
            displayName: 'Base URL',
            description: 'Enter the base URL',
            required: true,
        }),
        access_token: PieceAuth.SecretText({
            displayName: 'Access Token',
            description: 'Enter the access token',
            required: true
        })
    },
    // Optional Validation
    validate: async ({auth}) => {
        if(auth){
            return {
                valid: true,
            }
        }
        return {
            valid: false,
            error: 'Invalid Api Key'
        }
    },
    required: true
})
```

### OAuth2

This authentication collects OAuth2 authentication details, including the authentication URL, token URL, and scope.

**Example:**

```typescript
PieceAuth.OAuth2({
    displayName: 'OAuth2 Authentication',
    grantType: OAuth2GrantType.AUTHORIZATION_CODE,
    required: true,
    authUrl: 'https://example.com/auth',
    tokenUrl: 'https://example.com/token',
    scope: ['read', 'write']
})
```

<Tip>
Please note `OAuth2GrantType.CLIENT_CREDENTIALS` is also supported for service-based authentication.
</Tip>
---
title: 'Webhook Trigger'
description: 'Listen to user events through a single URL'
---

The way webhook triggers usually work is as follows:

**On Enable:**
Use `context.webhookUrl` to perform an HTTP request to register the webhook in a third-party app, and store the webhook Id in the `store`.

**On Handshake:**
Some services require a successful handshake request usually consisting of some challenge. It works similar to a normal run except that you return the correct challenge response. This is optional and in order to enable the handshake you need to configure one of the available handshake strategies in the `handshakeConfiguration` option.

**Run:**
You can find the HTTP body inside `context.payload.body`. If needed, alter the body; otherwise, return an array with a single item `context.payload.body`.

**Disable:**
Using the `context.store`, fetch the webhook ID from the enable step and delete the webhook on the third-party app.

**Testing:**
You cannot test it with Test Flow, as it uses static sample data provided in the piece.
To test the trigger, publish the flow, perform the event. Then check the flow runs from the main dashboard.

**Examples:**

- [New Form Submission on Typeform](https://github.com/activepieces/activepieces/blob/main/packages/pieces/community/typeform/src/lib/trigger/new-submission.ts)

<Warning>
To make your webhook accessible from the internet, you need to configure the backend URL. Follow these steps:

1. Install ngrok.
2. Run the command `ngrok http 4200`.
3. Replace the `AP_FRONTEND_URL` environment variable in `packages/server/api/.env` with the ngrok URL.

Once you have completed these configurations, you are good to go!

</Warning>

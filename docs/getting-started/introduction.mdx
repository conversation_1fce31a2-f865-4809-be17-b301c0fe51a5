---
sidebarTitle: "What is Activepieces?"
title: "🥳 Welcome to Activepieces" 
icon: 'hand-wave'
description: "Your friendliest open source all-in-one automation tool, designed to be extensible."
---
<CardGroup cols={2}>
  <Card
    href="/flows/building-flows"
    title="Learn Concepts"
    icon="shapes"
    color="#8143E3"
  >
    Learn how to work with Activepieces
  </Card>
  <Card
    href="https://www.activepieces.com/pieces"
    title="Pieces"
    icon="puzzle-piece"
    color="#8143E3"
  >
    Browse available pieces
  </Card>
  <Card
    href="/install/overview"
    title="Install"
    icon="server"
    color="#8143E3"
  >
    Learn how to install Activepieces
  </Card>
    <Card
    href="/developers/building-pieces/overview"
    title="Developers"
    icon="code"
    color="#8143E3"
  >
    How to Build Pieces and Contribute 
  </Card>
</CardGroup>

# 🔥 Why Activepieces is Different:

- **💖 Loved by Everyone**: Intuitive interface and great experience for both technical and non-technical users with a quick learning curve.
  

![](/resources/templates.gif)
- **🌐 Open Ecosystem:** All pieces are open source and available on npmjs.com, **60% of the pieces are contributed by the community**.

- **🛠️  Pieces are written in Typescript**: Pieces are npm packages in TypeScript, offering full customization with the best developer experience, including **hot reloading** for **local** piece development on your machine. 😎


![](/resources/create-action.png)


- **🤖 AI-Ready**: Native AI pieces let you experiment with various providers, or create your own agents using our AI SDK, and there is Copilot to help you build flows inside the builder.

- **🏢 Enterprise-Ready**: Developers set up the tools, and anyone in the organization can use the no-code builder. Full customization from branding to control.

- **🔒 Secure by Design**: Self-hosted and network-gapped for maximum security and control over your data.

- **🧠 Human in Loop**: Delay execution for a period of time or require approval. These are just pieces built on top of the piece framework, and you can build many pieces like that. 🎨

- **💻 Human Input Interfaces**: Built-in support for human input triggers like "Chat Interface" 💬 and "Form Interface" 📝



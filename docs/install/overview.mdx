---
title: "Overview"
icon: "hand-wave"
description: "Introduction to the different ways to install Activepieces"
---

Activepieces Community Edition can be deployed using **Docker**, **Docker Compose**, and **Kubernetes**.

<Tip>
Community Edition is **free** and **open source**.

You can read the difference between the editions [here](../about/editions).
</Tip>

## Recommended Options

<CardGroup cols={2}>
  <Card title="Docker (Fastest)" icon="docker" color="#248fe0" href="./options/docker">
Deploy Activepieces as a single Docker container using the SQLite database.
  </Card>

  <Card title="Docker Compose" icon="layer-group" color="#00FFFF" href="./options/docker-compose">
    Deploy Activepieces with **Redis** and **PostgreSQL** setup.
  </Card>

</CardGroup>

## Other Options

<CardGroup cols={2}>

  <Card title="Helm" icon="ship" color="#ff9900" href="./options/helm">
    Install on Kubernetes with <PERSON><PERSON>.
  </Card>

  <Card title="Easypanel" icon={
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 245 245">
          <g clip-path="url(#a)">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M242.291 110.378a15.002 15.002 0 0 0 0-15l-48.077-83.272a15.002 15.002 0 0 0-12.991-7.5H85.07a15 15 0 0 0-12.99 7.5L41.071 65.812a.015.015 0 0 0-.013.008L2.462 132.673a15 15 0 0 0 0 15l48.077 83.272a15 15 0 0 0 12.99 7.5h96.154a15.002 15.002 0 0 0 12.991-7.5l31.007-53.706c.005 0 .01-.003.013-.007l38.598-66.854Zm-38.611 66.861 3.265-5.655a15.002 15.002 0 0 0 0-15l-48.077-83.272a14.999 14.999 0 0 0-12.99-7.5H41.072l-3.265 5.656a15 15 0 0 0 0 15l48.077 83.271a15 15 0 0 0 12.99 7.5H203.68Z" fill="url(#b)" />
          </g>
          <defs>
            <linearGradient id="b" x1="188.72" y1="6.614" x2="56.032" y2="236.437" gradientUnits="userSpaceOnUse">
              <stop stop-color="#12CD87" />
              <stop offset="1" stop-color="#12ABCD" />
            </linearGradient>
            <clipPath id="a">
              <path fill="#fff" d="M0 0h245v245H0z" />
            </clipPath>
          </defs>
        </svg>
        } href="./options/easypanel">
   1-Click Install with Easypanel template, maintained by the community.
  </Card>

  <Card title="Elestio" icon="cloud" color="#ff9900" href="./options/elestio">
    1-Click Install on Elestio.
  </Card>

  <Card title="AWS (Pulumi)" icon="aws" color="#ff9900" href="./options/aws">
    Install on AWS with Pulumi.
  </Card>

  <Card title="GCP" icon="cloud" color="#4385f5" href="./options/gcp">
    Install on GCP as a VM template.
  </Card>

  <Card title="PikaPods" icon={
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 402.2 402.2">
    <path d="M393 277c-3 7-8 9-15 9H66c-27 0-49-18-55-45a56 56 0 0 1 54-68c7 0 12-5 12-11s-5-11-12-11H22c-7 0-12-5-12-11 0-7 4-12 12-12h44c18 1 33 15 33 33 1 19-14 34-33 35-18 0-31 12-34 30-2 16 9 35 31 37h37c5-46 26-83 65-110 22-15 47-23 74-24l-4 16c-4 30 19 58 49 61l8 1c6-1 11-6 10-12 0-6-5-10-11-10-14-1-24-7-30-20-7-12-4-27 5-37s24-14 36-10c13 5 22 17 23 31l2 4c33 23 55 54 63 93l3 17v14m-57-59c0-6-5-11-11-11s-12 5-12 11 6 12 12 12c6-1 11-6 11-12" 
          fill="#4daf4e"/>
  </svg>
  } href="https://www.pikapods.com/pods?run=activepieces">
    Instantly run on PikaPods from $2.9/month.
  </Card>

  <Card title="RepoCloud" icon="cloud" href="https://repocloud.io/details/?app_id=177">
    Easily install on RepoCloud using this template, maintained by the community.
  </Card>

  <Card title="Zeabur" icon={
    <svg viewBox="0 0 294 229" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M113.865 144.888H293.087V229H0V144.888H82.388L195.822 84.112H0V0H293.087V84.112L113.865 144.888Z" fill="black"/>
      <path d="M194.847 0H0V84.112H194.847V0Z" fill="#6300FF"/>
      <path d="M293.065 144.888H114.772V229H293.065V144.888Z" fill="#FF4400"/>
    </svg>
  } href="https://zeabur.com/templates/LNTQDF">
    1-Click Install on Zeabur.
  </Card>

</CardGroup>

## Cloud Edition

<CardGroup cols={2}>
  <Card title="Activepieces Cloud" icon="cloud" color="##5155D7" href="https://cloud.activepieces.com/">
   This is the fastest option.
  </Card>
</CardGroup>

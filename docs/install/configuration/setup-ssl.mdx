---
title: "Setup HTTPS"
description: ""
icon: "shield"
---

To enable SSL, you can use a reverse proxy. In this case, we will use Nginx as the reverse proxy.

## Install Nginx

```bash
sudo apt-get install nginx
```


## Create Certificate

To proceed with this documentation, it is assumed that you already have a certificate for your domain.

<Tip>
You have the option to use Cloudflare or generate a certificate using Let's Encrypt or Certbot.
</Tip>


Add the certificate to the following paths: `/etc/key.pem` and `/etc/cert.pem`


## Setup Nginx

```bash
sudo nano /etc/nginx/sites-available/default
```


```bash
server {
    listen 80;
    listen [::]:80;

    server_name example.com www.example.com;

    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;

    server_name example.com www.example.com;

    ssl_certificate /etc/cert.pem;
    ssl_certificate_key /etc/key.pem;

    location / {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;        
    }
}
```

## Restart Nginx

```bash
sudo systemctl restart nginx
```

## Test

Visit your domain and you should see your application running with SSL.
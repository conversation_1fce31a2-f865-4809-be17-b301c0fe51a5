---
title: 'Environment Variables'
description: ''
icon: 'gear'
---

To configure activepieces, you will need to set some environment variables, There is file called `.env` at the root directory for our main repo.

<Tip> When you execute the [tools/deploy.sh](https://github.com/activepieces/activepieces/blob/main/tools/deploy.sh) script in the Docker installation tutorial, 
it will produce these values. </Tip>

## Environment Variables

| Variable                           | Description                                                                                                                                                                                                          | Default Value                                                                  | Example                                                                |
| ---------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------ | ---------------------------------------------------------------------- |
| `AP_CONFIG_PATH`                   | Optional parameter for specifying the path to store SQLite3 and local settings.                                                                                                                                      | `~/.activepieces`                                                              |                                                                        |
| `AP_CLOUD_AUTH_ENABLED`            | Turn off the utilization of Activepieces oauth2 applications                                                                                                                                                         | `false`                                                                        |                                                                        |
| `AP_DB_TYPE`                       | The type of database to use. (POSTGRES / SQLITE3)                                                                                                                                                                    | `SQLITE3`                                                                      |                                                                        |
| `AP_EXECUTION_MODE`                | You can choose between 'SANDBOXED', 'UNSANDBOXED', 'SANDBOX_CODE_ONLY' as possible values. If you decide to change this, make sure to carefully read https://www.activepieces.com/docs/install/architecture/workers                                                                | `UNSANDBOXED`                                                                  |                                                                        |
| `AP_FLOW_WORKER_CONCURRENCY`       | The number of different flows can be processed in same time                                                                                                                                                          | `10`                                                                           |
| `AP_SCHEDULED_WORKER_CONCURRENCY`  | The number of different scheduled flows can be processed in same time                                                                                                                                                | `10`                                                                           |
| `AP_AGENTS_WORKER_CONCURRENCY`     | The number of different agents can be processed in same time                                                                                                                                                         | `10`                                                                           |
| `AP_ENCRYPTION_KEY`                | ❗️ Encryption key used for connections is a 32-character (16 bytes) hexadecimal key. You can generate one using the following command: `openssl rand -hex 16`.                                                                                                                                                                              | `None`                                                                         |
| `AP_EXECUTION_DATA_RETENTION_DAYS` | The number of days to retain execution data, logs and events.                                                                                                                                                        | `30`                                                                         |                                                                        |
| `AP_FRONTEND_URL`                  | ❗️ Url that will be used to specify redirect url and webhook url. 
| `AP_INTERNAL_URL`                  | (BETA) Used to specify the SSO authentication URL.                                                                                 | `None`                                                                         | [https://demo.activepieces.com/api](https://demo.activepieces.com/api) |
| `AP_JWT_SECRET`                    | ❗️ Encryption key used for generating JWT tokens is a 32-character hexadecimal key. You can generate one using the following command: `openssl rand -hex 32`.                                                                                                                                                                     | `None`                                                                         | [https://demo.activepieces.com](https://demo.activepieces.com)         |
| `AP_QUEUE_MODE`                    | The queue mode to use. (MEMORY / REDIS)                                                                                                                                                                              | `MEMORY`                                                                       |                                                                        |
| `AP_QUEUE_UI_ENABLED`              | Enable the queue UI (only works with redis)                                                                                                                                                                          | `true`                                                                         |                                                                        |
| `AP_QUEUE_UI_USERNAME`             | The username for the queue UI. This is required if `AP_QUEUE_UI_ENABLED` is set to `true`.                                                                                                                           | None                                                                           |                                                                        |
| `AP_QUEUE_UI_PASSWORD`             | The password for the queue UI. This is required if `AP_QUEUE_UI_ENABLED` is set to `true`.                                                                                                                           | None                                                                           |                                                                        |
| `AP_REDIS_FAILED_JOB_RETENTION_DAYS` | The number of days to retain failed jobs in Redis.                                                                                                                                                                  | `30`                                                                           |                                                                        |
| `AP_REDIS_FAILED_JOB_RETENTION_MAX_COUNT` | The maximum number of failed jobs to retain in Redis.                                                                                                                                                          | `2000`                                                                         |                                                                        |
| `AP_TRIGGER_DEFAULT_POLL_INTERVAL` | The default polling interval determines how frequently the system checks for new data updates for pieces with scheduled triggers, such as new Google Contacts.                                                       | `5`                                                                            |                                                                        |
| `AP_PIECES_SOURCE`                 | `AP_PIECES_SOURCE`: `FILE` for local development, `DB` for database. You can find more information about it in [Setting Piece Source](#setting-piece-source) section.                                                | `CLOUD_AND_DB`                                                                 |                                                                        |
| `AP_PIECES_SYNC_MODE`              | `AP_PIECES_SYNC_MODE`: `NONE` for no metadata syncing / 'OFFICIAL_AUTO' for automatic syncing for pieces metadata from cloud                                                                                         | `OFFICIAL_AUTO`                                                                |
| `AP_POSTGRES_DATABASE`             | ❗️ The name of the PostgreSQL database                                                                                                                                                                              | `None`                                                                         |                                                                        |
| `AP_POSTGRES_HOST`                 | ❗️ The hostname or IP address of the PostgreSQL server                                                                                                                                                              | `None`                                                                         |                                                                        |
| `AP_POSTGRES_PASSWORD`             | ❗️ The password for the PostgreSQL,  you can generate a 32-character hexadecimal key using the following command: `openssl rand -hex 32`.                                                                                                                                                                              | `None`                                                                         |                                                                        |
| `AP_POSTGRES_PORT`                 | ❗️ The port number for the PostgreSQL server                                                                                                                                                                        | `None`                                                                         |                                                                        |
| `AP_POSTGRES_USERNAME`             | ❗️ The username for the PostgreSQL user                                                                                                                                                                             | `None`                                                                         |                                                                        |
| `AP_POSTGRES_USE_SSL`              | Use SSL to connect the postgres database                                                                                                                                                                             | `false`                                                                        |                                                                        |
| `AP_POSTGRES_SSL_CA`               | Use SSL Certificate to connect to the postgres database                                                                                                                                                              |
| `AP_POSTGRES_URL`                  | Alternatively, you can specify only the connection string (e.g **********************************/database) instead of providing the database, host, port, username, and password.                                   | `None`                                                                         |                                                                        |
| `AP_REDIS_TYPE`                    | Type of Redis, Possible values are `DEFAULT` or `SENTINEL`.                                                                                                                                  | `DEFAULT`                                                                   |                                                                        
| `AP_REDIS_URL`                     | If a Redis connection URL is specified, all other Redis properties will be ignored.                                                                                                                                  | `None`                                                                         |                                                                        |
| `AP_REDIS_USER`                    | ❗️ Username to use when connect to redis                                                                                                                                                                            | `None`                                                                         |                                                                        |
| `AP_REDIS_PASSWORD`                | ❗️ Password to use when connect to redis                                                                                                                                                                            | `None`                                                                         |                                                                        |
| `AP_REDIS_HOST`                    | ❗️ The hostname or IP address of the Redis server                                                                                                                                                                   | `None`                                                                         |                                                                        |
| `AP_REDIS_PORT`                    | ❗️ The port number for the Redis server                                                                                                                                                                             | `None`                                                                         |                                                                        |
| `AP_REDIS_DB`                      | The Redis database index to use                                                                                                                                                                                      | `0`                                                                            |                                                                        |
| `AP_REDIS_USE_SSL`                 | Connect to Redis with SSL                                                                                                                                                                                            | `false`                                                                        |                                                                        |
| `AP_REDIS_SSL_CA_FILE`           | The path to the CA file for the Redis server.                                                                                                                                                             | `None` |  |
| `AP_REDIS_SENTINEL_HOSTS`          | If specified, this should be a comma-separated list of `host:port` pairs for Redis Sentinels. Make sure to set `AP_REDIS_CONNECTION_MODE` to `SENTINEL`                                                                 | `None` | `sentinel-host-1:26379,sentinel-host-2:26379,sentinel-host-3:26379` |
| `AP_REDIS_SENTINEL_NAME`          | The name of the master node monitored by the sentinels.                                                                                                                                                             | `None` | `sentinel-host-1` |
| `AP_REDIS_SENTINEL_ROLE`          | The role to connect to, either `master` or `slave`.                                                                                                                                                             | `None` | `master` |
| `AP_TRIGGER_TIMEOUT_SECONDS`       | Maximum allowed runtime for a trigger to perform polling in seconds                                                                                                                                                                     | `60`                                                                         |                                                                        |
| `AP_FLOW_TIMEOUT_SECONDS`          | Maximum allowed runtime for a flow to run in seconds                                                                                                                                                                        | `600`                                                                         |                                                                        |
| `AP_AGENT_TIMEOUT_SECONDS`         | Maximum allowed runtime for an agent to run in seconds                                                                                                                                                                        | `600`                                                                         |                                                                        |
| `AP_SANDBOX_MEMORY_LIMIT`          | The maximum amount of memory (in kilobytes) that a single sandboxed worker process can use. This helps prevent runaway memory usage in custom code or pieces. If not set, the default is 524288 KB (512 MB).                    | `524288`                                                                         | `1048576`                                                                 |
| `AP_SANDBOX_PROPAGATED_ENV_VARS`   | Environment variables that will be propagated to the sandboxed code. If you are using it for pieces, we strongly suggests keeping everything in the authentication object to make sure it works across AP instances. | `None`                                                                         |                                                                        |
| `AP_TELEMETRY_ENABLED`             | Collect telemetry information.                                                                                                                                                                                       | `true`                                                                         |                                                                        |
| `AP_TEMPLATES_SOURCE_URL`          | This is the endpoint we query for templates, remove it and templates will be removed from UI                                                                                                                         | `https://cloud.activepieces.com/api/v1/flow-templates`                         |                                                                        |
| `AP_WEBHOOK_TIMEOUT_SECONDS`       | The default timeout for webhooks. The maximum allowed is 15 minutes. Please note that Cloudflare limits it to 30 seconds. If you are using a reverse proxy for SSL, make sure it's configured correctly.             | `30`                                                                           |                                                                        |
| `AP_TRIGGER_FAILURE_THRESHOLD`     | The maximum number of consecutive trigger failures is 576 by default, which is equivalent to approximately 2 days.                                                                                                   | `30`                                                                           |        
| `AP_PROJECT_RATE_LIMITER_ENABLED`   | Enforce rate limits and prevent excessive usage by a single project.                                                                                                                                                   | `true`                                                                           |                                                                        |
| `AP_MAX_CONCURRENT_JOBS_PER_PROJECT`| The maximum number of active runs a project can have. This is used to enforce rate limits and prevent excessive usage by a single project.                                                                                 | `100`                                                                           |                                                                        |
| `AP_S3_ACCESS_KEY_ID`              | The access key ID for your S3-compatible storage service. Not required if `AP_S3_USE_IRSA` is `true`.                                                                                                                | `None`                                                                         |                                                                        |
| `AP_S3_SECRET_ACCESS_KEY`          | The secret access key for your S3-compatible storage service. Not required if `AP_S3_USE_IRSA` is `true`.                                                                                                            | `None`                                                                         |                                                                        |
| `AP_S3_BUCKET`                     | The name of the S3 bucket to use for file storage.                                                                                                                                                                   | `None`                                                                         |                                                                        |
| `AP_S3_ENDPOINT`                   | The endpoint URL for your S3-compatible storage service. Not required if `AWS_ENDPOINT_URL` is set.                                                                                                                  | `None`                                                                         | `https://s3.amazonaws.com`                                             |
| `AP_S3_REGION`                     | The region where your S3 bucket is located. Not required if `AWS_REGION` is set.                                                                                                                                     | `None`                                                                         | `us-east-1`                                                            |
| `AP_S3_USE_SIGNED_URLS`            | It is used to route traffic to S3 directly. It should be enabled if the S3 bucket is public.                                                                                                                         | `None`                                                                         |                                                                        |
| `AP_S3_USE_IRSA`                   | Use IAM Role for Service Accounts (IRSA) to connect to S3. When `true`, `AP_S3_ACCESS_KEY_ID` and `AP_S3_ACCESS_KEY_ID` are not required.                                                                            | `None`                                                                         |  `true`                                                                |
| `AP_MAX_FILE_SIZE_MB`              | The maximum allowed file size in megabytes for uploads including logs of flow runs. If logs exceed this size, they will be truncated which may cause flow execution issues.                                                                                                                                                              | `10`                                                                         | `10`                                                                   |
| `AP_FILE_STORAGE_LOCATION`         | The location to store files. Possible values are `DB` for storing files in the database or `S3` for storing files in an S3-compatible storage service.                                                               | `DB`                                                                           |                                                                        |
| `AP_PAUSED_FLOW_TIMEOUT_DAYS`      | The maximum allowed pause duration in days for a paused flow, please note it can not exceed `AP_EXECUTION_DATA_RETENTION_DAYS`                                                                                                                                                            | `30`                                                                           |                                                                        
| `AP_MAX_RECORDS_PER_TABLE`         | The maximum allowed number of records per table                                                                | `1500` | `1500`
| `AP_MAX_FIELDS_PER_TABLE` |  The maximum allowed number of fields per table  | `15` | `15`
| `AP_MAX_TABLES_PER_PROJECT` | The maximum allowed number of tables per project | `20` | `20`
| `AP_MAX_MCPS_PER_PROJECT` | The maximum allowed number of mcp per project | `20` | `20`
| `AP_ENABLE_FLOW_ON_PUBLISH` | Whether publishing a new flow version should automatically enable the flow | `true` | `false`  
| `AP_ISSUE_ARCHIVE_DAYS`            | Controls the automatic archival of issues in the system. Issues that have not been updated for this many days will be automatically moved to an archived state.| `14`  | `1`
| `AP_APP_TITLE` | Initial title shown in the browser tab while loading the app | `Activepieces`  | `Activepieces`
| `AP_FAVICON_URL` | Initial favicon shown in the browser tab while loading the app | `https://cdn.activepieces.com/brand/favicon.ico` | `https://cdn.activepieces.com/brand/favicon.ico`

### OpenTelemetry Configuration

Activepieces supports both standard OpenTelemetry environment variables and vendor-specific configuration for observability and tracing.

#### OpenTelemetry Environment Variables

| Variable | Description | Default Value | Example |
|----------|-------------|---------------|---------|
| `AP_OTEL_ENABLED` | Enable OpenTelemetry tracing | `false` | `true` |
| `OTEL_EXPORTER_OTLP_ENDPOINT` | OTLP exporter endpoint URL | `None` | `https://your-collector:4317/v1/traces` |
| `OTEL_EXPORTER_OTLP_HEADERS` | Headers for OTLP exporter (comma-separated key=value pairs) | `None` | `Authorization=Bearer token` |

**Note**: Both `AP_OTEL_ENABLED` and `OTEL_EXPORTER_OTLP_ENDPOINT` must be set for OpenTelemetry to be enabled.


<Warning>
  The frontend URL is essential for webhooks and app triggers to work. It must
  be accessible to third parties to send data.
</Warning>


### Setting Webhook (Frontend URL):

The default URL is set to the machine's IP address. To ensure proper operation, ensure that this address is accessible or specify an `AP_FRONTEND_URL` environment variable.

One possible solution for this is using a service like ngrok ([https://ngrok.com/](https://ngrok.com/)), which can be used to expose the frontend port (4200) to the internet.

### Setting Piece Source

These are the different options for the `AP_PIECES_SOURCE` environment variable:

1. `FILE`: **Only for Local Development**, this option loads pieces directly from local files. For Production, please consider using other options, as this one only supports a single version per piece.

2. `DB`: This option will only load pieces that are manually installed in the database from "My Pieces" or the Admin Console in the EE Edition. Pieces are loaded from npm, which provides multiple versions per piece, making it suitable for production.

You can also set AP_PIECES_SYNC_MODE to `OFFICIAL_AUTO`, where it will update the metadata of pieces periodically.

### Redis Configuration

Set the `AP_REDIS_URL` environment variable to the connection URL of your Redis server. 

Please note that if a Redis connection URL is specified, all other **Redis properties** will be ignored.

<Info>
If you don't have the Redis URL, you can use the following command to get it. You can use the following variables:

- `REDIS_USER`: The username to use when connecting to Redis.
- `REDIS_PASSWORD`: The password to use when connecting to Redis.
- `REDIS_HOST`: The hostname or IP address of the Redis server.
- `REDIS_PORT`: The port number for the Redis server.
- `REDIS_DB`: The Redis database index to use.
- `REDIS_USE_SSL`: Connect to Redis with SSL.
</Info>
<Info>
  If you are using **Redis Sentinel**, you can set the following environment variables:
  - `AP_REDIS_TYPE`: Set this to `SENTINEL`.
  - `AP_REDIS_SENTINEL_HOSTS`: A comma-separated list of `host:port` pairs for Redis Sentinels. When set, all other Redis properties will be ignored.
  - `AP_REDIS_SENTINEL_NAME`: The name of the master node monitored by the sentinels.
  - `AP_REDIS_SENTINEL_ROLE`: The role to connect to, either `master` or `slave`.
  - `AP_REDIS_PASSWORD`: The password to use when connecting to Redis.
  - `AP_REDIS_USE_SSL`: Connect to Redis with SSL.
  - `AP_REDIS_SSL_CA_FILE`: The path to the CA file for the Redis server.
</Info>

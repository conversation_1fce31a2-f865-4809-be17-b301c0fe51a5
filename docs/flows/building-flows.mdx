---
title: "Building Flows"
icon: 'hammer'
description: "Flow consists of two parts, trigger and actions"
---
## Trigger

The flow's starting point determines its frequency of execution. There are various types of triggers available, such as Schedule Trigger, Webhook Trigger, or Event Trigger based on specific service.

## Action

Actions come after the flow and control what occurs when the flow is activated, like running code or communicating with other services.

In real-life scenario:

![Flow Parts](/resources/flow-parts.png)
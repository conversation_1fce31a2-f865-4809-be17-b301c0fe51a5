---
title: "License"
description: ""
icon: 'file-contract'
---

Activepieces' **core** is released as open source under the [MIT license](https://github.com/activepieces/activepieces/blob/main/LICENSE) and enterprise / cloud editions features are released under [Commercial License](https://github.com/activepieces/activepieces/blob/main/packages/ee/LICENSE)

The MIT license is a permissive license that grants users the freedom to use, modify, or distribute the software without any significant restrictions. The only requirement is that you include the license notice along with the software when distributing it.

Using the enterprise features (under the packages/ee and packages/server/api/src/app/ee folder) with a self-hosted instance requires an Activepieces license. If you are looking for these features, contact us at [<EMAIL>](mailto:<EMAIL>).

**Benefits of Dual Licensing Repo**

- **Transparency** - Everyone can see what we are doing and contribute to the project.
- **Clarity** - Everyone can see what the difference is between the open source and commercial versions of our software.
- **Audit** - Everyone can audit our code and see what we are doing.
- **Faster Development** - We can develop faster and more efficiently.

<Tip>
If you are still confused or have feedback, please open an issue on GitHub or send a message in the #contribution channel on Discord.
</Tip>

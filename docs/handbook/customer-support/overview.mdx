---
title: "Overview" 
icon: "cube"
---

At Activepieces, we take a unique approach to customer support. Instead of having dedicated support staff, our full-time engineers handle support requests on rotation. This ensures you get expert technical help from the people who build the product.

### Support Schedule

Our on-call engineer handles customer support as part of their rotation. For more details about how this works, check out our on-call documentation.


### Support Channels

- Community Support 
    - GitHub Issues: We actively monitor and respond to issues on our [GitHub repository](https://github.com/activepieces/activepieces)
    - Community Forum: We engage with users on our [Community Platform](https://community.activepieces.com/) to provide help and gather feedback
    - Email: only for account related issues, delete account request or billing issues.

- Enterprise Support
    - Enterprise customers receive dedicated support through Slack
    - We use [Pylon](https://usepylon.com) to manage support tickets and customer channels efficiently
    - For detailed information on using Pylon, see our [Pylon Guide](/docs/handbook/customer-support/pylon)


### Support Hours & SLA:

<Warning>
Work in progress—coming soon!
</Warning>
---
title: "Interface Design"
icon: "palette"
---

This page is a collection of resources for interface design. It's a work in progress and will be updated as we go.

## Color Palette

![Color Palette](/resources/color-palette.png)

The palette includes:

- Primary colors for main actions and branding
- Secondary colors for supporting elements
- Semantic colors for status and feedback (success, warning, destructive)


## Tech Stack

Our frontend is built with:

- **React** - Core UI framework
- **Shadcn UI** - Component library 
- **Tailwind CSS** - Utility-first styling


## Learning Resources

- [Interface Design (Chapters 46-53)](https://basecamp.com/gettingreal/09.1-interface-first) from Getting Real by Basecamp

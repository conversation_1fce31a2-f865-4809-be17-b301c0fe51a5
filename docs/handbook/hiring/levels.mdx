---
title: "Our Roles & Levels"
icon: 'layer-group'
---

**Product Engineers** are full stack engineers who handle both the engineering and product side, delivering features end-to-end.

### Our Levels

We break out seniority into three levels, **L1 to L3**.

### L1 Product Engineers

They tend to be early-career.

- They get more management support than folks at other levels. 
- They focus on continuously absorbing new information about our users and how to be effective at **Activepieces**. 
- They aim to be increasingly autonomous as they gain more experience here.

### L2 Product Engineers

They are generally responsible for running a project start-to-finish. 

- They independently decide on the implementation details.
- They work with **Stakeholders** / **teammates** / **L3s** on the plan. 
- They have personal responsibility for the **“how”** of what they’re working on, but share responsibility for the **“what”** and **“why”**. 
- They make consistent progress on their work by continuously defining the scope, incorporating feedback, trying different approaches and solutions, and deciding what will deliver the most value for users.

### L3 Product Engineers
Their scope is bigger than coding, they lead a product area, make key product decisions and guide the team with strong leadership skills.

- **Planning**: They help **L2s** figure out what the next priority things to focus on and guide **L1s** in determining the right sequence of work to get a project done.
- **Day-to-Day Work**: They might be hands-on with the day-to-day work of the team, providing support and resources to their teammates as needed.
- **Customer Communication**: They handle direct communication with customers regarding planning and product direction, ensuring that customer needs and feedback are incorporated into the development process.

### How to Level Up

There is no formal process, but it happens at the end of **each year** and is based on two things:

1. **Manager Review**: Managers look at how well the engineer has performed and grown over the year.
2. **Peer Review**: Colleagues give feedback on how well the engineer has worked with the team.

This helps make sure promotions are fair and based on merit.
---
title: "Onboarding Check List"
icon: 'lightbulb'
---

🎉 Welcome to Activepieces!

This guide provides a checklist for the new hire onboarding process.

---

## 📧 Essentials

- [ ] Set up your @activepieces.com email account and setup 2FA
- [ ] Confirm access to out private Discord server.
- [ ] Get Invited to the Activepieces Github Organization and Setup 2FA
- [ ] Get Assigned to a buddy who will be your onboarding buddy.


<Tip>
During your first two months, we'll schedule 1:1 meetings every two weeks to ensure you're progressing well and to maintain open communication in both directions.
After two months, we will decrease the frequency of the 1:1 to once a month.
</Tip>


<Tip>
If you don't setup the 2FA, We will be alerted from security perspective.
</Tip>

---


### Engineering Checklist

- [ ] Setup your development environment using our setup guide
- [ ] Learn the repository structure and our tech stack (Fastify, React, PostgreSQL, SQLite, Redis)
- [ ] Understand the key database tables (Platform, Projects, Flows, Connections, Users)
- [ ] Complete your first "warmup" task within your first day (it's our tradition!)


---
## 🌟 Tips for Success

- Don't hesitate to ask questions—the team is especially helpful during your first days
- Take time to understand the product from a business perspective
- Work closely with your onboarding buddy to get up to speed quickly
- Review our documentation, explore the codebase, and check out community resources, outside your scope.
- Provide your ideas and feedback regularly

---

Welcome again to the team. We can't wait to see the impact you'll make at Activepieces! 😉

---
title: "Setup Incident.io"
icon: 'bell-ring'
---

Incident.io is our primary tool for managing and responding to urgent issues and service disruptions. 
This guide explains how we use Incident.io to coordinate our on-call rotations and emergency response procedures.

## Setup and Notifications

### Personal Setup

1. Download the Incident.io mobile app from your device's app store
2. Ask your team to add you to the Incident.io workspace
3. Configure your notification preferences:
   - Phone calls for critical incidents
   - Push notifications for high-priority issues
   - Slack notifications for standard updates

### On-Call Rotations

Our team operates on a weekly rotation schedule through Incident.io, where every team member participates. When you're on-call:
- You'll receive priority notifications for all urgent issues
- Phone calls will be placed for critical service disruptions 
- Rotations change every week, with handoffs occurring on Monday mornings
- Response is expected within 15 minutes for critical incidents

<Tip>
  If you are unable to respond to an incident, please escalate to the engineering team.
</Tip>

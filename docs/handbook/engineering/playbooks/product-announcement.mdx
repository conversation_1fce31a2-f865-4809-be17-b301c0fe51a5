---
title: "Feature Announcement"
icon: "bullhorn"
---

When we develop new features, our marketing team handles the public announcements. As engineers, we need to clearly communicate:

1. The problem the feature solves
2. The benefit to our users
3. How it integrates with our product

### Handoff to Marketing Team

There is an integration between GitHub and Linear, that automatically open a ticket for the marketing team after 5 minutes of issue get closed.\
\
Please make sure of the following:

- Github Pull Request is linked to an issue.
- The pull request must have one of these labels: **"Pieces"**, **"Polishing"**, or **"Feature"**.
  - If none of these labels are added, the PR will not be merged.
  - You can also add any other relevant label.
- The GitHub issue must include the correct template (see "Ticket templates" below).

<Tip>
  Bonus: Please include a video showing the marketing team  on how to use this feature so they can create a demo video and market it correctly.
</Tip>

Ticket templates:

```
### What Problem Does This Feature Solve?

### Explain How the Feature Works
[Insert the video link here]

### Target Audience
Enterprise / Everyone 

### Relevant User Scenarios
[Insert Pylon tickets or community posts here]
```
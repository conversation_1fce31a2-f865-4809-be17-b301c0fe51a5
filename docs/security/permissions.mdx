---
title: "Project Permissions"
description: "Documentation on project permissions in Activepieces"
icon: 'user'
---

Activepieces utilizes Role-Based Access Control (RBAC) for managing permissions within projects. Each project consists of multiple flows and users, with each user assigned specific roles that define their actions within the project.

The supported roles in Activepieces are:

- **Admin:**
  - View Flows
  - Edit Flows
  - Publish/Turn On and Off Flows
  - View Runs
  - Retry Runs
  - View Issues
  - Resolve Issues
  - View Connections
  - Edit Connections
  - View Project Members
  - Add/Remove Project Members
  - Configure Git Repo to Sync Flows With
  - Push/Pull Flows to/from Git Repo

- **Editor:**
  - View Flows
  - Edit Flows
  - Publish/Turn On and Off Flows
  - View Runs
  - Retry Runs
  - View Connections
  - Edit Connections
  - View Issues
  - Resolve Issues
  - View Project Members

- **Operator:**
  - Publish/Turn On and Off Flows
  - View Runs
  - Retry Runs
  - View Issues
  - View Connections
  - Edit Connections
  - View Project Members

- **Viewer:**
  - View Flows
  - View Runs
  - View Connections
  - View Project Members
  - View Issues
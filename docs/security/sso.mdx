---
title: "Single Sign-On"
description: ""
icon: 'key'
---


<Snippet file="enterprise-feature.mdx" />

## Enforcing SSO

You can enforce SSO by specifying the domain. As part of the SSO configuration, you have the option to disable email and user login. This ensures that all authentication is routed through the designated SSO provider.


![SSO](/resources/screenshots/sso.png)


## Supported SSO Providers

You can enable various SSO providers, including Google and GitHub, to integrate with your system by configuring SSO.

### Google:

<Steps>
    <Step title="Go to the Developer Console"></Step>
    <Step title="Create an OAuth2 App"></Step>
    <Step title="Copy the Redirect URL from the Configure Screen into the Google App"></Step>
    <Step title="Fill in the Client ID & Client Secret in Activepieces"></Step>
    <Step title="Click Finish"></Step>
</Steps>

### GitHub:

<Steps>
    <Step title="Go to the GitHub Developer Settings"></Step>
    <Step title="Create a new OAuth App"></Step>
    <Step title="Fill in the App details and click Register a new application"></Step>
    <Step title="Use the following Redirect URL from the Configure Screen"></Step>
    <Step title="Fill in the Homepage URL with the URL of your application"></Step>
    <Step title="Click Register application"></Step>
    <Step title="Copy the Client ID and Client Secret and fill them in Activepieces"></Step>
    <Step title="Click Finish"></Step>
</Steps>

### SAML with OKTA:

<Steps>
    <Step title="Go to the Okta Admin Portal and create a new app"></Step>
    <Step title="Select SAML 2.0 as the Sign-on method"></Step>
    <Step title="Fill in the App details and click Next"></Step>
    <Step title="Use the following Single Sign-On URL from the Configure Screen"></Step>
    <Step title="Fill in Audience URI (SP Entity ID) with 'Activepieces'"></Step>
    <Step title="Add the following attributes (firstName, lastName, email)"></Step>
    <Step title="Click Next and Finish"></Step>
    <Step title="Go to the Sign On tab and click on View Setup Instructions"></Step>
    <Step title="Copy the Identity Provider metadata and paste it in the Idp Metadata field"></Step>
    <Step title="Copy the Signing Certificate and paste it in the Signing Key field"></Step>
    <Step title="Click Save"></Step>
</Steps>

### SAML with JumpCloud:

<Steps>
    <Step title="Go to the JumpCloud Admin Portal and create a new app"></Step>
    <Step title="Create SAML App"></Step>
    <Step title="Copy the ACS URL from Activepieces and paste it in the ACS urls">
        ![JumpCloud ACS URL](/resources/screenshots/jumpcloud/acl-url.png)
    </Step>
    <Step title="Fill in Audience URI (SP Entity ID) with 'Activepieces'"></Step>
    <Step title="Add the following attributes (firstName, lastName, email)">
        ![JumpCloud User Attributes](/resources/screenshots/jumpcloud/user-attribute.png)
    </Step>
    <Step title="Include the HTTP-Redirect binding and export the metadata">
        JumpCloud does not provide the `HTTP-Redirect` binding by default. You need to tick this box.
        ![JumpCloud Redirect Binding](/resources/screenshots/jumpcloud/declare-login.png)

        Make sure you press `Save` and then Refresh the Page and Click on `Export Metadata`

        ![JumpCloud Export Metadata](/resources/screenshots/jumpcloud/export-metadata.png)

        <Tip>
            Please Verify ` Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"` inside the xml.
        </Tip>

        After you export the metadata, paste it in the `Idp Metadata` field.

    </Step>
    <Step title="Copy the Certificate and paste it in the Signing Key field">
        Find the `<ds:X509Certificate>` element in the IDP metadata and copy its value. Paste it between these lines:

        ```
        -----BEGIN CERTIFICATE-----
        [PASTE THE VALUE FROM IDP METADATA]
        -----END CERTIFICATE-----
        ```
    </Step>
    <Step title="Make sure you Assigned the App to the User">
        ![JumpCloud Assign App](/resources/screenshots/jumpcloud/user-groups.png)
    </Step>
    <Step title="Click Next and Finish"></Step>
</Steps>
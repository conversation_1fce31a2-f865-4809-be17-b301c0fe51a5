---
title: "Overview"
icon: "webhook"
---

<Tip>
API keys are generated under the platform dashboard at this moment to manage multiple projects, which is only available in the Platform and Enterprise editions, 
<NAME_EMAIL> for more information. 
</Tip>

### Authentication:

The API uses "API keys" to authenticate requests. You can view and manage your API keys from the Platform Dashboard. After creating the API keys, you can pass the API key as a Bearer token in the header.

Example:
`Authorization: Bearer {API_KEY}`

### Pagination 

All endpoints use seek pagination, to paginate through the results, you can provide the `limit` and `cursor` as query parameters.

The API response will have the following structure:
```json
{
    "data": [],
    "next": "string",
    "previous": "string"
}
```

- **`data`**: Holds the requested results or data.

- **`next`**: Provides a starting cursor for the next set of results, if available.

- **`previous`**: Provides a starting cursor for the previous set of results, if applicable.


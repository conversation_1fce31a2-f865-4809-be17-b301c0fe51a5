{"name": "cli", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/cli/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/cli", "main": "packages/cli/src/index.ts", "tsConfig": "packages/cli/tsconfig.lib.json"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/cli/jest.config.ts"}}}, "tags": []}
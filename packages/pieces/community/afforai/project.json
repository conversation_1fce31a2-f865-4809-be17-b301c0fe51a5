{"name": "pieces-afforai", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/pieces/community/afforai/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/pieces/community/afforai", "tsConfig": "packages/pieces/community/afforai/tsconfig.lib.json", "packageJson": "packages/pieces/community/afforai/package.json", "main": "packages/pieces/community/afforai/src/index.ts", "assets": ["packages/pieces/community/afforai/*.md", {"input": "packages/pieces/community/afforai/src/i18n", "output": "./src/i18n", "glob": "**/!(i18n.json)"}], "buildableProjectDepsInPackageJsonType": "dependencies", "updateBuildableProjectDepsInPackageJson": true}}, "publish": {"command": "node tools/scripts/publish.mjs pieces-afforai {args.ver} {args.tag}", "dependsOn": ["build"]}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}, "tags": []}
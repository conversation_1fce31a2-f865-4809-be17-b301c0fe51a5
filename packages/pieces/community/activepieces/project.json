{"name": "pieces-activepieces", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/pieces/community/activepieces/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/pieces/community/activepieces", "tsConfig": "packages/pieces/community/activepieces/tsconfig.lib.json", "packageJson": "packages/pieces/community/activepieces/package.json", "main": "packages/pieces/community/activepieces/src/index.ts", "assets": ["packages/pieces/community/activepieces/*.md", {"input": "packages/pieces/community/activepieces/src/i18n", "output": "./src/i18n", "glob": "**/!(i18n.json)"}], "buildableProjectDepsInPackageJsonType": "dependencies", "updateBuildableProjectDepsInPackageJson": true}}, "publish": {"command": "node tools/scripts/publish.mjs pieces-activepieces {args.ver} {args.tag}", "dependsOn": ["build"]}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}, "tags": []}
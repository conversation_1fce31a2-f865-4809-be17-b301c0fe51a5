{"Activepieces Platform": "Платформа активных частей", "Open source no-code business automation": "Открытый исходный код бизнес-автоматизация", "Base URL": "Базовый URL", "API Key": "Ключ API", "\nActivepieces Platform API is available under the Platform Edition.\n(https://www.activepieces.com/docs/admin-console/overview)\n\n**Note**: The API Key is available in the Platform Dashboard.\n\n": "\nActivepieces Platform API доступен под версией платформы.\n(https://www.activepieces.com/docs/admin-console/overview)\n\n**Примечание**: API ключ доступен в панели управления платформой.\n\n", "Create Project": "Создать проект", "Update Project": "Обновить проект", "List Projects": "Список проектов", "Custom API Call": "Пользовательский вызов API", "Create a new project": "Создать новый проект", "Update a project": "Изменить проект", "List all projects": "Список всех проектов", "Make a custom API call to a specific endpoint": "Сделать пользовательский API вызов к определенной конечной точке", "Display Name": "Показать имя", "Id": "Id", "Notify Status": "Статус уведомления", "Tasks": "Зада<PERSON>и", "Team Members": "Члены команды", "Method": "Метод", "Headers": "Заголовки", "Query Parameters": "Параметры запроса", "Body": "Тело", "No Error on Failure": "Нет ошибок при ошибке", "Timeout (in seconds)": "Таймаут (в секундах)", "Id of the project": "Id проекта", "Authorization headers are injected automatically from your connection.": "Заголовки авторизации включаются автоматически из вашего соединения.", "Always notify": "Всегда уведомлять", "Never notify": "Никогда не уведомлять", "GET": "ПОЛУЧИТЬ", "POST": "ПОСТ", "PATCH": "ПАТЧ", "PUT": "ПОКУПИТЬ", "DELETE": "УДАЛИТЬ", "HEAD": "HEAD"}
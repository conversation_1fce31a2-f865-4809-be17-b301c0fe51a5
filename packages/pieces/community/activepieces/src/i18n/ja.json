{"Activepieces Platform": "Activeepieces Platform", "Open source no-code business automation": "オープンソースコードのない業務自動化", "Base URL": "ベースURL", "API Key": "API キー", "\nActivepieces Platform API is available under the Platform Edition.\n(https://www.activepieces.com/docs/admin-console/overview)\n\n**Note**: The API Key is available in the Platform Dashboard.\n\n": "\nActiveepieces Platform API is available under the Platform Edition.\n(https://www.activeeces.com/docs/admin-console/overview)\n\n**Note**: The API Key is available in the Platform Dashboard.\n\n", "Create Project": "プロジェクトを作成", "Update Project": "プロジェクトを更新", "List Projects": "プロジェクト一覧", "Custom API Call": "カスタムAPI通話", "Create a new project": "新しいプロジェクトを作成", "Update a project": "プロジェクトを更新", "List all projects": "すべてのプロジェクトの一覧", "Make a custom API call to a specific endpoint": "特定のエンドポイントへのカスタム API コールを実行します。", "Display Name": "表示名", "Id": "Id", "Notify Status": "ステータスを通知する", "Tasks": "タスク", "Team Members": "チーム メンバー", "Method": "方法", "Headers": "ヘッダー", "Query Parameters": "クエリパラメータ", "Body": "本文", "No Error on Failure": "失敗時にエラーはありません", "Timeout (in seconds)": "タイムアウト（秒）", "Id of the project": "プロジェクトの Id", "Authorization headers are injected automatically from your connection.": "認証ヘッダは接続から自動的に注入されます。", "Always notify": "常に通知", "Never notify": "通知しない", "GET": "取得", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "削除", "HEAD": "頭"}
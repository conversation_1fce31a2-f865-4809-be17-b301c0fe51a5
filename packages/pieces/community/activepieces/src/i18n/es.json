{"Activepieces Platform": "Plataforma Activepieces", "Open source no-code business automation": "Open source no-code automatización de negocios", "Base URL": "URL base", "API Key": "Clave API", "\nActivepieces Platform API is available under the Platform Edition.\n(https://www.activepieces.com/docs/admin-console/overview)\n\n**Note**: The API Key is available in the Platform Dashboard.\n\n": "\nLa API de Plataforma Activepieces está disponible en la Edición de Plataforma.\n(https://www.activepieces.com/docs/admin-console/overview)\n\n**Nota**: La clave API está disponible en el panel de Plataforma.\n\n", "Create Project": "<PERSON><PERSON>r proyecto", "Update Project": "Actualizar proyecto", "List Projects": "Lista de proyectos", "Custom API Call": "Llamada API personalizada", "Create a new project": "<PERSON>rear un nuevo proyecto", "Update a project": "Actualizar un proyecto", "List all projects": "Listar todos los proyectos", "Make a custom API call to a specific endpoint": "Hacer una llamada API personalizada a un extremo específico", "Display Name": "Mostrar nombre", "Id": "Id", "Notify Status": "Notificar estado", "Tasks": "<PERSON><PERSON><PERSON>", "Team Members": "Miembros del equipo", "Method": "<PERSON><PERSON><PERSON><PERSON>", "Headers": "Encabezados", "Query Parameters": "Parámetros de consulta", "Body": "<PERSON><PERSON><PERSON>", "No Error on Failure": "No hay ningún error en fallo", "Timeout (in seconds)": "Tiempo de espera (en segundos)", "Id of the project": "Id del proyecto", "Authorization headers are injected automatically from your connection.": "Las cabeceras de autorización se inyectan automáticamente desde tu conexión.", "Always notify": "Notificar siempre", "Never notify": "Nunca notificar", "GET": "RECOGER", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "BORRAR", "HEAD": "LIMPIO"}
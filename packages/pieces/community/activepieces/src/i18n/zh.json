{"Activepieces Platform": "Activepieces Platform", "Open source no-code business automation": "Open source no-code business automation", "Base URL": "基本网址", "API Key": "API 密钥", "\nActivepieces Platform API is available under the Platform Edition.\n(https://www.activepieces.com/docs/admin-console/overview)\n\n**Note**: The API Key is available in the Platform Dashboard.\n\n": "\nActivepieces Platform API is available under the Platform Edition.\n(https://www.activepieces.com/docs/admin-console/overview)\n\n**Note**: The API Key is available in the Platform Dashboard.\n\n", "Create Project": "Create Project", "Update Project": "Update Project", "List Projects": "List Projects", "Custom API Call": "自定义 API 呼叫", "Create a new project": "Create a new project", "Update a project": "Update a project", "List all projects": "List all projects", "Make a custom API call to a specific endpoint": "将一个自定义 API 调用到一个特定的终点", "Display Name": "显示名称", "Id": "Id", "Notify Status": "Notify Status", "Tasks": "任务", "Team Members": "Team Members", "Method": "方法", "Headers": "信头", "Query Parameters": "查询参数", "Body": "正文内容", "No Error on Failure": "失败时没有错误", "Timeout (in seconds)": "超时(秒)", "Id of the project": "Id of the project", "Authorization headers are injected automatically from your connection.": "授权头自动从您的连接中注入。", "Always notify": "Always notify", "Never notify": "Never notify", "GET": "获取", "POST": "帖子", "PATCH": "PATCH", "PUT": "弹出", "DELETE": "删除", "HEAD": "黑色"}
{"Activepieces Platform": "Plataforma Ativa", "Open source no-code business automation": "Automação de código aberto sem código aberto", "Base URL": "URL Base", "API Key": "Chave de <PERSON>", "\nActivepieces Platform API is available under the Platform Edition.\n(https://www.activepieces.com/docs/admin-console/overview)\n\n**Note**: The API Key is available in the Platform Dashboard.\n\n": "\nActivepieces Platform API está disponível sob a Edição de Plataforma.\n(https://www.activepieces.com/docs/admin-console/overview)\n\n**Nota**: A API Key está disponível no Painel da Plataforma.\n\n", "Create Project": "<PERSON><PERSON>r <PERSON>", "Update Project": "Atualizar Projeto", "List Projects": "Listar Projetos", "Custom API Call": "Chamada de API personalizada", "Create a new project": "Criar um novo projeto", "Update a project": "Atualizar um projeto", "List all projects": "Listar todos os projetos", "Make a custom API call to a specific endpoint": "Faça uma chamada de API personalizada para um ponto de extremidade específico", "Display Name": "Nome de Exibição", "Id": "Id", "Notify Status": "Notificar Status", "Tasks": "<PERSON><PERSON><PERSON><PERSON>", "Team Members": "Membros da equipe", "Method": "<PERSON><PERSON><PERSON><PERSON>", "Headers": "Cabeçalhos", "Query Parameters": "Parâ<PERSON><PERSON> da consult<PERSON>", "Body": "<PERSON><PERSON><PERSON><PERSON>", "No Error on Failure": "Nenhum erro no Failure", "Timeout (in seconds)": "Tempo limite (em segundos)", "Id of the project": "ID do projeto", "Authorization headers are injected automatically from your connection.": "Os cabeçalhos de autorização são inseridos automaticamente a partir da sua conexão.", "Always notify": "Sempre notificar", "Never notify": "Nunca notificar", "GET": "OBTER", "POST": "POSTAR", "PATCH": "COMPRAR", "PUT": "COLOCAR", "DELETE": "EXCLUIR", "HEAD": "CABEÇA"}
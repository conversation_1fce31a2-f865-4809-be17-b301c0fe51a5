{"Activepieces Platform": "Activepieces Plattform", "Open source no-code business automation": "Open Source no-code Business Automation", "Base URL": "Basis-URL", "API Key": "API-Schlüssel", "\nActivepieces Platform API is available under the Platform Edition.\n(https://www.activepieces.com/docs/admin-console/overview)\n\n**Note**: The API Key is available in the Platform Dashboard.\n\n": "\nActivepieces Platform-API ist unter der Plattform-Edition verfügbar.\n(https://www.activepieces.com/docs/admin-console/overview)\n\n**Hinweis**: Der API-Schlüssel ist im Plattform-Dashboard verfügbar.\n\n", "Create Project": "Projekt erstellen", "Update Project": "Projekt aktualisieren", "List Projects": "Projekte auflisten", "Custom API Call": "Eigener API-Aufruf", "Create a new project": "Neues Projekt erstellen", "Update a project": "Projekt aktualisieren", "List all projects": "Alle Projekte auflisten", "Make a custom API call to a specific endpoint": "Einen benutzerdefinierten API-Aufruf an einen bestimmten Endpunkt machen", "Display Name": "Anzeigename", "Id": "Id", "Notify Status": "Benachrichtigungssta<PERSON>", "Tasks": "Aufgaben", "Team Members": "Teammitglieder", "Method": "<PERSON>e", "Headers": "Kopfzeilen", "Query Parameters": "Abfrageparameter", "Body": "<PERSON><PERSON><PERSON><PERSON>", "No Error on Failure": "<PERSON><PERSON> bei <PERSON>hler", "Timeout (in seconds)": "Timeout (in Sekunden)", "Id of the project": "Id des Projekts", "Authorization headers are injected automatically from your connection.": "Autorisierungs-Header werden automatisch von Ihrer Verbindung injiziert.", "Always notify": "Immer benachrichtigen", "Never notify": "Nie benachrichtigen", "GET": "ERHALTEN", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "LÖSCHEN", "HEAD": "HEAD"}
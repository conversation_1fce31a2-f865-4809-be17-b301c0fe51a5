{"Activepieces Platform": "Activepieces Platform", "Open source no-code business automation": "Open source no-code business automation", "Base URL": "Base URL", "API Key": "API Key", "\nActivepieces Platform API is available under the Platform Edition.\n(https://www.activepieces.com/docs/admin-console/overview)\n\n**Note**: The API Key is available in the Platform Dashboard.\n\n": "\nActivepieces Platform API is available under the Platform Edition.\n(https://www.activepieces.com/docs/admin-console/overview)\n\n**Note**: The API Key is available in the Platform Dashboard.\n\n", "Create Project": "Create Project", "Update Project": "Update Project", "List Projects": "List Projects", "Custom API Call": "Custom API Call", "Create a new project": "Create a new project", "Update a project": "Update a project", "List all projects": "List all projects", "Make a custom API call to a specific endpoint": "Make a custom API call to a specific endpoint", "Display Name": "Display Name", "Id": "Id", "Notify Status": "Notify Status", "Tasks": "Tasks", "Team Members": "Team Members", "Method": "Method", "Headers": "Headers", "Query Parameters": "Query Parameters", "Body": "Body", "No Error on Failure": "No Error on Failure", "Timeout (in seconds)": "Timeout (in seconds)", "Id of the project": "Id of the project", "Authorization headers are injected automatically from your connection.": "Authorization headers are injected automatically from your connection.", "Always notify": "Always notify", "Never notify": "Never notify", "GET": "GET", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "DELETE", "HEAD": "HEAD"}
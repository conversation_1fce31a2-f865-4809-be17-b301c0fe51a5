{"Activepieces Platform": "Activepieces Platform", "Open source no-code business automation": "Open source zakelijke automatisering zonder code", "Base URL": "Basis URL", "API Key": "API Sleutel", "\nActivepieces Platform API is available under the Platform Edition.\n(https://www.activepieces.com/docs/admin-console/overview)\n\n**Note**: The API Key is available in the Platform Dashboard.\n\n": "\nActivepieces Platform API is beschikbaar onder de Platform Edition.\n(https://www.activepieces.com/docs/admin-console/overview)\n\n**Let op**: De API Key is beschikbaar in het Platform Dashboard.\n\n", "Create Project": "Project aanmaken", "Update Project": "Project bijwerken", "List Projects": "<PERSON><PERSON> weergeven", "Custom API Call": "Custom API Call", "Create a new project": "Maak een nieuw project", "Update a project": "Update een project", "List all projects": "Toon alle projecten", "Make a custom API call to a specific endpoint": "Maak een aangepaste API call naar een specifiek eindpunt", "Display Name": "Weergavenaam", "Id": "Id", "Notify Status": "Notificatie status", "Tasks": "Taken", "Team Members": "Team leden", "Method": "<PERSON>e", "Headers": "Kopteksten", "Query Parameters": "Query parameters", "Body": "<PERSON><PERSON><PERSON>", "No Error on Failure": "<PERSON><PERSON> fout bij fout", "Timeout (in seconds)": "Time-out (in seconden)", "Id of the project": "<PERSON><PERSON> van het project", "Authorization headers are injected automatically from your connection.": "Autorisatie headers worden automatisch geïnjecteerd vanuit uw verbinding.", "Always notify": "<PERSON><PERSON><PERSON><PERSON>n", "Never notify": "<PERSON><PERSON> melden", "GET": "KRIJG", "POST": "POSTE", "PATCH": "BEKIJK", "PUT": "PUT", "DELETE": "VERWIJDEREN", "HEAD": "HOOFD"}
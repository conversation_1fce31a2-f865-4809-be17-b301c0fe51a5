{"Activepieces Platform": "Plateforme Activepieces", "Open source no-code business automation": "Automatisation professionnelle sans code source libre", "Base URL": "URL de base", "API Key": "Clé API", "\nActivepieces Platform API is available under the Platform Edition.\n(https://www.activepieces.com/docs/admin-console/overview)\n\n**Note**: The API Key is available in the Platform Dashboard.\n\n": "\nL'API Activepieces Platform est disponible sous l'édition Platform.\n(https://www.activepieces.com/docs/admin-console/overview)\n\n**Note** : La clé API est disponible dans le tableau de bord de la plate-forme.\n\n", "Create Project": "Créer un projet", "Update Project": "Mettre à jour le projet", "List Projects": "Lister les projets", "Custom API Call": "Appel API personnalisé", "Create a new project": "Créer un nouveau projet", "Update a project": "Mettre à jour un projet", "List all projects": "Lister tous les projets", "Make a custom API call to a specific endpoint": "Passez un appel API personnalisé à un point de terminaison spécifique", "Display Name": "Display Name", "Id": "Id", "Notify Status": "Notifier le statut", "Tasks": "Tasks", "Team Members": "Membres de l'équipe", "Method": "Méthode", "Headers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Query Parameters": "Paramètres de requête", "Body": "Corps", "No Error on Failure": "Aucune erreur en cas d'échec", "Timeout (in seconds)": "<PERSON><PERSON><PERSON>atten<PERSON> (en secondes)", "Id of the project": "Id du projet", "Authorization headers are injected automatically from your connection.": "Les en-têtes d'autorisation sont injectés automatiquement à partir de votre connexion.", "Always notify": "Toujours notifier", "Never notify": "Ne jamais notifier", "GET": "OBTENIR", "POST": "POSTER", "PATCH": "PATCH", "PUT": "EFFACER", "DELETE": "SUPPRIMER", "HEAD": "TÊTE"}
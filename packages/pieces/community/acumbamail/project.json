{"name": "pieces-acumbamail", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/pieces/community/acumbamail/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/pieces/community/acumbamail", "tsConfig": "packages/pieces/community/acumbamail/tsconfig.lib.json", "packageJson": "packages/pieces/community/acumbamail/package.json", "main": "packages/pieces/community/acumbamail/src/index.ts", "assets": ["packages/pieces/community/acumbamail/*.md", {"input": "packages/pieces/community/acumbamail/src/i18n", "output": "./src/i18n", "glob": "**/!(i18n.json)"}], "buildableProjectDepsInPackageJsonType": "dependencies", "updateBuildableProjectDepsInPackageJson": true}}, "publish": {"command": "node tools/scripts/publish.mjs pieces-acumbamail {args.ver} {args.tag}", "dependsOn": ["build"]}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}, "tags": []}
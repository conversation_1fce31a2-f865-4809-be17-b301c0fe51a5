{"Acumbamail": "Acumbamail", "Easily send email and SMS campaigns and boost your business": "Отправляйте рассылку по электронной почте и SMS и развивайте свой бизнес", "\n  To obtain your Auth Token, follow these steps:\n  1. Login to your Acumbamail account.\n  2. Go to **https://acumbamail.com/apidoc/**.\n  3. Under **Customer identifier**, you can find auth token;\n  ": "\n  To obtain your Auth Token, follow these steps:\n  1. Login to your Acumbamail account.\n  2. Go to **https://acumbamail.com/apidoc/**.\n  3. Under **Customer identifier**, you can find auth token;\n  ", "Add/Update Subscriber": "Добавить/Обновить подписчика", "Create Subscriber List": "Создать список подписчиков", "Unsuscribe Subscriber": "Отказаться от подписки", "Delete Subscriber List": "Удалить список подписчиков", "Duplicate Template": "Duplicate Template", "Search Subscriber": "Поиск Абонента", "Remove Subscriber": "Удалить подписчика", "Adds a new subscriber to a subscriber list of your choosing.Can be used to update an existing subscriber too.": "Добавляет нового абонента в список подписчиков по вашему выбору. Может использоваться также для обновления существующего абонента.", "Creates a new subscriber list.": "Создает новый список подписчиков.", "Unsubscribes an email address from a subscriber list of your choosing.": "Отписаться от выбранного вами списка подписчиков на адрес электронной почты.", "Deletes an existing subscriber list.": "Удаляет существующий список подписчиков.", "Duplicates an existing template to use it on a email marketing campaign shipping.": "Дублирует существующий шаблон для использования на рассылке по электронной почте.", "Returns the subscriber's advanced data in each list to which they belong.": "Возвращает дополнительные данные абонента в каждом списке, к которому он принадлежит.", "Removes a subscriber from a list": "Удаляет подписчика из списка", "Subscriber List": "Список подписчиков", "Merge Fields": "Объединить поля", "Update Existing Subscriber Data": "Обновить существующие данные абонента", "Double Option": "Двойной вариант", "List Name": "Название списка", "Sener Email": "<PERSON><PERSON>", "Company Name": "Название компании", "Company Address": "Адрес компании", "Company Phone": "Телефон компании", "Email": "Почта", "New Template Name": "Имя нового шаблона", "Origin Template": "Исходный шаблон", "Subscriber Email": "Email подписчика", "Updates the merge fields over the existent ones if the subscriber exists on the subscriber list.": "Обновляет поля слияния над существующими, если абонент существует в списке подписчиков.", "Activates the send of a confirmation email when the subscriber is added.": "Активир<PERSON>ет отправку письма с подтверждением при добавлении абонента.", "Sender e-mail shown to the subscribers of the list when e-mail marketing campaigns are sent to them.": "Электронная почта отправителя, показываемая подписчикам списка, когда к ним отправляются рекламные кампании."}
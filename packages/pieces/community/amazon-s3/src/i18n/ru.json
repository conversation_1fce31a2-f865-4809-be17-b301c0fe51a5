{"Amazon S3": "Amazon S3", "Scalable storage in the cloud": "Масштабируемое хранилище в облаке", "Access Key ID": "ID ключа доступа", "Secret Access Key": "Ключ секретного доступа", "Bucket": "Ведро", "Endpoint": "Endpoint", "Region": "Регион", "Default": "По умолчанию", "US East (N. Virginia) [us-east-1]": "US East (N. Virginia) [us-east-1]", "US East (Ohio) [us-east-2]": "US East (Ohio) [us-east-2]", "US West (N. California) [us-west-1]": "US West (N. California) [us-west-1]", "US West (Oregon) [us-west-2]": "US West (Oregon) [us-west-2]", "Africa (Cape Town) [af-south-1]": "Africa (Cape Town) [af-south-1]", "Asia Pacific (Hong Kong) [ap-east-1]": "Asia Pacific (Hong Kong) [ap-east-1]", "Asia Pacific (Mumbai) [ap-south-1]": "Asia Pacific (Mumbai) [ap-south-1]", "Asia Pacific (Osaka-Local) [ap-northeast-3]": "Asia Pacific (Osaka-Local) [ap-northeast-3]", "Asia Pacific (Seoul) [ap-northeast-2]": "Asia Pacific (Seoul) [ap-northeast-2]", "Asia Pacific (Singapore) [ap-southeast-1]": "Asia Pacific (Singapore) [ap-southeast-1]", "Asia Pacific (Sydney) [ap-southeast-2]": "Asia Pacific (Sydney) [ap-southeast-2]", "Asia Pacific (Tokyo) [ap-northeast-1]": "Asia Pacific (Tokyo) [ap-northeast-1]", "Canada (Central) [ca-central-1]": "Canada (Central) [ca-central-1]", "Europe (Frankfurt) [eu-central-1]": "Европа (Франкфурт) [eu-central-1]", "Europe (Ireland) [eu-west-1]": "Европа (Ирландия) [eu-west-1]", "Europe (London) [eu-west-2]": "Европа (Лондон) [eu-west-2]", "Europe (Milan) [eu-south-1]": "Европа (Милан) [eu-south-1]", "Europe (Paris) [eu-west-3]": "Europe (Paris) [eu-west-3]", "Europe (Stockholm) [eu-north-1]": "Europe (Stockholm) [eu-north-1]", "Middle East (Bahrain) [me-south-1]": "Middle East (Bahrain) [me-south-1]", "South America (São Paulo) [sa-east-1]": "South America (São Paulo) [sa-east-1]", "Europe (Spain) [eu-south-2]": "Европа (Испания) [eu-south-2]", "Asia Pacific (Hyderabad) [ap-south-2]": "Asia Pacific (Hyderabad) [ap-south-2]", "Asia Pacific (Jakarta) [ap-southeast-3]": "Asia Pacific (Jakarta) [ap-southeast-3]", "Asia Pacific (Melbourne) [ap-southeast-4]": "Asia Pacific (Melbourne) [ap-southeast-4]", "China (Beijing) [cn-north-1]": "China (Beijing) [cn-north-1]", "China (Ningxia) [cn-northwest-1]": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Нинксия) [cn-northwest-1]", "Europe (Zurich) [eu-central-2]": "Европа (Цюрих) [eu-central-2]", "Middle East (UAE) [me-central-1]": "Middle East (UAE) [me-central-1]", "\nThis piece allows you to upload files to Amazon S3 or other S3 compatible services.\n\nAmazon S3 Settings:\nRegions: https://docs.aws.amazon.com/general/latest/gr/s3.html\nEndpoint: leave blank\n": "\nЭта часть позволяет вам загружать файлы на Amazon S3 или другие S3 совместимые сервисы.\n\nAmazon S3 Настройки:\nРегионы: https://docs.aws.<unk> .com/general/latest/gr/s3.html\nEndpoint: leave blank\n", "Upload File": "Загрузить файл", "Read File": "Читать файл", "Generate signed URL": "Генерировать подписанный URL", "Move File": "Переместить файл", "Delete File": "Удалить файл", "List Files": "Список файлов", "Upload an File to S3": "Загрузить файл в S3", "Read a file from S3 to use it in other steps": "Прочитайте файл S3 для использования его на других этапах", "Generate a signed URL for a file in a s3 bucket": "Генерировать подписанный URL для файла в сегменте s3", "Move a File to Another Folder": "Переместить файл в другую папку", "Deletes an existing file.": "Удаляет существующий файл.", "List all files from an S3 bucket folder/prefix.": "Список всех файлов из папки /префикса S3.", "File": "<PERSON>а<PERSON><PERSON>", "File Name": "Имя файла", "ACL": "ACL", "Content Type": "Ти<PERSON> контента", "Key": "Спецификация", "Expires In (minutes)": "Истекает через (в минутах)", "File Key": "Ключ файла", "Folder Key": "Ключ папки", "Folder path": "Путь к папке", "Maximum Files": "Максимум файлов", "The File Name to use, if not set the API will try to figure out the file name.": "Имя файла, используемое, если он не установлен, API попытается найти имя файла.", "Content Type of the uploaded file, if not set the API will try to figure out the content type.": "Тип содержимого загруженного файла, если он не установлен, API попытается определить тип содержимого.", "The key of the file to read": "Ключ для чтения файла", "The path/filename of the file to get": "Путь к файлу для получения", "How long the URL should remain valid (in minutes).": "Как долго URL-адрес должен оставаться действительным (в минутах).", "The key of the file to move": "Ключ файла для перемещения", "The key of the folder to move the file to": "Ключ папки для перемещения файла в", "The key of the file to delete.": "Ключ файла для удаления.", "The folder path to list files from (e.g., \"folder/\"). Leave empty to list from root.": "Путь к папке со списком файлов (например, \"папка/\"). Оставьте пустым для списка из root.", "Maximum number of files to return (1-1000)": "Максимальное количество возвращаемых файлов (1-1000)", "private": "приватный", "public-read": "публичное чтение", "public-read-write": "публичное чтение", "authenticated-read": "прочтение аутентификации", "aws-exec-read": "aws-exec-read", "bucket-owner-read": "прочитано владельцем контейнера", "bucket-owner-full-control": "полноправное управление ведром-владельцем", "New or Updated File": "Новый или Обновленный файл", "Triggers when you add or update a file in your bucket. The bucket/folder you choose must not contain more than 10,000 files.": "Включает при добавлении или обновлении файла в корзину. Выбранные сегменты/папки не должны содержать более 10000 файлов.", "Markdown": "<PERSON><PERSON>", "Folder Path": "Путь к папке"}
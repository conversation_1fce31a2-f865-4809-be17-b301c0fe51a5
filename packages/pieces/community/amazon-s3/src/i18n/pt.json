{"Amazon S3": "Amazon S3", "Scalable storage in the cloud": "Armazenamento escalável na nuvem", "Access Key ID": "Chave ID de acesso", "Secret Access Key": "<PERSON><PERSON>sso <PERSON>", "Bucket": "Balde", "Endpoint": "Endpoint", "Region": "Região", "Default": "Padrão", "US East (N. Virginia) [us-east-1]": "Leste dos EUA (N. Virgínia) [us-east-1]", "US East (Ohio) [us-east-2]": "Leste dos EUA (Ohio) [us-east-2]", "US West (N. California) [us-west-1]": "Oeste dos EUA (N. Califórnia) [us-west-1]", "US West (Oregon) [us-west-2]": "Oeste dos EUA (Oregon) [us-west-2]", "Africa (Cape Town) [af-south-1]": "África (Cidade do Macaco) [af-south-1]", "Asia Pacific (Hong Kong) [ap-east-1]": "<PERSON><PERSON> (Hong Kong) [ap-east-1]", "Asia Pacific (Mumbai) [ap-south-1]": "Asia Pacific (Mumbai) [ap-south-1]", "Asia Pacific (Osaka-Local) [ap-northeast-3]": "<PERSON><PERSON> (Osaka-Local) [ap-northeast-3]", "Asia Pacific (Seoul) [ap-northeast-2]": "<PERSON><PERSON> (Seul) [ap-northeast-2]", "Asia Pacific (Singapore) [ap-southeast-1]": "<PERSON><PERSON> (Singapura) [ap-southeast-1]", "Asia Pacific (Sydney) [ap-southeast-2]": "<PERSON><PERSON> (Sydney) [ap-southeast-2]", "Asia Pacific (Tokyo) [ap-northeast-1]": "Asia Pacific (Tokyo) [ap-northeast-1]", "Canada (Central) [ca-central-1]": "Canada (Central) [ca-central-1]", "Europe (Frankfurt) [eu-central-1]": "Europa (Frankfurt) [eu-central-1]", "Europe (Ireland) [eu-west-1]": "Europa (Irlanda) [eu-west-1]", "Europe (London) [eu-west-2]": "Europa (Londres) [eu-west-2]", "Europe (Milan) [eu-south-1]": "Europa (Milão) [eu-south-1]", "Europe (Paris) [eu-west-3]": "Europa (Paris) [eu-west-3]", "Europe (Stockholm) [eu-north-1]": "Europe (Stockholm) [eu-north-1]", "Middle East (Bahrain) [me-south-1]": "Oriente Médio (Bahre) [me-south-1]", "South America (São Paulo) [sa-east-1]": "América do Sul (Sa├o Paulo) [sa-east-1]", "Europe (Spain) [eu-south-2]": "Europa (Espanha) [eu-south-2]", "Asia Pacific (Hyderabad) [ap-south-2]": "<PERSON><PERSON> (Hyderabad) [ap-south-2]", "Asia Pacific (Jakarta) [ap-southeast-3]": "Asia Pacific (Jakarta) [ap-southeast-3]", "Asia Pacific (Melbourne) [ap-southeast-4]": "<PERSON><PERSON> (Melbourne) [ap-southeast-4]", "China (Beijing) [cn-north-1]": "China (Pequim) [cn-north-1]", "China (Ningxia) [cn-northwest-1]": "China (Ningxia) [cn-northwest-1]", "Europe (Zurich) [eu-central-2]": "Europa (Zurich) [eu-central-2]", "Middle East (UAE) [me-central-1]": "Oriente Médio (AUE) [me-central-1]", "\nThis piece allows you to upload files to Amazon S3 or other S3 compatible services.\n\nAmazon S3 Settings:\nRegions: https://docs.aws.amazon.com/general/latest/gr/s3.html\nEndpoint: leave blank\n": "\nEsta peça permite que você envie arquivos para Amazon S3 ou outros serviços compatíveis com S3.\n\nConfigurações do Amazon S3:\nRegiões: https://docs.aws.amazon.com/general/latest/gr/s3.html\nEndpoint: deixe em branco\n", "Upload File": "Enviar Arquivo", "Read File": "Arquivo de leitura", "Generate signed URL": "Gerar URL assinada", "Move File": "Mover Arquivo", "Delete File": "Excluir Arquivo", "List Files": "Listar Arquivos", "Upload an File to S3": "Enviar um Arquivo para S3", "Read a file from S3 to use it in other steps": "Leia um arquivo do S3 para usá-lo em outras etapas", "Generate a signed URL for a file in a s3 bucket": "Gerar uma URL assinada para um arquivo em um bucket s3", "Move a File to Another Folder": "Mover um arquivo para outra pasta", "Deletes an existing file.": "Exclui um arquivo existente.", "List all files from an S3 bucket folder/prefix.": "Lista todos os arquivos de uma pasta/prefixo S3.", "File": "Arquivo", "File Name": "Nome do arquivo", "ACL": "PTL", "Content Type": "Tipo de Conteúdo", "Key": "Chave", "Expires In (minutes)": "<PERSON><PERSON><PERSON> <PERSON> (minutos)", "File Key": "Chave do arquivo", "Folder Key": "Chave da pasta", "Folder path": "<PERSON><PERSON><PERSON> da pasta", "Maximum Files": "Máximo de arquivos", "The File Name to use, if not set the API will try to figure out the file name.": "O nome do arquivo a ser usado, se não definido, a API tentará descobrir o nome do arquivo.", "Content Type of the uploaded file, if not set the API will try to figure out the content type.": "Tipo de conteúdo do arquivo enviado, se não definido, a API tentará descobrir o tipo de conteúdo.", "The key of the file to read": "A chave do arquivo a ser lido", "The path/filename of the file to get": "O caminho/nome do arquivo para obter", "How long the URL should remain valid (in minutes).": "Quanto tempo a URL deve permanecer v<PERSON> (em minutos).", "The key of the file to move": "A tecla do arquivo para mover", "The key of the folder to move the file to": "A chave da pasta para mover o arquivo", "The key of the file to delete.": "A chave do arquivo a ser apagado.", "The folder path to list files from (e.g., \"folder/\"). Leave empty to list from root.": "O caminho da pasta para a lista de arquivos (por exemplo, \"pasta/\"). Deixe em branco para listar da raiz", "Maximum number of files to return (1-1000)": "Número máximo de arquivos a retornar (1-1000)", "private": "privada", "public-read": "leitura-pública", "public-read-write": "leitura-escrita", "authenticated-read": "lido-autenticado", "aws-exec-read": "aws-exec-read", "bucket-owner-read": "lecionar-proprietário-do-quadro", "bucket-owner-full-control": "dono-do-do-em-controle completo", "New or Updated File": "Arquivo novo ou atualizado", "Triggers when you add or update a file in your bucket. The bucket/folder you choose must not contain more than 10,000 files.": "Aciona quando você adiciona ou atualiza um arquivo no seu bucket. O bucket/pasta que você escolher não deve conter mais de 10.000 arquivos.", "Markdown": "<PERSON><PERSON>", "Folder Path": "<PERSON><PERSON><PERSON> da pasta"}
{"Amazon S3": "Amazon S3", "Scalable storage in the cloud": "雲端可擴展存儲", "Access Key ID": "訪問密鑰 ID", "Secret Access Key": "秘密訪問密鑰", "Bucket": "桶", "Endpoint": "端點", "Region": "地區", "Default": "默認", "US East (N. Virginia) [us-east-1]": "美國東部 (北弗吉尼亞) [us-east-1]", "US East (Ohio) [us-east-2]": "美國東部 (俄亥俄) [us-east-2]", "US West (N. California) [us-west-1]": "美國西部 (北加利福尼亞) [us-west-1]", "US West (Oregon) [us-west-2]": "美國西部 (俄勒岡) [us-west-2]", "Africa (Cape Town) [af-south-1]": "非洲 (開普敦) [af-south-1]", "Asia Pacific (Hong Kong) [ap-east-1]": "亞太地區 (香港) [ap-east-1]", "Asia Pacific (Mumbai) [ap-south-1]": "亞太地區 (孟買) [ap-south-1]", "Asia Pacific (Osaka-Local) [ap-northeast-3]": "亞太地區 (大阪-當地) [ap-northeast-3]", "Asia Pacific (Seoul) [ap-northeast-2]": "亞太地區 (首爾) [ap-northeast-2]", "Asia Pacific (Singapore) [ap-southeast-1]": "亞太地區 (新加坡) [ap-southeast-1]", "Asia Pacific (Sydney) [ap-southeast-2]": "亞太地區 (悉尼) [ap-southeast-2]", "Asia Pacific (Tokyo) [ap-northeast-1]": "亞太地區 (東京) [ap-northeast-1]", "Canada (Central) [ca-central-1]": "加拿大 (中央) [ca-central-1]", "Europe (Frankfurt) [eu-central-1]": "歐洲 (法蘭克福) [eu-central-1]", "Europe (Ireland) [eu-west-1]": "歐洲 (愛爾蘭) [eu-west-1]", "Europe (London) [eu-west-2]": "歐洲 (倫敦) [eu-west-2]", "Europe (Milan) [eu-south-1]": "歐洲 (米蘭) [eu-south-1]", "Europe (Paris) [eu-west-3]": "歐洲 (巴黎) [eu-west-3]", "Europe (Stockholm) [eu-north-1]": "歐洲 (斯德哥爾摩) [eu-north-1]", "Middle East (Bahrain) [me-south-1]": "中東 (巴林) [me-south-1]", "South America (São Paulo) [sa-east-1]": "南美洲 (聖保羅) [sa-east-1]", "Europe (Spain) [eu-south-2]": "歐洲 (西班牙) [eu-south-2]", "Asia Pacific (Hyderabad) [ap-south-2]": "亞太地區 (海得拉巴) [ap-south-2]", "Asia Pacific (Jakarta) [ap-southeast-3]": "亞太地區 (雅加達) [ap-southeast-3]", "Asia Pacific (Melbourne) [ap-southeast-4]": "亞太地區 (墨爾本) [ap-southeast-4]", "China (Beijing) [cn-north-1]": "中國 (北京) [cn-north-1]", "China (Ningxia) [cn-northwest-1]": "中國 (寧夏) [cn-northwest-1]", "Europe (Zurich) [eu-central-2]": "歐洲 (蘇黎世) [eu-central-2]", "Middle East (UAE) [me-central-1]": "中東 (阿聯酋) [me-central-1]", "\nThis piece allows you to upload files to Amazon S3 or other S3 compatible services.\n\nAmazon S3 Settings:\nRegions: https://docs.aws.amazon.com/general/latest/gr/s3.html\nEndpoint: leave blank\n": "\n這個部分允許您將文件上傳到 Amazon S3 或其他 S3 兼容服務。\n\nAmazon S3 設置：\n地區： https://docs.aws.amazon.com/general/latest/gr/s3.html\n端點：留空\n", "Upload File": "上傳文件", "Read File": "讀取文件", "Generate signed URL": "生成簽名 URL", "Move File": "Move File", "Delete File": "Delete File", "List Files": "List Files", "Upload an File to S3": "將文件上傳到 S3", "Read a file from S3 to use it in other steps": "將文件從 S3 讀取以供其他步驟使用", "Generate a signed URL for a file in a s3 bucket": "為 S3 桶中的文件生成簽名 URL", "Move a File to Another Folder": "Move a File to Another Folder", "Deletes an existing file.": "Deletes an existing file.", "List all files from an S3 bucket folder/prefix.": "List all files from an S3 bucket folder/prefix.", "File": "文件", "File Name": "文件名", "ACL": "訪問控制列表", "Content Type": "Content Type", "Key": "密鑰", "Expires In (minutes)": "過期時間（分鐘）", "File Key": "File Key", "Folder Key": "Folder Key", "Folder path": "Folder path", "Maximum Files": "Maximum Files", "The File Name to use, if not set the API will try to figure out the file name.": "The File Name to use, if not set the API will try to figure out the file name.", "Content Type of the uploaded file, if not set the API will try to figure out the content type.": "Content Type of the uploaded file, if not set the API will try to figure out the content type.", "The key of the file to read": "要讀取的文件密鑰", "The path/filename of the file to get": "要獲取的文件路徑/文件名", "How long the URL should remain valid (in minutes).": "URL 應保持有效的時間（以分鐘為單位）。", "The key of the file to move": "The key of the file to move", "The key of the folder to move the file to": "The key of the folder to move the file to", "The key of the file to delete.": "The key of the file to delete.", "The folder path to list files from (e.g., \"folder/\"). Leave empty to list from root.": "The folder path to list files from (e.g., \"folder/\"). Leave empty to list from root.", "Maximum number of files to return (1-1000)": "Maximum number of files to return (1-1000)", "private": "私有", "public-read": "公開可讀", "public-read-write": "公開讀寫", "authenticated-read": "身份驗證讀取", "aws-exec-read": "AWS 執行讀取", "bucket-owner-read": "桶擁有者讀取", "bucket-owner-full-control": "桶擁有者完全控制", "New or Updated File": "新建或更新的文件", "Triggers when you add or update a file in your bucket. The bucket/folder you choose must not contain more than 10,000 files.": "當您在存儲桶中添加或更新文件時觸發。您選擇的存儲桶/文件夾不得包含超過 10,000 個文件。", "Markdown": "<PERSON><PERSON>", "Folder Path": "文件夾路徑"}
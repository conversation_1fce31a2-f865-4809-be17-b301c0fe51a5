{"Amazon S3": "Amazon S3", "Scalable storage in the cloud": "クラウド内のスケーラブルなストレージ", "Access Key ID": "アクセスキーID", "Secret Access Key": "シークレットアクセスキー", "Bucket": "バケツ入りバケツ", "Endpoint": "Endpoint", "Region": "地域", "Default": "デフォルト", "US East (N. Virginia) [us-east-1]": "US East (バージニア州北部) [us-east-1]", "US East (Ohio) [us-east-2]": "US East (Ohio) [us-east-2]", "US West (N. California) [us-west-1]": "US West (N. California) [us-west-1]", "US West (Oregon) [us-west-2]": "アメリカ西部（オレゴン） [us-west-2]", "Africa (Cape Town) [af-south-1]": "アフリカ (ケープタウン) [af-south-1]", "Asia Pacific (Hong Kong) [ap-east-1]": "アジア太平洋(香港) [ap-east-1]", "Asia Pacific (Mumbai) [ap-south-1]": "Asia Pacific (Mumbai) [ap-south-1]", "Asia Pacific (Osaka-Local) [ap-northeast-3]": "Asia Pacific (Osaka-Local) [ap-northeast-3]", "Asia Pacific (Seoul) [ap-northeast-2]": "アジア太平洋(ソウル) [ap-northeast-2]", "Asia Pacific (Singapore) [ap-southeast-1]": "アジア太平洋(シンガポール) [ap-southeast-1]", "Asia Pacific (Sydney) [ap-southeast-2]": "アジア太平洋(シドニー) [ap-southeast-2]", "Asia Pacific (Tokyo) [ap-northeast-1]": "Asia Pacific (Tokyo) [ap-northeast-1]", "Canada (Central) [ca-central-1]": "Canada (Central) [ca-central-1]", "Europe (Frankfurt) [eu-central-1]": "Europe (Frankfurt) [eu-central-1]", "Europe (Ireland) [eu-west-1]": "ヨーロッパ (アイルランド) [eu-west-1]", "Europe (London) [eu-west-2]": "ヨーロッパ (ロンドン) [eu-west-2]", "Europe (Milan) [eu-south-1]": "ヨーロッパ (ミラノ) [eu-south-1]", "Europe (Paris) [eu-west-3]": "ヨーロッパ (パリ) [eu-west-3]", "Europe (Stockholm) [eu-north-1]": "Europe (Stockholm) [eu-north-1]", "Middle East (Bahrain) [me-south-1]": "中東（バーレーン） [me-south-1]", "South America (São Paulo) [sa-east-1]": "南アメリカ (Safingo Paulo) [sa-east-1]", "Europe (Spain) [eu-south-2]": "ヨーロッパ (スペイン) [eu-south-2]", "Asia Pacific (Hyderabad) [ap-south-2]": "アジア太平洋(ハイデラバード) [ap-south-2]", "Asia Pacific (Jakarta) [ap-southeast-3]": "Asia Pacific (Jakarta) [ap-southeast-3]", "Asia Pacific (Melbourne) [ap-southeast-4]": "アジア太平洋(Melbourne) [ap-southeast-4]", "China (Beijing) [cn-north-1]": "中国 (北京) [cn-north-1]", "China (Ningxia) [cn-northwest-1]": "中国 (Ningxia) [cn-northwest-1]", "Europe (Zurich) [eu-central-2]": "ヨーロッパ (チューリッヒ) [eu-central-2]", "Middle East (UAE) [me-central-1]": "中東(UAE) [me-central-1]", "\nThis piece allows you to upload files to Amazon S3 or other S3 compatible services.\n\nAmazon S3 Settings:\nRegions: https://docs.aws.amazon.com/general/latest/gr/s3.html\nEndpoint: leave blank\n": "\nThis piece allows you to upload files to Amazon S3 or other S3 compatible services.\n\nAmazon S3 Settings:\nRegions: https://docs.aws.amazon.com/general/latest/gr/s3.html\nEndpoint: leave blank\n", "Upload File": "ファイルをアップロード", "Read File": "ファイルの読み取り", "Generate signed URL": "署名済みURLを生成", "Move File": "ファイルを移動", "Delete File": "ファイルを削除", "List Files": "リストファイル", "Upload an File to S3": "S3にファイルをアップロード", "Read a file from S3 to use it in other steps": "他のステップで使用するには、S3からファイルを読み込みます", "Generate a signed URL for a file in a s3 bucket": "S3 Bucket 内のファイルの署名 URL を生成する", "Move a File to Another Folder": "ファイルを別のフォルダに移動", "Deletes an existing file.": "既存のファイルを削除します。", "List all files from an S3 bucket folder/prefix.": "S3 バケットフォルダ/プレフィックスからすべてのファイルを一覧表示します。", "File": "ファイル", "File Name": "ファイル名", "ACL": "ACL", "Content Type": "コンテンツタイプ", "Key": "キー", "Expires In (minutes)": "有効期限 (分)", "File Key": "File Key", "Folder Key": "フォルダーキー", "Folder path": "フォルダのパス", "Maximum Files": "最大ファイル", "The File Name to use, if not set the API will try to figure out the file name.": "使用するファイル名 (File Name) APIを設定しない場合、ファイル名を特定しようとします。", "Content Type of the uploaded file, if not set the API will try to figure out the content type.": "Content Type(コンテンツタイプ)は、APIが設定されていない場合、コンテンツタイプを把握しようとします。", "The key of the file to read": "読み込むファイルのキー", "The path/filename of the file to get": "取得するファイルのパス/ファイル名", "How long the URL should remain valid (in minutes).": "URLの有効期間(分単位)", "The key of the file to move": "移動するファイルのキー", "The key of the folder to move the file to": "ファイルを移動するフォルダのキー", "The key of the file to delete.": "削除するファイルのキー。", "The folder path to list files from (e.g., \"folder/\"). Leave empty to list from root.": "フォルダのパス（例：「フォルダ/」）。空のままにするとルートから一覧表示されます。", "Maximum number of files to return (1-1000)": "Maximum number of files to return (1-1000)", "private": "非公開", "public-read": "public-read", "public-read-write": "public-read-write", "authenticated-read": "認証された読み取り", "aws-exec-read": "aws-exec-read", "bucket-owner-read": "Bucket-owner-read", "bucket-owner-full-control": "Bucket-owner-full control", "New or Updated File": "新規または更新されたファイル", "Triggers when you add or update a file in your bucket. The bucket/folder you choose must not contain more than 10,000 files.": "Bucketにファイルを追加または更新したときにトリガーします。選択したBucket/フォルダには、10,000個以上のファイルが含まれていてはいけません。", "Markdown": "<PERSON><PERSON>", "Folder Path": "フォルダパス"}
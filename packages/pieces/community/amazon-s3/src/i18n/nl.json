{"Amazon S3": "Amazon S3", "Scalable storage in the cloud": "<PERSON><PERSON><PERSON><PERSON><PERSON> opslag in de cloud", "Access Key ID": "Toegangssleutel ID", "Secret Access Key": "Geheime toegangssleutel", "Bucket": "<PERSON><PERSON>", "Endpoint": "Endpoint", "Region": "Regio", "Default": "Standaard", "US East (N. Virginia) [us-east-1]": "VS Oost (N. Virginia) [us-east-1]", "US East (Ohio) [us-east-2]": "VS Oost (Ohio) [us-east-2]", "US West (N. California) [us-west-1]": "VS West (N. <PERSON>) [us-west-1]", "US West (Oregon) [us-west-2]": "VS West (Oregon) [us-west-2]", "Africa (Cape Town) [af-south-1]": "Afrika (Cape Town) [af-south-1]", "Asia Pacific (Hong Kong) [ap-east-1]": "Azië Pacific (Hong Kong) [ap-east-1]", "Asia Pacific (Mumbai) [ap-south-1]": "Asia Pacific (Mumbai) [ap-south-1]", "Asia Pacific (Osaka-Local) [ap-northeast-3]": "A<PERSON><PERSON> (Osaka-Local) [ap-northeast-3]", "Asia Pacific (Seoul) [ap-northeast-2]": "A<PERSON>ë <PERSON> (Seoul) [ap-northeast-2]", "Asia Pacific (Singapore) [ap-southeast-1]": "<PERSON><PERSON>ë <PERSON> (Singapore) [ap-southeast-1]", "Asia Pacific (Sydney) [ap-southeast-2]": "<PERSON><PERSON><PERSON> (Sydney) [ap-southeast-2]", "Asia Pacific (Tokyo) [ap-northeast-1]": "Asia Pacific (Tokyo) [ap-northeast-1]", "Canada (Central) [ca-central-1]": "Canada (Central) [ca-central-1]", "Europe (Frankfurt) [eu-central-1]": "Europa (Frankfurt) [eu-central-1]", "Europe (Ireland) [eu-west-1]": "Europa (Ireland) [eu-west-1]", "Europe (London) [eu-west-2]": "Europa (London) [eu-west-2]", "Europe (Milan) [eu-south-1]": "Europa (Milan) [eu-south-1]", "Europe (Paris) [eu-west-3]": "Europa (Parijs) [eu-west-3]", "Europe (Stockholm) [eu-north-1]": "Europe (Stockholm) [eu-north-1]", "Middle East (Bahrain) [me-south-1]": "<PERSON><PERSON> Oosten (Bahrain) [me-south-1]", "South America (São Paulo) [sa-east-1]": "<PERSON>uid<PERSON><PERSON><PERSON><PERSON> (Saľo Paulo) [sa-east-1]", "Europe (Spain) [eu-south-2]": "Europa (Spanje) [eu-south-2]", "Asia Pacific (Hyderabad) [ap-south-2]": "Azië Pacific (Hyderabad) [ap-south-2]", "Asia Pacific (Jakarta) [ap-southeast-3]": "Asia Pacific (Jakarta) [ap-southeast-3]", "Asia Pacific (Melbourne) [ap-southeast-4]": "<PERSON><PERSON><PERSON> (Melbourne) [ap-southeast-4]", "China (Beijing) [cn-north-1]": "China (Beijing) [cn-north-1]", "China (Ningxia) [cn-northwest-1]": "China (Ningxia) [cn-northwest-1]", "Europe (Zurich) [eu-central-2]": "Europa (Zurich) [eu-central-2]", "Middle East (UAE) [me-central-1]": "Midden Oosten (AAE) [me-central-1]", "\nThis piece allows you to upload files to Amazon S3 or other S3 compatible services.\n\nAmazon S3 Settings:\nRegions: https://docs.aws.amazon.com/general/latest/gr/s3.html\nEndpoint: leave blank\n": "\nDit stuk maakt het mogelijk om bestanden te uploaden naar Amazon S3 of andere S3 compatibele diensten.\n\nAmazon S3 Instellingen:\nRegio's: https://docs.aws.where.com/general/latest/gr/s3.html\nEindpunt: laat leeg\n", "Upload File": "Bestand uploaden", "Read File": "<PERSON><PERSON> lezen", "Generate signed URL": "Genereer ondertekende URL", "Move File": "<PERSON><PERSON> ve<PERSON>en", "Delete File": "Bestand verwijderen", "List Files": "<PERSON><PERSON><PERSON>", "Upload an File to S3": "Een bestand uploaden naar S3", "Read a file from S3 to use it in other steps": "<PERSON><PERSON> een bestand van S3 om het in andere stappen te gebruiken", "Generate a signed URL for a file in a s3 bucket": "Genereer een ondertekende URL voor een bestand in een s3 bucket", "Move a File to Another Folder": "Verplaats een bestand naar een andere map", "Deletes an existing file.": "<PERSON>er<PERSON>j<PERSON>t een bestaand bestand.", "List all files from an S3 bucket folder/prefix.": "Toon alle bestanden van een S3 bucket map/voorvoegsel.", "File": "Bestand", "File Name": "File Name", "ACL": "ACL", "Content Type": "Type inhoud", "Key": "<PERSON><PERSON><PERSON><PERSON>", "Expires In (minutes)": "<PERSON><PERSON><PERSON><PERSON> over (minuten)", "File Key": "<PERSON><PERSON>", "Folder Key": "Map Sleutel", "Folder path": "Pad naar map", "Maximum Files": "Maximum aantal bestanden", "The File Name to use, if not set the API will try to figure out the file name.": "De bestandsnaam die gebruikt moet worden, als de API niet instelt, probeert de bestandsnaam te achterhalen.", "Content Type of the uploaded file, if not set the API will try to figure out the content type.": "Content Type van het geüploade bestand, indien niet ingesteld wordt de API zal proberen uit te zoeken wat het inhoudstype is.", "The key of the file to read": "<PERSON> sleutel van het bestand om te lezen", "The path/filename of the file to get": "Het pad/bestandsna<PERSON> van het te krijgen bestand", "How long the URL should remain valid (in minutes).": "<PERSON><PERSON> lang moet de URL geldig blijven (in minuten).", "The key of the file to move": "<PERSON> sleutel van het bestand om te verpla<PERSON>en", "The key of the folder to move the file to": "<PERSON> sleutel van de map om het bestand naar te verpla<PERSON>en", "The key of the file to delete.": "De sleutel van het te verwijderen bestand.", "The folder path to list files from (e.g., \"folder/\"). Leave empty to list from root.": "Het pad naar een lijst van bestanden (bijv. \"map/\"). Laat leeg voor de lijst van root.", "Maximum number of files to return (1-1000)": "Maximum aantal bestanden om terug te sturen (1-1000)", "private": "Priv<PERSON>", "public-read": "publiek-gelezen", "public-read-write": "publiek-lezen-schrijven", "authenticated-read": "g<PERSON><PERSON><PERSON><PERSON>", "aws-exec-read": "aws-exec-read", "bucket-owner-read": "bucket-eigenaar-gelezen", "bucket-owner-full-control": "bucket-eigenaar-volledige controle", "New or Updated File": "Nieuw of bijgewerkt bestand", "Triggers when you add or update a file in your bucket. The bucket/folder you choose must not contain more than 10,000 files.": "Triggert wanneer u een bestand in uw buffer toevoegt of bijwerkt. De gekozen bucket/map mag niet meer dan 10.000 bestanden bevatten.", "Markdown": "<PERSON><PERSON>", "Folder Path": "Map pad"}
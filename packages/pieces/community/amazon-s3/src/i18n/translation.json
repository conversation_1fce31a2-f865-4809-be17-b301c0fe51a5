{"Amazon S3": "Amazon S3", "Scalable storage in the cloud": "Scalable storage in the cloud", "Access Key ID": "Access Key ID", "Secret Access Key": "Secret Access Key", "Bucket": "Bucket", "Endpoint": "Endpoint", "Region": "Region", "Default": "<PERSON><PERSON><PERSON>", "US East (N. Virginia) [us-east-1]": "US East (N. Virginia) [us-east-1]", "US East (Ohio) [us-east-2]": "US East (Ohio) [us-east-2]", "US West (N. California) [us-west-1]": "US West (N. California) [us-west-1]", "US West (Oregon) [us-west-2]": "US West (Oregon) [us-west-2]", "Africa (Cape Town) [af-south-1]": "Africa (Cape Town) [af-south-1]", "Asia Pacific (Hong Kong) [ap-east-1]": "Asia Pacific (Hong Kong) [ap-east-1]", "Asia Pacific (Mumbai) [ap-south-1]": "Asia Pacific (Mumbai) [ap-south-1]", "Asia Pacific (Osaka-Local) [ap-northeast-3]": "Asia Pacific (Osaka-Local) [ap-northeast-3]", "Asia Pacific (Seoul) [ap-northeast-2]": "Asia Pacific (Seoul) [ap-northeast-2]", "Asia Pacific (Singapore) [ap-southeast-1]": "Asia Pacific (Singapore) [ap-southeast-1]", "Asia Pacific (Sydney) [ap-southeast-2]": "Asia Pacific (Sydney) [ap-southeast-2]", "Asia Pacific (Tokyo) [ap-northeast-1]": "Asia Pacific (Tokyo) [ap-northeast-1]", "Canada (Central) [ca-central-1]": "Canada (Central) [ca-central-1]", "Europe (Frankfurt) [eu-central-1]": "Europe (Frankfurt) [eu-central-1]", "Europe (Ireland) [eu-west-1]": "Europe (Ireland) [eu-west-1]", "Europe (London) [eu-west-2]": "Europe (London) [eu-west-2]", "Europe (Milan) [eu-south-1]": "Europe (Milan) [eu-south-1]", "Europe (Paris) [eu-west-3]": "Europe (Paris) [eu-west-3]", "Europe (Stockholm) [eu-north-1]": "Europe (Stockholm) [eu-north-1]", "Middle East (Bahrain) [me-south-1]": "Middle East (Bahrain) [me-south-1]", "South America (São Paulo) [sa-east-1]": "South America (São Paulo) [sa-east-1]", "Europe (Spain) [eu-south-2]": "Europe (Spain) [eu-south-2]", "Asia Pacific (Hyderabad) [ap-south-2]": "Asia Pacific (Hyderabad) [ap-south-2]", "Asia Pacific (Jakarta) [ap-southeast-3]": "Asia Pacific (Jakarta) [ap-southeast-3]", "Asia Pacific (Melbourne) [ap-southeast-4]": "Asia Pacific (Melbourne) [ap-southeast-4]", "China (Beijing) [cn-north-1]": "China (Beijing) [cn-north-1]", "China (Ningxia) [cn-northwest-1]": "China (Ningxia) [cn-northwest-1]", "Europe (Zurich) [eu-central-2]": "Europe (Zurich) [eu-central-2]", "Middle East (UAE) [me-central-1]": "Middle East (UAE) [me-central-1]", "\nThis piece allows you to upload files to Amazon S3 or other S3 compatible services.\n\nAmazon S3 Settings:\nRegions: https://docs.aws.amazon.com/general/latest/gr/s3.html\nEndpoint: leave blank\n": "\nThis piece allows you to upload files to Amazon S3 or other S3 compatible services.\n\nAmazon S3 Settings:\nRegions: https://docs.aws.amazon.com/general/latest/gr/s3.html\nEndpoint: leave blank\n", "Upload File": "Upload File", "Read File": "Read File", "Generate signed URL": "Generate signed URL", "Move File": "Move File", "Delete File": "Delete File", "List Files": "List Files", "Upload an File to S3": "Upload an File to S3", "Read a file from S3 to use it in other steps": "Read a file from S3 to use it in other steps", "Generate a signed URL for a file in a s3 bucket": "Generate a signed URL for a file in a s3 bucket", "Move a File to Another Folder": "Move a File to Another Folder", "Deletes an existing file.": "Deletes an existing file.", "List all files from an S3 bucket folder/prefix.": "List all files from an S3 bucket folder/prefix.", "File": "File", "File Name": "File Name", "ACL": "ACL", "Content Type": "Content Type", "Key": "Key", "Expires In (minutes)": "Expires In (minutes)", "File Key": "File Key", "Folder Key": "Folder Key", "Folder path": "Folder path", "Maximum Files": "Maximum Files", "The File Name to use, if not set the API will try to figure out the file name.": "The File Name to use, if not set the API will try to figure out the file name.", "Content Type of the uploaded file, if not set the API will try to figure out the content type.": "Content Type of the uploaded file, if not set the API will try to figure out the content type.", "The key of the file to read": "The key of the file to read", "The path/filename of the file to get": "The path/filename of the file to get", "How long the URL should remain valid (in minutes).": "How long the URL should remain valid (in minutes).", "The key of the file to move": "The key of the file to move", "The key of the folder to move the file to": "The key of the folder to move the file to", "The key of the file to delete.": "The key of the file to delete.", "The folder path to list files from (e.g., \"folder/\"). Leave empty to list from root.": "The folder path to list files from (e.g., \"folder/\"). Leave empty to list from root.", "Maximum number of files to return (1-1000)": "Maximum number of files to return (1-1000)", "private": "private", "public-read": "public-read", "public-read-write": "public-read-write", "authenticated-read": "authenticated-read", "aws-exec-read": "aws-exec-read", "bucket-owner-read": "bucket-owner-read", "bucket-owner-full-control": "bucket-owner-full-control", "New or Updated File": "New or Updated File", "Triggers when you add or update a file in your bucket. The bucket/folder you choose must not contain more than 10,000 files.": "Triggers when you add or update a file in your bucket. The bucket/folder you choose must not contain more than 10,000 files.", "Markdown": "<PERSON><PERSON>", "Folder Path": "Folder Path"}
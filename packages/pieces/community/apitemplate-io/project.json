{"name": "pieces-apitemplate-io", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/pieces/community/apitemplate-io/src", "projectType": "library", "release": {"version": {"manifestRootsToUpdate": ["dist/{projectRoot}"], "currentVersionResolver": "git-tag", "fallbackCurrentVersionResolver": "disk"}}, "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/pieces/community/apitemplate-io", "tsConfig": "packages/pieces/community/apitemplate-io/tsconfig.lib.json", "packageJson": "packages/pieces/community/apitemplate-io/package.json", "main": "packages/pieces/community/apitemplate-io/src/index.ts", "assets": ["packages/pieces/community/apitemplate-io/*.md", {"input": "packages/pieces/community/apitemplate-io/src/i18n", "output": "./src/i18n", "glob": "**/!(i18n.json)"}], "buildableProjectDepsInPackageJsonType": "dependencies", "updateBuildableProjectDepsInPackageJson": true}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}}
{"APITemplate.io": "APITemplate.io", "Region": "Регион", "API Key": "Ключ API", "Select your preferred API region for better performance": "Выберите регион API для лучшей производительности", "Your APITemplate.io API key": "Ваш APITemplate.io API ключ", "Default (Singapore)": "По умолчанию (Сингапур)", "Europe (Frankfurt)": "Европа (Франкфурт)", "US East (N. Virginia)": "US East (N. Virginia)", "Australia (Sydney)": "Австралия (Сиднее)", "Alternative - Default (Singapore)": "Альтернативный - по умолчанию (Сингапур)", "Alternative - Europe (Frankfurt)": "Альтер<PERSON><PERSON>ива - Европа (Франкфурт)", "Alternative - US East (N. Virginia)": "Альтернатива - Восток США (Новая Вирджиния)", "\nTo obtain your API key:\n1. Go to https://app.apitemplate.io/.\n2. Navigate to API Integration section.\n3. Copy your API key.\n\nSelect the region closest to your location for better performance.\n": "\nДля получения ключа API:\n1. Перейдите на https://app.apitemplate.io/.\n2. Перейдите в раздел API Integration секции.\n3. Скопируйте ключ API.\n\nВыберите регион, ближайший к вашему местоположению, для лучшей производительности.\n", "Create Image": "Создать изображение", "Create PDF From HTML": "Создать PDF из HTML", "Create PDF From URL": "Создать PDF из URL", "Create PDF": "Создать PDF", "Delete Object": "Удалить объект", "Get Account Information": "Получить информацию об учетной записи", "List Objects": "Список объектов", "Custom API Call": "Пользовательский вызов API", "Creates an image from a template with provided data.": "Создает изображение из шаблона с предоставленными данными.", "Creates a PDF from HTML.": "Создает PDF из HTML.", "Creates a PDF from a webpage URL.": "Создает PDF из URL веб-страницы.", "Creates a PDF from a template with provided data.": "Создает PDF из шаблона с предоставленными данными.", "Deletes a generated PDF or image by its transaction reference or object ID.": "Удаляет сгенерированный PDF или изображение по ссылке транзакции или ID объекта.", "Retrieves account information including usage statistics and account details.": "Извлекает информацию об аккаунте, включая статистику использования и данные учетной записи.", "Retrieves a list of generated PDFs and images with optional filtering": "Извлекает список сгенерированных PDF документов и изображений с опциональной фильтрацией", "Make a custom API call to a specific endpoint": "Сделать пользовательский API вызов к определенной конечной точке", "Template ID": "ID шаблона", "Template Data": "Данные шаблона", "Generation Delay (ms)": "Задержка генерации (мс)", "External Reference ID": "Внешняя ссылка ID", "HTML Content": "Содержимое HTML", "CSS Styles": "CSS Styles", "Data for Templating": "Данные для шаблонов", "Expiration (minutes)": "Срок действия (минут)", "Page Size": "Размер страницы", "Page Orientation": "Ориентация страницы", "Margin Top (mm)": "Отступ сверху (мм)", "Margin Bottom (mm)": "Отступ снизу (мм)", "Margin Left (mm)": "Отступ слева (мм)", "Margin Right (mm)": "Правый край (мм)", "Print Background": "Печать фона", "Header Font Size": "Header <PERSON><PERSON>", "Display Header/Footer": "Отображать верхний колонтитул", "Custom Header HTML": "Пользовательские заголовки HTML", "Custom Footer HTML": "HTML-код футера", "Scale": "Масш<PERSON><PERSON><PERSON>", "Wait Timeout (ms)": "Время ожидания (мс)", "URL": "URL", "Wait for Selector": "Дождитесь выбора", "Viewport Width": "<PERSON><PERSON><PERSON><PERSON><PERSON> вида", "Viewport Height": "Высота видового поля", "Full Page": "Полная страница", "Transaction Reference": "Ссылка на транзакцию", "Limit": "<PERSON>и<PERSON><PERSON><PERSON>", "Offset": "Смещение", "Date From": "<PERSON><PERSON><PERSON><PERSON> <PERSON>т", "Date To": "Дата до", "Meta Filter": "Мета-фильтр", "Method": "Метод", "Headers": "Заголовки", "Query Parameters": "Параметры запроса", "Body": "Тело", "No Error on Failure": "Нет ошибок при ошибке", "Timeout (in seconds)": "Таймаут (в секундах)", "JSON data with overrides array to populate the template. Format: {\"overrides\": [{\"name\": \"object_name\", \"property\": \"value\"}]}.": "JSON данные с переопределением массива для заполнения шаблона. Формат: {\"Переопределить\": [{\"name\": \"object_name\", \"property\": \"value\"}]}.", "Delay in milliseconds before PDF generation": "Задержка в миллисекундах до генерации PDF", "Specify an external reference ID for your own reference": "Укажите внешний идентификатор ссылки для вашей собственной ссылки", "The HTML content to convert to PDF. Can include CSS styles and external resources.": "Контент HTML для преобразования в PDF. Может включать стили CSS и внешние ресурсы.", "Optional CSS styles to apply to the HTML content. Can include inline styles or external stylesheets.": "Необязательные CSS стили для применения к HTML-содержимому. Может включать встроенные стили или внешние стили.", "Optional JSON data to use for templating the HTML content. Can include variables and dynamic content.": "Необязательные данные JSON, используемые для шаблона HTML содержимого. Может включать переменные и динамическое содержимое.", "Expiration of the generated PDF in minutes. Use 0 to store permanently, or 1-10080 minutes (7 days) to specify expiration.": "Срок действия сгенерированного PDF за считанные минуты. Используйте 0 для хранения навсегда, или 1-10080 минут (7 дней) для указания срока действия.", "PDF page size format": "Размер страницы PDF", "PDF page orientation": "Ориентация страницы PDF", "Top margin in millimeters": "Верхнее поле в миллиметрах", "Bottom margin in millimeters": "Нижнее поле в миллиметрах", "Left margin in millimeters": "Левое поле в миллиметрах", "Right margin in millimeters": "Правое поле в миллиметрах", "Whether to print background graphics and colors": "Напечатать ли фоновую графику и цвета", "Font size for header (e.g., \"9px\")": "Размер шрифта заголовка (например, \"9px\")", "Whether to display header and footer": "Показывать ли заголовок и нижний колонтитул", "Custom HTML content for header": "Пользовательский HTML контент для заголовка", "Custom HTML content for footer": "Пользовательский HTML контент для футера", "Scale factor for the PDF (0.1 to 2.0)": "Коэффициент масштаба для PDF (0,1-2.0)", "Time to wait before generating PDF (in milliseconds)": "Время ожидания перед созданием PDF (в миллисекундах)", "The URL of the webpage to convert to PDF": "URL веб-страницы для преобразования в PDF", "Time to wait for page to load before generating PDF (in milliseconds)": "Время ожидания загрузки страницы перед генерацией PDF (в миллисекундах)", "CSS selector to wait for before generating PDF (e.g., \".content-loaded\")": "CSS селектор подождет перед генерацией PDF (например, \".content-загружен\")", "Browser viewport width in pixels (default: 1920)": "Ширина просмотра браузера в пикселях (по умолчанию: 1920)", "Browser viewport height in pixels (default: 1080)": "Высота просмотра браузера в пикселях (по умолчанию: 1080)", "Capture the full scrollable page": "Захват полной прокручиваемой страницы", "Select a transaction reference to filter objects.": "Выберите ссылку на транзакцию для фильтрации объектов.", "Maximum number of objects to return (default: 300, max: 300)": "Максимальное количество возвращаемых объектов (по умолчанию: 300, макс: 300)", "Number of objects to skip for pagination (default: 0)": "Количество пропущенных объектов для пагинации (по умолчанию: 0)", "Filter objects by template ID (optional)": "Фильтровать объекты по ID шаблона (необязательно)", "Filter by specific transaction reference (optional)": "Фильтр по конкретной ссылке транзакции (опционально)", "Start date for filtering (YYYY-MM-DD format, optional)": "Дата начала фильтрации (формат YYYY-MM-DD, необязательно)", "End date for filtering (YYYY-MM-DD format, optional)": "Дата окончания фильтрации (формат YYYY-MM-DD, необязательно)", "Filter by external reference ID (meta field)": "Фильтр по внешнему идентификатору ссылки (meta поле)", "Authorization headers are injected automatically from your connection.": "Заголовки авторизации включаются автоматически из вашего соединения.", "A4": "A4", "A3": "A3", "A5": "A5", "Letter": "Буква", "Legal": "Юридическая информация", "Tabloid": "Таблоид", "Portrait": "Портрет", "Landscape": "Горизонтальный", "GET": "ПОЛУЧИТЬ", "POST": "ПОСТ", "PATCH": "ПАТЧ", "PUT": "ПОКУПИТЬ", "DELETE": "УДАЛИТЬ", "HEAD": "HEAD"}
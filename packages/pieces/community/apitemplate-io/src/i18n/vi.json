{"APITemplate.io": "APITemplate.io", "Region": "Region", "API Key": "API Key", "Select your preferred API region for better performance": "Select your preferred API region for better performance", "Your APITemplate.io API key": "Your APITemplate.io API key", "Default (Singapore)": "<PERSON><PERSON><PERSON> (Singapore)", "Europe (Frankfurt)": "Europe (Frankfurt)", "US East (N. Virginia)": "US East (N. Virginia)", "Australia (Sydney)": "Australia (Sydney)", "Alternative - Default (Singapore)": "Alternative - <PERSON><PERSON><PERSON> (Singapore)", "Alternative - Europe (Frankfurt)": "Alternative - Europe (Frankfurt)", "Alternative - US East (N. Virginia)": "Alternative - US East (N. Virginia)", "\nTo obtain your API key:\n1. Go to https://app.apitemplate.io/.\n2. Navigate to API Integration section.\n3. Copy your API key.\n\nSelect the region closest to your location for better performance.\n": "\nTo obtain your API key:\n1. Go to https://app.apitemplate.io/.\n2. Navigate to API Integration section.\n3. Copy your API key.\n\nSelect the region closest to your location for better performance.\n", "Create Image": "Create Image", "Create PDF From HTML": "Create PDF From HTML", "Create PDF From URL": "Create PDF From URL", "Create PDF": "Create PDF", "Delete Object": "Delete Object", "Get Account Information": "Get Account Information", "List Objects": "List Objects", "Custom API Call": "Custom API Call", "Creates an image from a template with provided data.": "Creates an image from a template with provided data.", "Creates a PDF from HTML.": "Creates a PDF from HTML.", "Creates a PDF from a webpage URL.": "Creates a PDF from a webpage URL.", "Creates a PDF from a template with provided data.": "Creates a PDF from a template with provided data.", "Deletes a generated PDF or image by its transaction reference or object ID.": "Deletes a generated PDF or image by its transaction reference or object ID.", "Retrieves account information including usage statistics and account details.": "Retrieves account information including usage statistics and account details.", "Retrieves a list of generated PDFs and images with optional filtering": "Retrieves a list of generated PDFs and images with optional filtering", "Make a custom API call to a specific endpoint": "Make a custom API call to a specific endpoint", "Template ID": "Template ID", "Template Data": "Template Data", "Generation Delay (ms)": "Generation Delay (ms)", "External Reference ID": "External Reference ID", "HTML Content": "HTML Content", "CSS Styles": "CSS Styles", "Data for Templating": "Data for Templating", "Expiration (minutes)": "Expiration (minutes)", "Page Size": "<PERSON><PERSON> trang", "Page Orientation": "Page Orientation", "Margin Top (mm)": "Margin Top (mm)", "Margin Bottom (mm)": "Margin Bottom (mm)", "Margin Left (mm)": "Margin Left (mm)", "Margin Right (mm)": "Margin Right (mm)", "Print Background": "Print Background", "Header Font Size": "Header <PERSON><PERSON>", "Display Header/Footer": "Display Header/Footer", "Custom Header HTML": "Custom Header HTML", "Custom Footer HTML": "Custom Footer HTML", "Scale": "Scale", "Wait Timeout (ms)": "Wait Timeout (ms)", "URL": "URL", "Wait for Selector": "Wait for Selector", "Viewport Width": "Viewport Width", "Viewport Height": "Viewport Height", "Full Page": "Full Page", "Transaction Reference": "Transaction Reference", "Limit": "Limit", "Offset": "Offset", "Date From": "Date From", "Date To": "Date To", "Meta Filter": "<PERSON><PERSON>", "Method": "Method", "Headers": "Headers", "Query Parameters": "Query Parameters", "Body": "Body", "No Error on Failure": "No Error on Failure", "Timeout (in seconds)": "Timeout (in seconds)", "JSON data with overrides array to populate the template. Format: {\"overrides\": [{\"name\": \"object_name\", \"property\": \"value\"}]}.": "JSON data with overrides array to populate the template. Format: {\"overrides\": [{\"name\": \"object_name\", \"property\": \"value\"}]}.", "Delay in milliseconds before PDF generation": "Delay in milliseconds before PDF generation", "Specify an external reference ID for your own reference": "Specify an external reference ID for your own reference", "The HTML content to convert to PDF. Can include CSS styles and external resources.": "The HTML content to convert to PDF. Can include CSS styles and external resources.", "Optional CSS styles to apply to the HTML content. Can include inline styles or external stylesheets.": "Optional CSS styles to apply to the HTML content. Can include inline styles or external stylesheets.", "Optional JSON data to use for templating the HTML content. Can include variables and dynamic content.": "Optional JSON data to use for templating the HTML content. Can include variables and dynamic content.", "Expiration of the generated PDF in minutes. Use 0 to store permanently, or 1-10080 minutes (7 days) to specify expiration.": "Expiration of the generated PDF in minutes. Use 0 to store permanently, or 1-10080 minutes (7 days) to specify expiration.", "PDF page size format": "PDF page size format", "PDF page orientation": "PDF page orientation", "Top margin in millimeters": "Top margin in millimeters", "Bottom margin in millimeters": "Bottom margin in millimeters", "Left margin in millimeters": "Left margin in millimeters", "Right margin in millimeters": "Right margin in millimeters", "Whether to print background graphics and colors": "Whether to print background graphics and colors", "Font size for header (e.g., \"9px\")": "Font size for header (e.g., \"9px\")", "Whether to display header and footer": "Whether to display header and footer", "Custom HTML content for header": "Custom HTML content for header", "Custom HTML content for footer": "Custom HTML content for footer", "Scale factor for the PDF (0.1 to 2.0)": "Scale factor for the PDF (0.1 to 2.0)", "Time to wait before generating PDF (in milliseconds)": "Time to wait before generating PDF (in milliseconds)", "The URL of the webpage to convert to PDF": "The URL of the webpage to convert to PDF", "Time to wait for page to load before generating PDF (in milliseconds)": "Time to wait for page to load before generating PDF (in milliseconds)", "CSS selector to wait for before generating PDF (e.g., \".content-loaded\")": "CSS selector to wait for before generating PDF (e.g., \".content-loaded\")", "Browser viewport width in pixels (default: 1920)": "Browser viewport width in pixels (default: 1920)", "Browser viewport height in pixels (default: 1080)": "Browser viewport height in pixels (default: 1080)", "Capture the full scrollable page": "Capture the full scrollable page", "Select a transaction reference to filter objects.": "Select a transaction reference to filter objects.", "Maximum number of objects to return (default: 300, max: 300)": "Maximum number of objects to return (default: 300, max: 300)", "Number of objects to skip for pagination (default: 0)": "Number of objects to skip for pagination (default: 0)", "Filter objects by template ID (optional)": "Filter objects by template ID (optional)", "Filter by specific transaction reference (optional)": "Filter by specific transaction reference (optional)", "Start date for filtering (YYYY-MM-DD format, optional)": "Start date for filtering (YYYY-MM-DD format, optional)", "End date for filtering (YYYY-MM-DD format, optional)": "End date for filtering (YYYY-MM-DD format, optional)", "Filter by external reference ID (meta field)": "Filter by external reference ID (meta field)", "Authorization headers are injected automatically from your connection.": "Authorization headers are injected automatically from your connection.", "A4": "A4", "A3": "A3", "A5": "A5", "Letter": "Letter", "Legal": "Legal", "Tabloid": "Tabloid", "Portrait": "Portrait", "Landscape": "Landscape", "GET": "GET", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "DELETE", "HEAD": "HEAD"}
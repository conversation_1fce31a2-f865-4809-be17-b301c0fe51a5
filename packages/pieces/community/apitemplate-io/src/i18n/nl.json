{"APITemplate.io": "APITemplate.io", "Region": "Regio", "API Key": "API Sleutel", "Select your preferred API region for better performance": "Selecteer uw gewenste API-regio voor betere prestaties", "Your APITemplate.io API key": "Uw APITemplate.io API-sleutel", "Default (Singapore)": "<PERSON><PERSON><PERSON> (Singapore)", "Europe (Frankfurt)": "Europa (Frankfurt)", "US East (N. Virginia)": "<PERSON><PERSON> (N. Virginia)", "Australia (Sydney)": "<PERSON><PERSON><PERSON><PERSON> (Sydney)", "Alternative - Default (Singapore)": "Alternatief - Standaard (Singapore)", "Alternative - Europe (Frankfurt)": "Alternatief - Europa (Frankfurt)", "Alternative - US East (N. Virginia)": "Alternatief - US Oost (N. Virginia)", "\nTo obtain your API key:\n1. Go to https://app.apitemplate.io/.\n2. Navigate to API Integration section.\n3. Copy your API key.\n\nSelect the region closest to your location for better performance.\n": "\nOm je API-sleutel te verkrijgen:\n1. Ga naar https://app.apitemplate.io/.\n2. Navigeer naar de API integratie sectie.\n3. Kopieer uw API-sleutel.\n\nSelecteer de regio die het dichtst bij uw locatie staat voor betere prestaties.\n", "Create Image": "Afbeelding aanmaken", "Create PDF From HTML": "PDF maken van HTML", "Create PDF From URL": "PDF maken vanaf URL", "Create PDF": "PDF maken", "Delete Object": "Object verwijderen", "Get Account Information": "Acco<PERSON><PERSON><PERSON><PERSON><PERSON>", "List Objects": "<PERSON>n objecten", "Custom API Call": "Custom API Call", "Creates an image from a template with provided data.": "<PERSON><PERSON>t een afbee<PERSON> van een s<PERSON><PERSON> met ve<PERSON><PERSON><PERSON> g<PERSON>.", "Creates a PDF from HTML.": "Maakt een PDF van HTML.", "Creates a PDF from a webpage URL.": "Maakt een PDF van een webpagina-URL.", "Creates a PDF from a template with provided data.": "Maakt een PDF van een template met verstrekte gegevens.", "Deletes a generated PDF or image by its transaction reference or object ID.": "Verwijdert een gegenereerde PDF of afbeelding met de transactiereferentie of object ID.", "Retrieves account information including usage statistics and account details.": "<PERSON><PERSON><PERSON> van accountinformatie inclusief gebruiksstatistieken en accountgegevens.", "Retrieves a list of generated PDFs and images with optional filtering": "<PERSON><PERSON><PERSON> li<PERSON> met gegenereerde PDF's en afbeeldingen met optionele filtering", "Make a custom API call to a specific endpoint": "Maak een aangepaste API call naar een specifiek eindpunt", "Template ID": "Sjabloon ID", "Template Data": "Sjabloon gegevens", "Generation Delay (ms)": "<PERSON><PERSON><PERSON> (ms)", "External Reference ID": "Externe referentie ID", "HTML Content": "HTML inhoud", "CSS Styles": "CSS Styles", "Data for Templating": "Data voor Templating", "Expiration (minutes)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (minuten)", "Page Size": "Paginagrootte", "Page Orientation": "<PERSON><PERSON><PERSON>", "Margin Top (mm)": "Marge boven (mm)", "Margin Bottom (mm)": "Marge onderkant (mm)", "Margin Left (mm)": "Rand links (mm)", "Margin Right (mm)": "Marge rechts (mm)", "Print Background": "Achtergrond afdrukken", "Header Font Size": "Header <PERSON><PERSON>", "Display Header/Footer": "Kop- en voettekst weergeven", "Custom Header HTML": "Aangepaste koptekst HTML", "Custom Footer HTML": "Aangepaste voettekst HTML", "Scale": "<PERSON><PERSON><PERSON>", "Wait Timeout (ms)": "Wacht time-out (ms)", "URL": "URL", "Wait for Selector": "Wacht op selector", "Viewport Width": "Weergave breedte", "Viewport Height": "Viewport hoogte", "Full Page": "Volledige pagina", "Transaction Reference": "Transactie referentie", "Limit": "<PERSON><PERSON>", "Offset": "<PERSON><PERSON><PERSON><PERSON>", "Date From": "<PERSON><PERSON>", "Date To": "<PERSON><PERSON> tot", "Meta Filter": "Meta filter", "Method": "<PERSON>e", "Headers": "Kopteksten", "Query Parameters": "Query parameters", "Body": "<PERSON><PERSON><PERSON>", "No Error on Failure": "<PERSON><PERSON> fout bij fout", "Timeout (in seconds)": "Time-out (in seconden)", "JSON data with overrides array to populate the template. Format: {\"overrides\": [{\"name\": \"object_name\", \"property\": \"value\"}]}.": "JSON-gegevens met overrides array om het sjabloon te vullen. Formaat: {\"overrides\": [{\"name\": \"object_name\", \"property\": \"value\"}}.", "Delay in milliseconds before PDF generation": "Vertraging in milliseconden voor PDF-generatie", "Specify an external reference ID for your own reference": "<PERSON><PERSON> een extern referentie-ID op voor uw eigen referentie", "The HTML content to convert to PDF. Can include CSS styles and external resources.": "De HTML-inhoud om te converteren naar PDF. Kan CSS-stijlen en externe bronnen bevatten.", "Optional CSS styles to apply to the HTML content. Can include inline styles or external stylesheets.": "Optionele CSS stijlen om toe te passen op de HTML inhoud. Kan inline stijlen of externe stylesheets bevatten.", "Optional JSON data to use for templating the HTML content. Can include variables and dynamic content.": "Optionele JSON-gegevens om de HTML-inhoud te templen. Kan variabelen en dynamische inhoud bevatten.", "Expiration of the generated PDF in minutes. Use 0 to store permanently, or 1-10080 minutes (7 days) to specify expiration.": "Vervaldatum van de gegenereerde PDF in minuten. Gebruik 0 om permanent op te slaan, of 1-10080 minuten (7 dagen) om de vervaldatum op te geven.", "PDF page size format": "PDF paginagrootte formaat", "PDF page orientation": "PDF pagina oriëntatie", "Top margin in millimeters": "Hoogste marge in millimeters", "Bottom margin in millimeters": "Onderste marge in millimeters", "Left margin in millimeters": "Linker marge in millimeters", "Right margin in millimeters": "Rechter marge in millimeters", "Whether to print background graphics and colors": "Afdrukken van achtergrond afbeeldingen en kleuren", "Font size for header (e.g., \"9px\")": "Tekengrootte voor header (bijv. \"9px\")", "Whether to display header and footer": "Of de voettekst en koptekst getoond worden", "Custom HTML content for header": "Aangepaste HTML-inhoud voor koptekst", "Custom HTML content for footer": "Aangepaste HTML-inhoud voor voettekst", "Scale factor for the PDF (0.1 to 2.0)": "Schaalfactor voor de PDF (0.1 tot 2.0)", "Time to wait before generating PDF (in milliseconds)": "Wachttijd voordat PDF wordt gegenereerd (in milliseconden)", "The URL of the webpage to convert to PDF": "De URL van de webpagina om te converteren naar PDF", "Time to wait for page to load before generating PDF (in milliseconds)": "Tijd om te wachten tot de pagina te laden voordat de PDF wordt gegenereerd (in milliseconden)", "CSS selector to wait for before generating PDF (e.g., \".content-loaded\")": "CSS-selector die wacht voor het genereren van PDF (bijv. \".content-loaded\")", "Browser viewport width in pixels (default: 1920)": "Browserviewport breedte in pixels (standaard: 1920)", "Browser viewport height in pixels (default: 1080)": "Browser viewport hoogte in pixels (standaard: 1080)", "Capture the full scrollable page": "Sla de volledige scrollbare pagina op", "Select a transaction reference to filter objects.": "Selecteer een transactiereferentie om objecten te filteren.", "Maximum number of objects to return (default: 300, max: 300)": "Maximum aantal te retourneren objecten (standaard: 300, max: 300)", "Number of objects to skip for pagination (default: 0)": "Aantal objecten om over te slaan voor paginering (standaard: 0)", "Filter objects by template ID (optional)": "Objecten filteren op template ID (optioneel)", "Filter by specific transaction reference (optional)": "Filter op specifieke transactie referentie (optioneel)", "Start date for filtering (YYYY-MM-DD format, optional)": "Startdatum voor het filteren (JJJJ-MM-DD formaat, optioneel)", "End date for filtering (YYYY-MM-DD format, optional)": "Einddatum voor het filteren (JJJJ-MM-DD formaat, optioneel)", "Filter by external reference ID (meta field)": "Filter op extern referentie-ID (meta veld)", "Authorization headers are injected automatically from your connection.": "Autorisatie headers worden automatisch geïnjecteerd vanuit uw verbinding.", "A4": "A4", "A3": "A3", "A5": "A5", "Letter": "Brief", "Legal": "<PERSON><PERSON><PERSON>", "Tabloid": "Ta<PERSON><PERSON><PERSON><PERSON>", "Portrait": "<PERSON><PERSON><PERSON>", "Landscape": "Liggend", "GET": "KRIJG", "POST": "POSTE", "PATCH": "BEKIJK", "PUT": "PUT", "DELETE": "VERWIJDEREN", "HEAD": "HOOFD"}
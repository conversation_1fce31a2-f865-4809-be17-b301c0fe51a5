{"APITemplate.io": "APITemplate.io", "Region": "地域", "API Key": "API キー", "Select your preferred API region for better performance": "パフォーマンスを向上させるために、ご希望の API リージョンを選択してください", "Your APITemplate.io API key": "APITemplate.io APIキー", "Default (Singapore)": "デフォルト (シンガポール)", "Europe (Frankfurt)": "ヨーロッパ (フランクフルト)", "US East (N. Virginia)": "US East (バージニア州)", "Australia (Sydney)": "オーストラリア (シドニー)", "Alternative - Default (Singapore)": "Alternative - デフォルト (シンガポール)", "Alternative - Europe (Frankfurt)": "Alternative - Europe(フランクフルト)", "Alternative - US East (N. Virginia)": "代替 - 米国東部(バージニア州)", "\nTo obtain your API key:\n1. Go to https://app.apitemplate.io/.\n2. Navigate to API Integration section.\n3. Copy your API key.\n\nSelect the region closest to your location for better performance.\n": "\nAPIキーを取得するには:\n1. https://app.apitemplate.io/.\n2. API統合セクションに移動します。\n3. APIキーをコピーします。\n\nパフォーマンスを向上させるために、あなたの場所に最も近い地域を選択します。\n", "Create Image": "画像を作成", "Create PDF From HTML": "HTMLからPDFを作成", "Create PDF From URL": "URLからPDFを作成", "Create PDF": "PDFを作成", "Delete Object": "オブジェクトを削除", "Get Account Information": "アカウント情報を取得", "List Objects": "オブジェクトの一覧", "Custom API Call": "カスタムAPI通話", "Creates an image from a template with provided data.": "指定されたデータでテンプレートから画像を作成します。", "Creates a PDF from HTML.": "HTML から PDF を作成します。", "Creates a PDF from a webpage URL.": "Web ページの URL から PDF を作成します。", "Creates a PDF from a template with provided data.": "指定されたデータを含むテンプレートからPDFを作成します。", "Deletes a generated PDF or image by its transaction reference or object ID.": "トランザクション参照またはオブジェクト ID によって生成された PDF または画像を削除します。", "Retrieves account information including usage statistics and account details.": "使用状況の統計とアカウントの詳細を含むアカウント情報を取得します。", "Retrieves a list of generated PDFs and images with optional filtering": "オプションのフィルタリングで生成されたPDFと画像のリストを取得します", "Make a custom API call to a specific endpoint": "特定のエンドポイントへのカスタム API コールを実行します。", "Template ID": "テンプレートID", "Template Data": "テンプレートデータ", "Generation Delay (ms)": "生成遅延 (ミリ秒)", "External Reference ID": "外部参照ID", "HTML Content": "HTML コンテンツ", "CSS Styles": "CSS Styles", "Data for Templating": "テンプレートのデータ", "Expiration (minutes)": "有効期限 (分)", "Page Size": "ページサイズ", "Page Orientation": "ページの向き", "Margin Top (mm)": "マージントップ（mm）", "Margin Bottom (mm)": "下部証拠金 (mm)", "Margin Left (mm)": "左マージン（mm）", "Margin Right (mm)": "右マージン（mm）", "Print Background": "背景を印刷", "Header Font Size": "Header <PERSON><PERSON>", "Display Header/Footer": "ヘッダー/フッター", "Custom Header HTML": "カスタムヘッダーHTML", "Custom Footer HTML": "カスタムフッターHTML", "Scale": "拡大縮小", "Wait Timeout (ms)": "待機タイムアウト (ms)", "URL": "URL", "Wait for Selector": "セレクターを待つ", "Viewport Width": "ビューポートの幅", "Viewport Height": "ビューポートの高さ", "Full Page": "全ページ", "Transaction Reference": "取引の参照", "Limit": "制限", "Offset": "オフセット", "Date From": "開始日", "Date To": "終了日", "Meta Filter": "メタフィルタ", "Method": "方法", "Headers": "ヘッダー", "Query Parameters": "クエリパラメータ", "Body": "本文", "No Error on Failure": "失敗時にエラーはありません", "Timeout (in seconds)": "タイムアウト（秒）", "JSON data with overrides array to populate the template. Format: {\"overrides\": [{\"name\": \"object_name\", \"property\": \"value\"}]}.": "テンプレートを追加するための配列をオーバーライドするJSONデータ。フォーマット: {\"overrides\": [{\"name\": \"object_name\", \"property\": \"value\"}]}.", "Delay in milliseconds before PDF generation": "PDF生成までの遅延時間", "Specify an external reference ID for your own reference": "外部参照IDを指定してください", "The HTML content to convert to PDF. Can include CSS styles and external resources.": "PDFに変換するHTMLコンテンツ。CSSスタイルと外部リソースを含めることができます。", "Optional CSS styles to apply to the HTML content. Can include inline styles or external stylesheets.": "HTML コンテンツに適用する任意の CSS スタイル。インラインスタイルまたは外部スタイルシートを含めることができます。", "Optional JSON data to use for templating the HTML content. Can include variables and dynamic content.": "HTMLコンテンツのテンプレートに使用する任意のJSONデータ。変数と動的コンテンツを含めることができます。", "Expiration of the generated PDF in minutes. Use 0 to store permanently, or 1-10080 minutes (7 days) to specify expiration.": "生成されたPDFの有効期限を分単位で指定します。0を指定すると永久保存、または1-10080分（7日）を指定します。", "PDF page size format": "PDF ページサイズフォーマット", "PDF page orientation": "PDF ページの向き", "Top margin in millimeters": "ミリメートル単位の上マージン数", "Bottom margin in millimeters": "下マージン（ミリメートル単位）", "Left margin in millimeters": "ミリメートル単位の左マージン。", "Right margin in millimeters": "ミリメートル単位の右マージンです。", "Whether to print background graphics and colors": "背景画像と色を印刷するかどうか", "Font size for header (e.g., \"9px\")": "ヘッダのフォントサイズ（例：\"9px\"）", "Whether to display header and footer": "ヘッダーとフッターを表示する", "Custom HTML content for header": "ヘッダーのHTMLコンテンツ", "Custom HTML content for footer": "フッター用のカスタムHTMLコンテンツ", "Scale factor for the PDF (0.1 to 2.0)": "PDFのスケール係数 (0.1 から 2.0)", "Time to wait before generating PDF (in milliseconds)": "PDF生成までの待機時間 (ミリ秒)", "The URL of the webpage to convert to PDF": "PDFに変換するウェブページのURL", "Time to wait for page to load before generating PDF (in milliseconds)": "PDFを生成する前にページの読み込みを待つ時間 (ミリ秒)", "CSS selector to wait for before generating PDF (e.g., \".content-loaded\")": "PDFを生成する前に待機するCSSセレクタ（例：\".content-loaded\")", "Browser viewport width in pixels (default: 1920)": "ブラウザービューポートの幅（ピクセル単位）（デフォルト：1920）", "Browser viewport height in pixels (default: 1080)": "ブラウザのビューポートの高さ（デフォルト：1080）", "Capture the full scrollable page": "スクロール可能な全ページをキャプチャする", "Select a transaction reference to filter objects.": "フィルタオブジェクトのトランザクション参照を選択します。", "Maximum number of objects to return (default: 300, max: 300)": "返すオブジェクトの最大数（デフォルト：300、最大：300）", "Number of objects to skip for pagination (default: 0)": "ページネーションをスキップするオブジェクトの数 (デフォルト: 0)", "Filter objects by template ID (optional)": "テンプレート ID でオブジェクトを絞り込みます (オプション)", "Filter by specific transaction reference (optional)": "特定のトランザクション参照でフィルター (オプション)", "Start date for filtering (YYYY-MM-DD format, optional)": "フィルタの開始日 (YYYY-MM-DD形式、任意)", "End date for filtering (YYYY-MM-DD format, optional)": "フィルタリングの終了日 (YYYY-MM-DD形式、任意)", "Filter by external reference ID (meta field)": "外部参照IDでフィルター (メタフィールド)", "Authorization headers are injected automatically from your connection.": "認証ヘッダは接続から自動的に注入されます。", "A4": "A4", "A3": "A3", "A5": "A5", "Letter": "手紙", "Legal": "Legal", "Tabloid": "タブロイド", "Portrait": "縦向き", "Landscape": "ランドスケープ", "GET": "取得", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "削除", "HEAD": "頭"}
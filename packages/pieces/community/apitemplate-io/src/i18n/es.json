{"APITemplate.io": "APITemplate.io", "Region": "Región", "API Key": "Clave API", "Select your preferred API region for better performance": "Seleccione su región API preferida para un mejor rendimiento", "Your APITemplate.io API key": "Su clave API APITemplate.io", "Default (Singapore)": "<PERSON><PERSON> (Singapore)", "Europe (Frankfurt)": "Europa (�)", "US East (N. Virginia)": "EEUU <PERSON>ste (N. Virginia)", "Australia (Sydney)": "Australia (Sydney)", "Alternative - Default (Singapore)": "Alternativo - Predeterminado (Singapore)", "Alternative - Europe (Frankfurt)": "Alternativo - Europa (named@@0)", "Alternative - US East (N. Virginia)": "Alternativo - EEUU Este (N. Virginia)", "\nTo obtain your API key:\n1. Go to https://app.apitemplate.io/.\n2. Navigate to API Integration section.\n3. Copy your API key.\n\nSelect the region closest to your location for better performance.\n": "\nTo obtain your API key:\n1. Go to https://app.apitemplate.io/.\n2. Navigate to API Integration section.\n3. Copy your API key.\n\nSelect the region closest to your location for better performance.\n", "Create Image": "<PERSON><PERSON><PERSON> imagen", "Create PDF From HTML": "Crear PDF desde HTML", "Create PDF From URL": "Crear PDF desde URL", "Create PDF": "Crear PDF", "Delete Object": "Eliminar objeto", "Get Account Information": "Obtener información de la cuenta", "List Objects": "Lista de objetos", "Custom API Call": "Llamada API personalizada", "Creates an image from a template with provided data.": "Crea una imagen desde una plantilla con los datos proporcionados.", "Creates a PDF from HTML.": "Crea un PDF desde HTML.", "Creates a PDF from a webpage URL.": "Crea un PDF desde una URL de página web.", "Creates a PDF from a template with provided data.": "Crea un PDF desde una plantilla con los datos proporcionados.", "Deletes a generated PDF or image by its transaction reference or object ID.": "Elimina un PDF o imagen generado por su referencia de transacción u objeto ID.", "Retrieves account information including usage statistics and account details.": "Recuperar información de la cuenta, incluyendo estadísticas de uso y detalles de la cuenta.", "Retrieves a list of generated PDFs and images with optional filtering": "Recuperar una lista de PDFs e imágenes generadas con filtrado opcional", "Make a custom API call to a specific endpoint": "Hacer una llamada API personalizada a un extremo específico", "Template ID": "ID de plantilla", "Template Data": "Datos de plantilla", "Generation Delay (ms)": "Retraso de generación (ms)", "External Reference ID": "ID de referencia externa", "HTML Content": "Contenido HTML", "CSS Styles": "CSS Styles", "Data for Templating": "Datos de Plantilla", "Expiration (minutes)": "Caducidad (minutos)", "Page Size": "Tamaño de página", "Page Orientation": "Orientación de página", "Margin Top (mm)": "Margen superior (mm)", "Margin Bottom (mm)": "Margen Inferior (mm)", "Margin Left (mm)": "<PERSON>gen izquierdo (mm)", "Margin Right (mm)": "Margen Derecha (mm)", "Print Background": "Imprimir fondo", "Header Font Size": "Header <PERSON><PERSON>", "Display Header/Footer": "Mostrar cabeza/pie de página", "Custom Header HTML": "HTML cabecera personalizada", "Custom Footer HTML": "HTML del pie de página personalizado", "Scale": "Escala", "Wait Timeout (ms)": "<PERSON><PERSON><PERSON> tiempo agotado (ms)", "URL": "URL", "Wait for Selector": "<PERSON><PERSON><PERSON> al <PERSON>", "Viewport Width": "<PERSON><PERSON> de vista", "Viewport Height": "Vista Altura", "Full Page": "Página completa", "Transaction Reference": "Referencia de la transacción", "Limit": "Límite", "Offset": "Desplazamiento", "Date From": "<PERSON><PERSON>", "Date To": "<PERSON><PERSON> has<PERSON>", "Meta Filter": "<PERSON><PERSON>", "Method": "<PERSON><PERSON><PERSON><PERSON>", "Headers": "Encabezados", "Query Parameters": "Parámetros de consulta", "Body": "<PERSON><PERSON><PERSON>", "No Error on Failure": "No hay ningún error en fallo", "Timeout (in seconds)": "Tiempo de espera (en segundos)", "JSON data with overrides array to populate the template. Format: {\"overrides\": [{\"name\": \"object_name\", \"property\": \"value\"}]}.": "Datos JSON con matriz de sobreescritura para rellenar la plantilla. Formato: {\"overrides\": [{\"name\": \"object_name\", \"property\": \"value\"}]}.", "Delay in milliseconds before PDF generation": "Retraso en milisegundos antes de la generación de PDF", "Specify an external reference ID for your own reference": "Especifique un ID de referencia externa para su propia referencia", "The HTML content to convert to PDF. Can include CSS styles and external resources.": "El contenido HTML para convertir a PDF. Puede incluir estilos CSS y recursos externos.", "Optional CSS styles to apply to the HTML content. Can include inline styles or external stylesheets.": "Estilos CSS opcionales para aplicar al contenido HTML. Puede incluir estilos en línea o hojas de estilo externas.", "Optional JSON data to use for templating the HTML content. Can include variables and dynamic content.": "Datos JSON opcionales a usar para plantillas del contenido HTML. Puede incluir variables y contenido dinámico.", "Expiration of the generated PDF in minutes. Use 0 to store permanently, or 1-10080 minutes (7 days) to specify expiration.": "Expiración del PDF generado en minutos. Utilice 0 para almacenar permanentemente, o 1-10080 minutos (7 días) para especificar la caducidad.", "PDF page size format": "Formato de tamaño de página PDF", "PDF page orientation": "Orientación de la página PDF", "Top margin in millimeters": "Margen superior en milímetros", "Bottom margin in millimeters": "Margen inicial en milímetros", "Left margin in millimeters": "Margen izquierdo en milímetros", "Right margin in millimeters": "Margen derecho en milímetros", "Whether to print background graphics and colors": "Imprimir gráficos de fondo y colores", "Font size for header (e.g., \"9px\")": "Tamaño de fuente para la cabecera (por ej., \"9px\")", "Whether to display header and footer": "Mostrar cabecera y pie de página", "Custom HTML content for header": "Contenido HTML personalizado para el encabezado", "Custom HTML content for footer": "Contenido HTML personalizado para pie de página", "Scale factor for the PDF (0.1 to 2.0)": "Factor de escala para el PDF (0.1 a 2.0)", "Time to wait before generating PDF (in milliseconds)": "Tiempo de espera antes de generar PDF (en milisegundos)", "The URL of the webpage to convert to PDF": "La URL de la página web para convertir a PDF", "Time to wait for page to load before generating PDF (in milliseconds)": "Tiempo de espera para cargar la página antes de generar PDF (en milisegundos)", "CSS selector to wait for before generating PDF (e.g., \".content-loaded\")": "Selector CSS a esperar antes de generar PDF (por ejemplo, \".content-loaded\")", "Browser viewport width in pixels (default: 1920)": "<PERSON><PERSON> de la vista del navegador en píxeles (por defecto: 1920)", "Browser viewport height in pixels (default: 1080)": "Altura de la vista del navegador en píxeles (por defecto: 1080)", "Capture the full scrollable page": "Capturar la página desplegable", "Select a transaction reference to filter objects.": "Seleccione una referencia de transacción para filtrar objetos.", "Maximum number of objects to return (default: 300, max: 300)": "Número máximo de objetos a devolver (por defecto: 300, máximo: 300)", "Number of objects to skip for pagination (default: 0)": "Número de objetos a omitir para la paginación (por defecto: 0)", "Filter objects by template ID (optional)": "Filtrar objetos por ID de plantilla (opcional)", "Filter by specific transaction reference (optional)": "Filtrar por referencia de transacción específica (opcional)", "Start date for filtering (YYYY-MM-DD format, optional)": "Fecha de inicio para el filtrado (formato AAA-MM-DD, opcional)", "End date for filtering (YYYY-MM-DD format, optional)": "Fecha de fin del filtrado (formato AAA-MM-DD, opcional)", "Filter by external reference ID (meta field)": "Filtrar por ID de referencia externa (meta campo)", "Authorization headers are injected automatically from your connection.": "Las cabeceras de autorización se inyectan automáticamente desde tu conexión.", "A4": "R4", "A3": "R3", "A5": "R5", "Letter": "Letra", "Legal": "Legal", "Tabloid": "Tabloide", "Portrait": "Retrato", "Landscape": "<PERSON><PERSON><PERSON>", "GET": "RECOGER", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "BORRAR", "HEAD": "LIMPIO"}
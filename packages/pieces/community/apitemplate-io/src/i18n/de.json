{"APITemplate.io": "APITemplate.io", "Region": "Region", "API Key": "API-Schlüssel", "Select your preferred API region for better performance": "Wählen Sie Ihre bevorzugte API-Region für eine bessere Leistung", "Your APITemplate.io API key": "Ihr APITemplate.io API-Schlüssel", "Default (Singapore)": "Standard (Singapore)", "Europe (Frankfurt)": "Europa (Frankfurt)", "US East (N. Virginia)": "US-<PERSON><PERSON> (N. Virginia)", "Australia (Sydney)": "<PERSON><PERSON><PERSON><PERSON> (Sydney)", "Alternative - Default (Singapore)": "Alternative - Standard (Singapore)", "Alternative - Europe (Frankfurt)": "Alternative - Europa (Frankfurt)", "Alternative - US East (N. Virginia)": "Alternative - US East (N. Virginia)", "\nTo obtain your API key:\n1. Go to https://app.apitemplate.io/.\n2. Navigate to API Integration section.\n3. Copy your API key.\n\nSelect the region closest to your location for better performance.\n": "\nUm Ihren API-Schlüssel zu erhalten:\n1. <PERSON><PERSON><PERSON> zu https://app.apitemplate.io/.\n2. Navigiere zur API-Integration.\n3. Kopieren Sie Ihren API-Schlüssel.\n\nWählen Sie die Region aus, die Ihrem Standort am nächsten ist, um die Leistung zu verbessern.\n", "Create Image": "Bild er<PERSON>", "Create PDF From HTML": "PDF aus HTML erstellen", "Create PDF From URL": "<PERSON> von URL erstellen", "Create PDF": "PDF erstellen", "Delete Object": "Objekt löschen", "Get Account Information": "Kontoinformationen abrufen", "List Objects": "Listen-Obje<PERSON><PERSON>", "Custom API Call": "Eigener API-Aufruf", "Creates an image from a template with provided data.": "Erst<PERSON>t ein Bild aus einer Vorlage mit den angegebenen Daten.", "Creates a PDF from HTML.": "Erstellt ein PDF aus HTML.", "Creates a PDF from a webpage URL.": "Erstellt ein PDF von einer Webseiten-URL.", "Creates a PDF from a template with provided data.": "Erstellt ein PDF aus einer Vorlage mit den bereitgestellten Daten.", "Deletes a generated PDF or image by its transaction reference or object ID.": "Löscht eine generierte PDF oder ein Bild durch die Transaktionsreferenz oder Objekt-ID.", "Retrieves account information including usage statistics and account details.": "Ruft Kontoinformationen einschließlich Nutzungsstatistiken und Kontodetails ab.", "Retrieves a list of generated PDFs and images with optional filtering": "Ruft eine Liste der generierten PDFs und Bilder mit optionaler Filterung ab", "Make a custom API call to a specific endpoint": "Einen benutzerdefinierten API-Aufruf an einen bestimmten Endpunkt machen", "Template ID": "Template-ID", "Template Data": "Vorlagendaten", "Generation Delay (ms)": "Generationsverzögerung (ms)", "External Reference ID": "Externe Referenz-ID", "HTML Content": "HTML-Inhalt", "CSS Styles": "CSS Styles", "Data for Templating": "Daten für Templates", "Expiration (minutes)": "<PERSON><PERSON><PERSON> (Minuten)", "Page Size": "Einträge pro Seite", "Page Orientation": "Seitenausrichtung", "Margin Top (mm)": "Abstand oben (mm)", "Margin Bottom (mm)": "Rand unten (mm)", "Margin Left (mm)": "Rand links (mm)", "Margin Right (mm)": "Rand rechts (mm)", "Print Background": "<PERSON><PERSON><PERSON><PERSON><PERSON> drucken", "Header Font Size": "Header <PERSON><PERSON>", "Display Header/Footer": "Header/Fußzeile anzeigen", "Custom Header HTML": "<PERSON><PERSON>ner Header HTML", "Custom Footer HTML": "Eigener Footer HTML", "Scale": "Maßstab", "Wait Timeout (ms)": "Wartezeit (ms)", "URL": "URL", "Wait for Selector": "Warte auf Auswahl", "Viewport Width": "Viewport-Breite", "Viewport Height": "Viewport Höhe", "Full Page": "Vollseite", "Transaction Reference": "Transaktionsreferenz", "Limit": "Limit", "Offset": "Versatz", "Date From": "<PERSON><PERSON> ab", "Date To": "Datum bis", "Meta Filter": "Meta-<PERSON>lter", "Method": "<PERSON>e", "Headers": "Kopfzeilen", "Query Parameters": "Abfrageparameter", "Body": "<PERSON><PERSON><PERSON><PERSON>", "No Error on Failure": "<PERSON><PERSON> bei <PERSON>hler", "Timeout (in seconds)": "Timeout (in Sekunden)", "JSON data with overrides array to populate the template. Format: {\"overrides\": [{\"name\": \"object_name\", \"property\": \"value\"}]}.": "JSON-Daten mit überschreibt Array, um die Vorlage zu füllen. Format: {\"overrides\": [{\"name\": \"object_name\", \"property\": \"value\"}]}.", "Delay in milliseconds before PDF generation": "Verzögerung von Millisekunden vor der PDF-Generierung", "Specify an external reference ID for your own reference": "Geben Sie eine externe Referenz-ID für Ihre eigene Referenz an", "The HTML content to convert to PDF. Can include CSS styles and external resources.": "Der HTML-Inhalt zum Konvertieren in PDF. Kann CSS-Stile und externe Ressourcen enthalten.", "Optional CSS styles to apply to the HTML content. Can include inline styles or external stylesheets.": "Optionale CSS-Styles für den HTML-Inhalt. Kann Inline-Styles oder externe Stylesheets enthalten.", "Optional JSON data to use for templating the HTML content. Can include variables and dynamic content.": "Optionale JSON-Daten zur Vorlage des HTML-Inhalts. Kann Variablen und dynamische Inhalte enthalten.", "Expiration of the generated PDF in minutes. Use 0 to store permanently, or 1-10080 minutes (7 days) to specify expiration.": "Ablauf der generierten PDF in Minuten. Verwenden Sie 0 um dauerhaft zu speichern, oder 1-10080 Minuten (7 Tage), um Ablaufdatum anzugeben.", "PDF page size format": "PDF-Seitengrößenformat", "PDF page orientation": "PDF-Seitenausrichtung", "Top margin in millimeters": "Oberer Abstand in Millimetern", "Bottom margin in millimeters": "Unterer Rand in Millimetern", "Left margin in millimeters": "Linker Rand in Millimetern", "Right margin in millimeters": "Rechter Abstand in Millimetern", "Whether to print background graphics and colors": "Ob Hintergrundgrafiken und Farben gedruckt werden sollen", "Font size for header (e.g., \"9px\")": "Schriftgröße für Kopfzeile (z. B. \"9px\")", "Whether to display header and footer": "<PERSON><PERSON><PERSON> und Fußzeile an", "Custom HTML content for header": "Eigener HTML-Inhalt für Kopfzeile", "Custom HTML content for footer": "Eigener HTML-Inhalt für Fußzeile", "Scale factor for the PDF (0.1 to 2.0)": "Skalierungsfaktor für das PDF (0.1 bis 2.0)", "Time to wait before generating PDF (in milliseconds)": "Wartezeit, bevor PDF generiert wird (in Millisekunden)", "The URL of the webpage to convert to PDF": "Die URL der zu konvertierenden Webseite", "Time to wait for page to load before generating PDF (in milliseconds)": "Wartezeit bis Seite geladen wird, bevor PDF generiert wird (in Millisekunden)", "CSS selector to wait for before generating PDF (e.g., \".content-loaded\")": "CSS-<PERSON><PERSON><PERSON><PERSON>, auf den gewartet wird, bevor PDF generiert wird (z.B. \".content loaded\")", "Browser viewport width in pixels (default: 1920)": "Breite des Browser-Viewport in Pixeln (Standard: 1920)", "Browser viewport height in pixels (default: 1080)": "Höhe des Browser-Viewport in Pixeln (Standard: 1080)", "Capture the full scrollable page": "Erfassen Sie die vollständige scrollbare Seite", "Select a transaction reference to filter objects.": "<PERSON>ählen Sie eine Transaktionsreferenz aus, um Objekte zu filtern.", "Maximum number of objects to return (default: 300, max: 300)": "Maximale Anzahl der zurückzugebenden Objekte (Standard: 300, max: 300)", "Number of objects to skip for pagination (default: 0)": "<PERSON><PERSON><PERSON> zu überspringender Objekte (Standard: 0)", "Filter objects by template ID (optional)": "Objekte nach Template-ID filtern (optional)", "Filter by specific transaction reference (optional)": "Nach einer bestimmten Transaktionsreferenz filtern (optional)", "Start date for filtering (YYYY-MM-DD format, optional)": "Startdatum für die Filterung (JJJJJ-MM-TT Format, optional)", "End date for filtering (YYYY-MM-DD format, optional)": "Enddatum für die Filterung (JJJJ-MM-TT Format, optional)", "Filter by external reference ID (meta field)": "Nach externer Referenz-ID filtern (Meta-Feld)", "Authorization headers are injected automatically from your connection.": "Autorisierungs-Header werden automatisch von Ihrer Verbindung injiziert.", "A4": "A4", "A3": "A3", "A5": "A5", "Letter": "Brief", "Legal": "<PERSON><PERSON><PERSON>", "Tabloid": "Tabloid", "Portrait": "Hochformat", "Landscape": "Querformat", "GET": "ERHALTEN", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "LÖSCHEN", "HEAD": "HEAD"}
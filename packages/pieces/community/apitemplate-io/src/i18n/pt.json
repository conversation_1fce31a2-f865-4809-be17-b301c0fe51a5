{"APITemplate.io": "APITemplate.io", "Region": "Região", "API Key": "Chave de <PERSON>", "Select your preferred API region for better performance": "Selecione a região da API preferida para um melhor desempenho", "Your APITemplate.io API key": "Sua chave de API APIT", "Default (Singapore)": "<PERSON><PERSON><PERSON> (Singapore)", "Europe (Frankfurt)": "Europa (Frankfurt)", "US East (N. Virginia)": "Leste dos EUA (N. Virgínia)", "Australia (Sydney)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Sydney)", "Alternative - Default (Singapore)": "Alternativo - Padrão (Singapore)", "Alternative - Europe (Frankfurt)": "Alternativa - Europa (Frankfurt)", "Alternative - US East (N. Virginia)": "Alternativo - Leste dos EUA (N. Virgínia)", "\nTo obtain your API key:\n1. Go to https://app.apitemplate.io/.\n2. Navigate to API Integration section.\n3. Copy your API key.\n\nSelect the region closest to your location for better performance.\n": "\nPara obter a sua chave de API:\n1. Acesse https://app.apitemplate.io/.\n2. Navegue até a seção de Integração API.\n3. Copie sua chave de API.\n\nSelecione a região mais próxima de seu local para um melhor desempenho.\n", "Create Image": "<PERSON><PERSON><PERSON> imagem", "Create PDF From HTML": "Criar PDF de HTML", "Create PDF From URL": "Criar PDF de URL", "Create PDF": "Criar PDF", "Delete Object": "Excluir Objeto", "Get Account Information": "Obter informações da conta", "List Objects": "Lista de Objetos", "Custom API Call": "Chamada de API personalizada", "Creates an image from a template with provided data.": "Cria uma imagem a partir de um modelo com dados fornecidos.", "Creates a PDF from HTML.": "Cria um PDF do HTML.", "Creates a PDF from a webpage URL.": "Cria um PDF de uma URL da página.", "Creates a PDF from a template with provided data.": "Cria um PDF a partir de um modelo com dados fornecidos.", "Deletes a generated PDF or image by its transaction reference or object ID.": "Deleta um PDF ou imagem gerado por sua referência de transação ou ID do objeto.", "Retrieves account information including usage statistics and account details.": "Obtém informações da conta incluindo estatísticas de uso e detalhes da conta.", "Retrieves a list of generated PDFs and images with optional filtering": "Recupera uma lista de PDFs gerados e imagens com filtragem opcional", "Make a custom API call to a specific endpoint": "Faça uma chamada de API personalizada para um ponto de extremidade específico", "Template ID": "ID do modelo", "Template Data": "Dados do modelo", "Generation Delay (ms)": "Atraso de geração (ms)", "External Reference ID": "Referência externa de ID", "HTML Content": "Conteúdo HTML", "CSS Styles": "CSS Styles", "Data for Templating": "Dados para modelo", "Expiration (minutes)": "Expiração (minutos)", "Page Size": "<PERSON><PERSON><PERSON>", "Page Orientation": "Orientação da página", "Margin Top (mm)": "Margem Superior (mm)", "Margin Bottom (mm)": "Margem inferior (mm)", "Margin Left (mm)": "Margem esquerda (mm)", "Margin Right (mm)": "Margem direita (mm)", "Print Background": "Fundo de Impressão", "Header Font Size": "Header <PERSON><PERSON>", "Display Header/Footer": "Mostrar C<PERSON>lho/Rodapé", "Custom Header HTML": "HTML de cabeçalho personalizado", "Custom Footer HTML": "HTML de rodapé personalizado", "Scale": "Escala", "Wait Timeout (ms)": "Tempo limite de espera (ms)", "URL": "URL:", "Wait for Selector": "Aguardar o Seletor", "Viewport Width": "Largura da vista", "Viewport Height": "Altura da vista", "Full Page": "Página inteira", "Transaction Reference": "Referência de transação", "Limit": "Limitar", "Offset": "Deslocamento", "Date From": "Data de origem", "Date To": "Data Final", "Meta Filter": "Filtro de Meta", "Method": "<PERSON><PERSON><PERSON><PERSON>", "Headers": "Cabeçalhos", "Query Parameters": "Parâ<PERSON><PERSON> da consult<PERSON>", "Body": "<PERSON><PERSON><PERSON><PERSON>", "No Error on Failure": "Nenhum erro no Failure", "Timeout (in seconds)": "Tempo limite (em segundos)", "JSON data with overrides array to populate the template. Format: {\"overrides\": [{\"name\": \"object_name\", \"property\": \"value\"}]}.": "Dados JSON com matriz de substituições para preencher o template. Formato: {\"overrides\": [{\"name\": \"object_name\", \"property\": \"value\"}]}.", "Delay in milliseconds before PDF generation": "Atraso em milissegundos antes da geração de PDF", "Specify an external reference ID for your own reference": "Especifique um ID de referência externo para sua própria referência", "The HTML content to convert to PDF. Can include CSS styles and external resources.": "O conteúdo HTML para converter em PDF. Pode incluir estilos CSS e recursos externos.", "Optional CSS styles to apply to the HTML content. Can include inline styles or external stylesheets.": "Estilos CSS opcionais para aplicar ao conteúdo HTML. Pode incluir estilos embutidos ou folhas de estilo externas.", "Optional JSON data to use for templating the HTML content. Can include variables and dynamic content.": "Dados JSON opcionais a serem usados para template do conteúdo HTML. Pode incluir variáveis e conteúdo dinâmico.", "Expiration of the generated PDF in minutes. Use 0 to store permanently, or 1-10080 minutes (7 days) to specify expiration.": "Expiração do PDF gerado em minutos. Use 0 para armazenar permanentemente, ou 1-10080 minutos (7 dias) para especificar a expiração.", "PDF page size format": "Formato de tamanho da página PDF", "PDF page orientation": "Orientação da página PDF", "Top margin in millimeters": "Margem superior em milímetros", "Bottom margin in millimeters": "Margem inferior em milímetros", "Left margin in millimeters": "Margem esquerda em milímetros", "Right margin in millimeters": "Margem direita em milímetros", "Whether to print background graphics and colors": "Imprimir gráficos e cores de fundo", "Font size for header (e.g., \"9px\")": "<PERSON><PERSON><PERSON> da fonte para cabeçalho (ex.: \"9px\")", "Whether to display header and footer": "Exibir cabeçalho e rodapé", "Custom HTML content for header": "Conteúdo HTML personalizado para o cabeçalho", "Custom HTML content for footer": "Conteúdo HTML personalizado para o rodapé", "Scale factor for the PDF (0.1 to 2.0)": "Fator de escala para PDF (0.1 a 2.0)", "Time to wait before generating PDF (in milliseconds)": "Tempo de espera antes de gerar o PDF (em milissegundos)", "The URL of the webpage to convert to PDF": "A URL da página da Web para converter em PDF", "Time to wait for page to load before generating PDF (in milliseconds)": "Tempo de espera que a página carregue antes de gerar o PDF (em milissegundos)", "CSS selector to wait for before generating PDF (e.g., \".content-loaded\")": "Seletor CSS a esperar antes de gerar PDF (por exemplo, \".content-loaded\")", "Browser viewport width in pixels (default: 1920)": "Largura de visualização do navegador em pixels (padrão: 1920)", "Browser viewport height in pixels (default: 1080)": "Altura da visualização do navegador em pixels (padrão: 1080)", "Capture the full scrollable page": "Capturar a página completa", "Select a transaction reference to filter objects.": "Selecione uma transação de referência para filtrar objetos.", "Maximum number of objects to return (default: 300, max: 300)": "Número máximo de objetos a retornar (padrão: 300, máximo: 300)", "Number of objects to skip for pagination (default: 0)": "Número de objetos a ignorar para paginação (padrão: 0)", "Filter objects by template ID (optional)": "Filtrar objetos por ID de modelo (opcional)", "Filter by specific transaction reference (optional)": "Filtrar por referência de transação específica (opcional)", "Start date for filtering (YYYY-MM-DD format, optional)": "Data de início para filtragem (AAAA-MM-DD formato, opcional)", "End date for filtering (YYYY-MM-DD format, optional)": "Data final para filtragem (AAAA-MM-DD formato, opcional)", "Filter by external reference ID (meta field)": "Filtrar por ID de referência externo (campo meta)", "Authorization headers are injected automatically from your connection.": "Os cabeçalhos de autorização são inseridos automaticamente a partir da sua conexão.", "A4": "A4", "A3": "A3", "A5": "A5", "Letter": "Letra", "Legal": "Informações", "Tabloid": "Tablóide", "Portrait": "Retrato", "Landscape": "Paisagem", "GET": "OBTER", "POST": "POSTAR", "PATCH": "COMPRAR", "PUT": "COLOCAR", "DELETE": "EXCLUIR", "HEAD": "CABEÇA"}
{"APITemplate.io": "APITemplate.io", "Region": "Région", "API Key": "Clé API", "Select your preferred API region for better performance": "Sélectionnez votre région API préférée pour de meilleures performances", "Your APITemplate.io API key": "Votre clé API APITemplate.io", "Default (Singapore)": "<PERSON><PERSON> (Singapore)", "Europe (Frankfurt)": "L'Europe (Francfort)", "US East (N. Virginia)": "US East (<PERSON>. <PERSON>)", "Australia (Sydney)": "<PERSON><PERSON><PERSON> (Sydney)", "Alternative - Default (Singapore)": "Alternative - Dé<PERSON>ut (Singapore)", "Alternative - Europe (Frankfurt)": "Alternative - Europe (Francfort)", "Alternative - US East (N. Virginia)": "Alternative - US East (<PERSON>. <PERSON>)", "\nTo obtain your API key:\n1. Go to https://app.apitemplate.io/.\n2. Navigate to API Integration section.\n3. Copy your API key.\n\nSelect the region closest to your location for better performance.\n": "\nTo obtain your API key:\n1. Go to https://app.apitemplate.io/.\n2. Navigate to API Integration section.\n3. Copy your API key.\n\nSelect the region closest to your location for better performance.\n", "Create Image": "<PERSON><PERSON><PERSON> une image", "Create PDF From HTML": "Créer un PDF à partir du HTML", "Create PDF From URL": "Créer un PDF à partir d'une URL", "Create PDF": "Créer un PDF", "Delete Object": "Supprimer l'objet", "Get Account Information": "Obtenir les informations du compte", "List Objects": "Lister les objets", "Custom API Call": "Appel API personnalisé", "Creates an image from a template with provided data.": "Crée une image à partir d'un modèle avec les données fournies.", "Creates a PDF from HTML.": "Crée un PDF à partir du HTML.", "Creates a PDF from a webpage URL.": "Crée un PDF à partir d'une URL de page web.", "Creates a PDF from a template with provided data.": "Crée un PDF à partir d'un modèle avec les données fournies.", "Deletes a generated PDF or image by its transaction reference or object ID.": "Supprime un PDF ou une image généré par sa référence de transaction ou son ID d'objet.", "Retrieves account information including usage statistics and account details.": "Récupère les informations du compte y compris les statistiques d'utilisation et les détails du compte.", "Retrieves a list of generated PDFs and images with optional filtering": "Récupère une liste de PDFs générés et d'images avec filtrage facultatif", "Make a custom API call to a specific endpoint": "Passez un appel API personnalisé à un point de terminaison spécifique", "Template ID": "ID du modèle", "Template Data": "<PERSON><PERSON><PERSON> du modèle", "Generation Delay (ms)": "<PERSON><PERSON>lai de génération (ms)", "External Reference ID": "ID de référence externe", "HTML Content": "Contenu HTML", "CSS Styles": "CSS Styles", "Data for Templating": "Données pour le modèle", "Expiration (minutes)": "Expiration (minutes)", "Page Size": "Nombre d'élément", "Page Orientation": "Orientation de la page", "Margin Top (mm)": "Marge Haut (mm)", "Margin Bottom (mm)": "Marge inférieure (mm)", "Margin Left (mm)": "Marge gauche (mm)", "Margin Right (mm)": "Marge droite (mm)", "Print Background": "Imprimer l'arrière-plan", "Header Font Size": "Header <PERSON><PERSON>", "Display Header/Footer": "Afficher en-tête/pied de page", "Custom Header HTML": "En-tête personnalisé HTML", "Custom Footer HTML": "Pied de page HTML personnalisé", "Scale": "<PERSON><PERSON><PERSON>", "Wait Timeout (ms)": "<PERSON><PERSON><PERSON>atten<PERSON> (ms)", "URL": "URL", "Wait for Selector": "<PERSON><PERSON><PERSON> le <PERSON>eur", "Viewport Width": "<PERSON><PERSON> de la fenêtre", "Viewport Height": "<PERSON><PERSON> de la fenêtre d'affichage", "Full Page": "Page complète", "Transaction Reference": "Référence de <PERSON>", "Limit": "Limite", "Offset": "Décalage", "Date From": "Date de début", "Date To": "Date de fin", "Meta Filter": "Filtre Meta", "Method": "Méthode", "Headers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Query Parameters": "Paramètres de requête", "Body": "Corps", "No Error on Failure": "Aucune erreur en cas d'échec", "Timeout (in seconds)": "<PERSON><PERSON><PERSON>atten<PERSON> (en secondes)", "JSON data with overrides array to populate the template. Format: {\"overrides\": [{\"name\": \"object_name\", \"property\": \"value\"}]}.": "Les données JSON avec un tableau surchargeant le modèle. Format: {\"overrides\": [{\"name\": \"object_name\", \"property\": \"value\"}]}.", "Delay in milliseconds before PDF generation": "Délai en millisecondes avant la génération de PDF", "Specify an external reference ID for your own reference": "Spécifiez un ID de référence externe pour votre propre référence", "The HTML content to convert to PDF. Can include CSS styles and external resources.": "Le contenu HTML à convertir en PDF. Peut inclure les styles CSS et les ressources externes.", "Optional CSS styles to apply to the HTML content. Can include inline styles or external stylesheets.": "Style CSS optionnel à appliquer au contenu HTML. Peut inclure des styles inline ou des feuilles de style externes.", "Optional JSON data to use for templating the HTML content. Can include variables and dynamic content.": "Données JSON optionnelles à utiliser pour modéliser le contenu HTML. Peut inclure des variables et du contenu dynamique.", "Expiration of the generated PDF in minutes. Use 0 to store permanently, or 1-10080 minutes (7 days) to specify expiration.": "Expiration du PDF généré en minutes. Utilisez 0 pour stocker de manière permanente, ou 1-10080 minutes (7 jours) pour spécifier l'expiration.", "PDF page size format": "Format de la taille de la page PDF", "PDF page orientation": "Orientation de la page PDF", "Top margin in millimeters": "Marge supérieure en millimètres", "Bottom margin in millimeters": "Marge inférieure en millimètres", "Left margin in millimeters": "Marge gauche en millimètres", "Right margin in millimeters": "Marge droite en millimètres", "Whether to print background graphics and colors": "Imprimer ou non les graphismes et couleurs d'arrière-plan", "Font size for header (e.g., \"9px\")": "Taille de la police pour l'en-tête (par exemple, \"9px\")", "Whether to display header and footer": "Afficher/Masquer l'en-tête et le pied de page", "Custom HTML content for header": "Contenu HTML personnalisé pour l'en-tête", "Custom HTML content for footer": "Contenu HTML personnalisé pour le pied de page", "Scale factor for the PDF (0.1 to 2.0)": "Facteur d'échelle pour le PDF (0.1 à 2.0)", "Time to wait before generating PDF (in milliseconds)": "Temps d'attente avant de générer le PDF (en millisecondes)", "The URL of the webpage to convert to PDF": "L'URL de la page Web à convertir en PDF", "Time to wait for page to load before generating PDF (in milliseconds)": "Temps d'attente avant le chargement de la page avant de générer le PDF (en millisecondes)", "CSS selector to wait for before generating PDF (e.g., \".content-loaded\")": "Sélecteur CSS à attendre avant de générer des PDF (par exemple, \".content-chargé\")", "Browser viewport width in pixels (default: 1920)": "Largeur de la fenêtre du navigateur en pixels (par défaut: 1920)", "Browser viewport height in pixels (default: 1080)": "Hauteur de la fenêtre du navigateur en pixels (par défaut: 1080)", "Capture the full scrollable page": "Capturer la page défilante complète", "Select a transaction reference to filter objects.": "Sélectionnez une référence de transaction pour filtrer les objets.", "Maximum number of objects to return (default: 300, max: 300)": "Nombre maximum d'objets à retourner (par défaut: 300, max: 300)", "Number of objects to skip for pagination (default: 0)": "Nombre d'objets à ignorer pour la pagination (par défaut: 0)", "Filter objects by template ID (optional)": "Filtrer les objets par ID de modèle (facultatif)", "Filter by specific transaction reference (optional)": "Filtrer par référence de transaction spécifique (facultatif)", "Start date for filtering (YYYY-MM-DD format, optional)": "Date de début pour le filtrage (format AAAA-MM-JJ, optionnel)", "End date for filtering (YYYY-MM-DD format, optional)": "Date de fin de filtrage (format AAAA-MM-JJ, optionnel)", "Filter by external reference ID (meta field)": "Filtrer par ID de référence externe (champ meta)", "Authorization headers are injected automatically from your connection.": "Les en-têtes d'autorisation sont injectés automatiquement à partir de votre connexion.", "A4": "A4", "A3": "A3", "A5": "A5", "Letter": "<PERSON><PERSON>", "Legal": "Mentions légales", "Tabloid": "Tabloïde", "Portrait": "Portrait", "Landscape": "Paysage", "GET": "OBTENIR", "POST": "POSTER", "PATCH": "PATCH", "PUT": "EFFACER", "DELETE": "SUPPRIMER", "HEAD": "TÊTE"}
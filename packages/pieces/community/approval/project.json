{"name": "pieces-approval", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/pieces/community/approval/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/pieces/community/approval", "tsConfig": "packages/pieces/community/approval/tsconfig.lib.json", "packageJson": "packages/pieces/community/approval/package.json", "main": "packages/pieces/community/approval/src/index.ts", "assets": ["packages/pieces/community/approval/*.md", {"input": "packages/pieces/community/approval/src/i18n", "output": "./src/i18n", "glob": "**/!(i18n.json)"}], "buildableProjectDepsInPackageJsonType": "dependencies", "updateBuildableProjectDepsInPackageJson": true}}, "publish": {"command": "node tools/scripts/publish.mjs pieces-approval {args.ver} {args.tag}", "dependsOn": ["build"]}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}, "tags": []}
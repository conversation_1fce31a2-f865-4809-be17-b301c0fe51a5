{"Airtop": "Airtop", "\n\t\tEnter your Airtop API key. You can get your API key from the [Airtop Dashboard](https://portal.airtop.ai/api-keys).\n\t\t\n\t\t**How to get your API key:**\n\t\t1. Go to the [Airtop Dashboard](https://portal.airtop.ai/api-keys)\n\t\t2. Sign in to your account\n\t\t3. Navigate to API Keys section\n\t\t4. Create a new API key or copy an existing one\n\t\t5. Paste the key here\n\t": "\n\t\tEnter your Airtop API key. You can get your API key from the [Airtop Dashboard](https://portal.airtop.ai/api-keys).\n\t\t\n\t\t**How to get your API key:**\n\t\t1. Go to the [Airtop Dashboard](https://portal.airtop.ai/api-keys)\n\t\t2. Sign in to your account\n\t\t3. Navigate to API Keys section\n\t\t4. Create a new API key or copy an existing one\n\t\t5. Paste the key here\n\t", "Create Session": "セッションを作成", "Terminate Session": "セッションを終了", "Create New Browser Window": "新しいブラウザウィンドウを作成", "Take Screenshot": "スクリーンショットを撮影", "Page Query": "ページクエリ", "Smart Scrape": "スマートスクレイプ", "Paginated Extraction": "ページ化された抽出", "Click": "Click", "Type": "タイプ", "Upload File to Sessions": "セッションにファイルをアップロード", "Hover on an Element": "エレメントの上にカーソルを置く", "Custom API Call": "カスタムAPI通話", "Starts a new browser session in Airtop.": "Airtopで新しいブラウザセッションを開始します。", "Ends an existing browser session in Airtop.": "Airtopで既存のブラウザセッションを終了します。", "Opens a new window within a session, optionally navigating to a URL.": "セッション内で新しいウィンドウを開き、必要に応じてURLに移動します。", "Captures a screenshot of the current window.": "現在のウィンドウのスクリーンショットをキャプチャします。", "Query a page to extract data or ask a question given the data on the page.": "ページにデータを抽出したり、ページ上のデータに与えられた質問をしたりするためにページに問い合わせます。", "Scrape a page and return the data as Markdown.": "ページをスクラップして、データを Markdown として返します。", "Extract content from paginated or dynamically loaded pages.": "ページ化または動的にロードされたページからコンテンツを抽出します。", "Execute a click interaction in a specific browser window.": "特定のブラウザウィンドウでクリック操作を実行します。", "Type into a browser window at the specified field.": "指定したフィールドにブラウザウィンドウを入力します。", "Push an existing file to one or more sessions, making it available for use in file inputs or downloads.": "既存のファイルを 1 つまたは複数のセッションにプッシュすると、ファイルの入力またはダウンロードで使用できるようになります。", "Moves mouse pointer over an element in the browser window.": "ブラウザーウィンドウ内の要素の上にマウスポインタを移動します。", "Make a custom API call to a specific endpoint": "特定のエンドポイントへのカスタム API コールを実行します。", "Profile Name": "プロファイル名", "Extension IDs": "エクステンションID", "Use Airtop Proxy?": "Airtopプロキシを使用しますか？", "Custom Proxy Configuration": "カスタムプロキシ設定", "Advanced Proxy Settings": "プロキシの詳細設定", "Solve Captcha": "Captcha を解決", "Session Timeout (minutes)": "セッションタイムアウト（分）", "Session": "セッション", "Initial URL": "初期URL", "Screen Resolution": "画面の解像度", "Custom Resolution": "カスタム解像度", "Page Load Strategy": "ページ読み込み戦略", "Page Load Timeout (seconds)": "ページ読み込みタイムアウト(秒)", "Window": "ウィンドウ", "Client Request ID": "クライアントリクエストID", "Screenshot Format": "スクリーンショットの形式", "Screenshot Scope": "スクリーンショットスコープ", "Max Height (pixels)": "高さの最大値（ピクセル）", "Max Width (pixels)": "幅の最大値（ピクセル）", "JPEG Quality (1-100)": "JPEG品質 (1-100)", "Enable Advanced Visual Analysis": "高度な視覚分析を有効にする", "Visual Analysis Settings": "ビジュアル分析の設定", "Maximum Credits to Spend": "使用する最大クレジット数", "Maximum Time (seconds)": "最大時間 (秒)", "Prompt": "Prompt", "Output Schema (JSON)": "出力スキーマ(JSON)", "Visual Analysis": "ビジュアル分析", "Optimize URLs": "URLの最適化", "Maximum Time (Seconds)": "最大時間 (秒)", "Follow Pagination Links": "ページネーションリンクをフォローする", "Scroll Within": "スクロール範囲内", "How to Load More Content": "さらにコンテンツを読み込む方法", "Speed vs Accuracy": "速度と正確度", "Element Description": "要素の説明", "Click Type": "「タイプ」をクリックします。", "Page Analysis Scope": "ページ分析スコープ", "Result Selection Strategy": "結果選択戦略", "Partition Direction": "パーティションの方向", "Maximum Scan Scrolls": "最大スキャンスクロール", "Scan Scroll Delay (ms)": "スクロールの遅延をスキャン (ミリ秒)", "Overlap Percentage": "重複割合", "Wait for Navigation": "ナビゲーションを待つ", "Navigation Wait Until": "までのナビゲーション待機時間", "Navigation Timeout (Seconds)": "ナビゲーションタイムアウト（秒）", "Text to Type": "文字を入力する", "Clear Input Field Before Typing": "入力する前に入力フィールドをクリア", "Press Enter After Typing": "入力後にEnterを押します", "Press Tab After Typing": "入力後にタブを押す", "Wait for Navigation After Typing": "入力後のナビゲーションを待つ", "Navigation Wait Strategy": "ナビゲーション待機ストラテジー", "Max Scrolls (Scan Mode)": "最大スクロール (スキャンモード)", "Chunk Overlap (%)": "チャンクの重複 (%)", "Scroll Delay (ms)": "スクロール遅延 (ms)", "Max Credits to Spend": "使用するクレジットの最大数", "Max Time to Wait (Seconds)": "待機までの最大時間 (秒)", "File": "ファイル", "Session IDs": "セッションID", "Method": "方法", "Headers": "ヘッダー", "Query Parameters": "クエリパラメータ", "Body": "本文", "No Error on Failure": "失敗時にエラーはありません", "Timeout (in seconds)": "タイムアウト（秒）", "Name of a profile to load into the session.": "セッションにロードするプロファイルの名前。", "List of Chrome extension IDs from Google Web Store.": "Google Web StoreからのChrome拡張IDの一覧。", "Enable Airtop-provided proxy. If disabled, configure a custom proxy.": "Airtop-providedプロキシを有効にします。無効にすると、カスタムプロキシを設定します。", "Automatically solve captcha challenges.": "Captcha チャレンジを自動的に解決します。", "How long before the session times out due to inactivity (1-10080 minutes). Default: 10.": "セッションが非アクティブのためにタイムアウトする期間 (1-10080分) デフォルト: 10.", "Select an active Airtop session to use for browser automation": "ブラウザーの自動化に使用するアクティブなAirtopセッションを選択してください", "URL to open in the new window. Default: https://www.google.com": "新しいウィンドウで開くURL。デフォルト: https://www.google.com", "Fixed dimensions for the browser window. Affects live view size.": "ブラウザーウィンドウの寸法を修正しました。ライブビューのサイズに影響します。", "Custom resolution in format \"widthxheight\" (e.g., \"1440x900\"). Leave blank to use selected resolution above.": "\"widthxheight\" フォーマットのカスタム解像度（例: \"1440x900\"）。上記の選択した解像度を使用する場合は空白のままにしてください。", "When to consider the page loaded. Default: load": "ロードされたページを考慮するタイミング。デフォルト: 読み込み", "Maximum time to wait for page loading. Default: 30 seconds": "ページの読み込みを待つ最大時間。デフォルト: 30 秒", "Select a browser window within the chosen session": "選択したセッション内のブラウザウィンドウを選択します", "Optional ID for tracking this request": "このリクエストを追跡するための任意のID", "How to return the screenshot. Default: base64 for viewport, url for page/scan": "スクリーンショットを返す方法。デフォルト: base64 for viewport, URL for page/scan", "What part of the page to capture. Default: auto": "キャプチャするページのどの部分ですか。デフォルト: 自動", "Maximum height of screenshot. Will scale down if needed, preserving aspect ratio.": "スクリーンショットの最大の高さ。必要に応じてスケールダウンし、アスペクト比を維持します。", "Maximum width of screenshot. Will scale down if needed, preserving aspect ratio.": "スクリーンショットの最大幅。必要に応じてスケールダウンし、アスペクト比を維持します。", "Image quality for JPEG compression. Higher = better quality. Note: Feature in development.": "JPEG圧縮のための画質。より高い=より良い品質。", "Enable advanced visual analysis features for better page processing": "より良いページ処理のために高度な視覚分析機能を有効にする", "Stop screenshot if it costs more than this. Leave blank for default limit.": "これ以上のコストがかかる場合はスクリーンショットを停止します。デフォルトの上限は空白のままにしてください。", "Stop screenshot if it takes longer than this. Leave blank for default timeout.": "これよりも時間がかかる場合はスクリーンショットを停止します。デフォルトのタイムアウトの場合は空白のままにしてください。", "The question or instruction for Airtop to answer about the current page.": "Airtopが現在のページについて答えるための質問または指示。", "An optional ID for your internal tracking.": "内部トラッキングの任意のID。", "JSON schema defining the structure of the output. Must be valid JSON schema format.": "出力の構造を定義する JSON スキーマ。有効な JSON スキーマフォーマットである必要があります。", "Whether to include visual analysis of the page (default: auto)": "ページの視覚的な分析を含めるかどうか（デフォルト：自動）", "Improve scraping performance by optimizing URLs (default: auto)": "URLを最適化することでスクレイピングのパフォーマンスを向上させます（デフォルト：自動）", "Abort if the credit cost exceeds this amount. Set to 0 to disable.": "クレジットコストがこの金額を超える場合は中止します。0に設定すると無効になります。", "Abort if the operation takes longer than this. Set to 0 to disable.": "操作に時間がかかる場合は中止します。0に設定すると無効になります。", "If enabled, Airtop will attempt to load more content from pagination, scrolling, etc. (default: false)": "有効にすると、Airtopはページネーション、スクロールなどからさらにコンテンツを読み込もうとします（デフォルト：false）", "Optional ID to track this request on your end.": "このリクエストを追跡する任意のID。", "Instructions on what to extract and how to paginate (e.g. \"Navigate through 3 pages and extract titles and prices\").": "抽出方法とページネーションの方法(例:「3ページをナビゲートし、タイトルと価格を抽出」)について説明します。", "Optional ID to track this request.": "このリクエストを追跡する任意のID。", "Describe the scrollable area (e.g. \"results container in middle of page\").": "スクロール可能な領域を記述してください。(例: \"結果のコンテナはページの真ん中\")。", "Choose how to navigate through pages (default: auto)": "ページをナビゲートする方法を選択してください（デフォルト：自動）", "Balance between speed and accuracy (default: auto)": "速度と精度のバランス（デフォルト：自動）", "Describe the element to click (e.g. \"Login button\").": "クリックする要素を記述してください（例：「ログイン」ボタン）。", "The type of click to perform (default: left click).": "実行するクリックの種類 (デフォルト: 左クリック)。", "Describe the scrollable area to search within (e.g. \"main content area\").": "検索するスクロール可能な領域を記述してください（例：\"メインコンテンツエリア\"）。", "Controls how much of the page is visually analyzed (default: auto).": "視覚的に分析されるページの量 (デフォルト: auto) を制御します。", "How to select from multiple matches (default: auto).": "複数のマッチから選択する方法(デフォルト: auto)。", "How to partition screenshots for analysis (default: vertical).": "分析用のスクリーンショットを分割する方法（デフォルト：垂直）。", "Maximum number of scrolls in scan mode (default: 50).": "スキャンモードでのスクロールの最大数(デフォルト: 50)。", "Delay between scrolls in scan mode (default: 1000ms).": "スキャンモードでのスクロールの間隔(デフォルト: 1000ms)。", "Percentage of overlap between screenshot chunks (default: 30).": "スクリーンショットのチャンク間の重複率 (デフォルト: 30)。", "Wait for page navigation to complete after clicking (default: false).": "クリック後にページナビゲーションが完了するのを待ちます (デフォルト: false)。", "When to consider navigation complete (default: load).": "ナビゲーション完了を検討するタイミング（デフォルト：負荷）", "Max seconds to wait for navigation (default: 30).": "ナビゲーションを待つ最大秒数（デフォルト：30）", "The text to type into the browser window.": "ブラウザウィンドウに入力するテキスト。", "Describe the element (e.g., \"search box\", \"username field\").": "要素を記述します (例: \"search box\", \"username field\")。", "Clear the input field before typing text.": "テキストを入力する前に入力フィールドをクリアします。", "Press Enter key after typing text.": "テキストを入力した後、Enter キーを押します。", "Press Tab key after typing text (after Enter if both enabled).": "テキストを入力した後、format@@0キーを押します (両方が有効な場合は、Enterキーの後)。", "Wait for page navigation to complete after typing (default: false).": "入力後にページナビゲーションが完了するのを待ちます (デフォルト: false)。", "Max time to wait for navigation after typing (default: 30).": "入力後のナビゲーション待ちの最大時間 (デフォルト: 30)。", "Condition to consider navigation complete (default: load).": "ナビゲーション完了を検討する条件（デフォルト：負荷）", "Controls how much of the page is analyzed to find the input (default: auto).": "入力を見つけるために分析されるページの量 (デフォルト: auto) を制御します。", "Percentage of overlap between visual chunks (default: 30).": "視覚的なチャンク間の重複率 (デフォルト: 30)。", "Direction to partition screenshots (default: vertical).": "パーティションスクリーンショットの方向（デフォルト：垂直）", "Describe the scrollable container (e.g., \"table body\", \"product list\").": "スクロール可能なコンテナを記述します (例: \"table body\", \"product list\")。", "Optional request ID for traceability.": "トレーサビリティのためのオプションのリクエストID。", "Cancel if this limit is exceeded. Set 0 to disable.": "この制限を超えた場合はキャンセルします。0を設定すると無効になります。", "Cancel if exceeded. Set 0 to disable.": "超過した場合はキャンセルします。無効にするには0を設定してください。", "Select a file that has been uploaded to Airtop": "Airtop にアップロードされたファイルを選択", "Select one or more sessions to make the file available on. Leave empty to make available to all sessions.": "ファイルを利用できるようにするセッションを1つ以上選択します。すべてのセッションで利用できるようにするには空のままにします。", "Describe the element to hover, e.g. \"the search box input in the top right corner\".": "ホバーする要素を記述します。例えば、「右上にある検索ボックスの入力」です。", "Wait for page navigation to complete after hovering (default: false).": "ホバリング後にページナビゲーションが完了するのを待ちます (デフォルト: false)。", "Authorization headers are injected automatically from your connection.": "認証ヘッダは接続から自動的に注入されます。", "1280x720 (Default)": "1280x720 (<PERSON><PERSON><PERSON>)", "1920x1080": "1920x1080", "1366x768": "1366x768", "1024x768": "1024x768", "800x600": "800x600", "Load (Page + Assets) - Default": "(Page + Assets) - デフォルト", "DOM Content Loaded": "DOMコンテンツがロードされました", "Complete (Page + Iframes)": "完成（ページ + アイフレーム）", "No Wait (Return Immediately)": "待機なし (すぐに戻ります)", "Base64 Data (Default for Viewport)": "Base64 データ (ビューポートのデフォルト)", "Download URL (Default for Page/Scan)": "ダウンロードURL（ページ/スキャンのデフォルト）", "Auto (Recommended)": "自動 (推奨)", "Current View Only": "現在のビューのみ", "Full Page": "全ページ", "Scan Mode (For Problem Pages)": "スキャンモード（問題ページ用）", "Auto (Default)": "自動 (デフォルト)", "Enabled": "有効", "Disabled": "無効", "Click Next/Previous Links": "次へ/前のリンクをクリックします。", "Infinite Scroll": "無限スクロール", "Auto (Balanced)": "自動 (壁紙)", "More Accurate (Slower)": "より正確な(低速)", "Faster (Less Accurate)": "高速(精度)", "Left Click": "左クリック", "Double Click": "Double Click", "Right Click": "右クリック", "Scan Mode": "スキャンモード", "Auto": "自動", "First Match": "最初の一致", "Best Match": "ベストマッチ", "Vertical": "垂直方向", "Horizontal": "水平方向", "Bidirectional": "双方向型", "load": "負荷", "domcontentloaded": "domcontentloaded", "networkidle0": "networkidle0", "networkidle2": "networkidle2", "Load (Default)": "読み込み (デフォルト)", "Network Idle 0": "ネットワーク待機時間 0", "Network Idle 2": "ネットワークアイドル2", "Viewport Only": "ビューポートのみ", "Vertical (Default)": "Vertical (Default)", "GET": "取得", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "削除", "HEAD": "頭"}
{"Airtop": "Airtop", "\n\t\tEnter your Airtop API key. You can get your API key from the [Airtop Dashboard](https://portal.airtop.ai/api-keys).\n\t\t\n\t\t**How to get your API key:**\n\t\t1. Go to the [Airtop Dashboard](https://portal.airtop.ai/api-keys)\n\t\t2. Sign in to your account\n\t\t3. Navigate to API Keys section\n\t\t4. Create a new API key or copy an existing one\n\t\t5. Paste the key here\n\t": "\n\t\tEnter your Airtop API key. You can get your API key from the [Airtop Dashboard](https://portal.airtop.ai/api-keys).\n\t\t\n\t\t**How to get your API key:**\n\t\t1. Go to the [Airtop Dashboard](https://portal.airtop.ai/api-keys)\n\t\t2. Sign in to your account\n\t\t3. Navigate to API Keys section\n\t\t4. Create a new API key or copy an existing one\n\t\t5. Paste the key here\n\t", "Create Session": "Создать сессию", "Terminate Session": "Завершить сеанс", "Create New Browser Window": "Создать новое окно браузера", "Take Screenshot": "Сделать скриншот", "Page Query": "Запрос страницы", "Smart Scrape": "Смарт-скрап", "Paginated Extraction": "Распакованное извлечение", "Click": "Click", "Type": "Тип", "Upload File to Sessions": "Загрузить файл на сессии", "Hover on an Element": "Наведите на элемент", "Custom API Call": "Пользовательский вызов API", "Starts a new browser session in Airtop.": "Запускает новый сеанс браузера в Airtop.", "Ends an existing browser session in Airtop.": "Завершает сеанс существующего браузера в Airtop.", "Opens a new window within a session, optionally navigating to a URL.": "Открывает новое окно в рамках сессии, при необходимости перейдя по ссылке.", "Captures a screenshot of the current window.": "Записывает скриншот текущего окна.", "Query a page to extract data or ask a question given the data on the page.": "Запросить страницу для извлечения данных или задать вопрос на этой странице.", "Scrape a page and return the data as Markdown.": "Перемещает страницу и возвращает данные как Markdown.", "Extract content from paginated or dynamically loaded pages.": "Извлечь содержимое из страничных или динамически загруженных страниц.", "Execute a click interaction in a specific browser window.": "Выполнить взаимодействие клика в определенном окне браузера.", "Type into a browser window at the specified field.": "Ввод в окно браузера в указанное поле.", "Push an existing file to one or more sessions, making it available for use in file inputs or downloads.": "Передать существующий файл в одну или несколько сессий, делая его доступным для использования в файловых входах или загрузках.", "Moves mouse pointer over an element in the browser window.": "Перемещает указатель мыши на элемент в окне браузера.", "Make a custom API call to a specific endpoint": "Сделать пользовательский API вызов к определенной конечной точке", "Profile Name": "Имя профиля", "Extension IDs": "ID расширений", "Use Airtop Proxy?": "Использовать прокси?", "Custom Proxy Configuration": "Пользовательские настройки прокси", "Advanced Proxy Settings": "Расширенные настройки прокси", "Solve Captcha": "Решить капчу", "Session Timeout (minutes)": "Таймаут сессии (минуты)", "Session": "Сессия", "Initial URL": "Исходный URL", "Screen Resolution": "Разрешение экрана", "Custom Resolution": "Пользовательское разрешение", "Page Load Strategy": "Стратегия загрузки страницы", "Page Load Timeout (seconds)": "Таймаут загрузки страницы (в секундах)", "Window": "Окно", "Client Request ID": "ID запроса клиента", "Screenshot Format": "Формат скриншота", "Screenshot Scope": "Область скриншота", "Max Height (pixels)": "Макс. высота (пикселей)", "Max Width (pixels)": "Макс. шири<PERSON> (пикселей)", "JPEG Quality (1-100)": "Качество JPEG (1-100)", "Enable Advanced Visual Analysis": "Включить расширенный визуальный анализ", "Visual Analysis Settings": "Настройки визуального анализа", "Maximum Credits to Spend": "Максимальное количество кредитов для траты", "Maximum Time (seconds)": "Максимальное время (в секундах)", "Prompt": "Prompt", "Output Schema (JSON)": "Схема вывода (JSON)", "Visual Analysis": "Визуальный анализ", "Optimize URLs": "Оптимизировать URL", "Maximum Time (Seconds)": "Максимальное время (секунды)", "Follow Pagination Links": "Перейти к нумерации ссылок", "Scroll Within": "Прокрутка в", "How to Load More Content": "Как загрузить больше контента", "Speed vs Accuracy": "Точность скорости и точности", "Element Description": "Описание элемента", "Click Type": "Ти<PERSON> клика", "Page Analysis Scope": "Область анализа страниц", "Result Selection Strategy": "Стратегия выбора результата", "Partition Direction": "Направление раздела", "Maximum Scan Scrolls": "Максимальное количество прокруток", "Scan Scroll Delay (ms)": "Задержка прокрутки (мс)", "Overlap Percentage": "Процент наложения", "Wait for Navigation": "Подождите навигации", "Navigation Wait Until": "Ожидание навигации до", "Navigation Timeout (Seconds)": "Тайм-аут навигации (секунды)", "Text to Type": "Текст для типа", "Clear Input Field Before Typing": "Очистить входное поле перед вводом", "Press Enter After Typing": "Нажмите Enter после ввода", "Press Tab After Typing": "Нажмите Tab после ввода", "Wait for Navigation After Typing": "Дождитесь навигации после ввода текста", "Navigation Wait Strategy": "Стратегия ожидания навигации", "Max Scrolls (Scan Mode)": "Макс. прокрутки (режим сканирования)", "Chunk Overlap (%)": "Наложение чанка (%)", "Scroll Delay (ms)": "Задержка прокрутки (мс)", "Max Credits to Spend": "Макс. кредитов для траты", "Max Time to Wait (Seconds)": "Максимальное время ожидания (сек.)", "File": "<PERSON>а<PERSON><PERSON>", "Session IDs": "ID сеанса", "Method": "Метод", "Headers": "Заголовки", "Query Parameters": "Параметры запроса", "Body": "Тело", "No Error on Failure": "Нет ошибок при ошибке", "Timeout (in seconds)": "Таймаут (в секундах)", "Name of a profile to load into the session.": "Имя профиля для загрузки в сессию.", "List of Chrome extension IDs from Google Web Store.": "Список идентификаторов расширений Chrome из Google Web Store.", "Enable Airtop-provided proxy. If disabled, configure a custom proxy.": "Включить прокси-сервер с Airtop. Если отключено, настройте пользовательский прокси.", "Automatically solve captcha challenges.": "Автоматически решать captcha вызовы.", "How long before the session times out due to inactivity (1-10080 minutes). Default: 10.": "Как долго до перерыва сессии из-за бездействия (1-10080 минут), по умолчанию: 10.", "Select an active Airtop session to use for browser automation": "Выберите активный сеанс Airtop для автоматизации браузера", "URL to open in the new window. Default: https://www.google.com": "URL для открытия в новом окне. По умолчанию: https://www.google.com", "Fixed dimensions for the browser window. Affects live view size.": "Фиксированные размеры для окна браузера. Влияет на размер живого просмотра.", "Custom resolution in format \"widthxheight\" (e.g., \"1440x900\"). Leave blank to use selected resolution above.": "Пользовательское разрешение в формате «widthxheight» (например, «1440x900»). Оставьте пустым, чтобы использовать выбранное разрешение выше.", "When to consider the page loaded. Default: load": "Когда учитывать загрузку страницы. По умолчанию: загрузка", "Maximum time to wait for page loading. Default: 30 seconds": "Максимальное время ожидания загрузки страницы. По умолчанию: 30 секунд", "Select a browser window within the chosen session": "Выберите окно браузера в рамках выбранной сессии", "Optional ID for tracking this request": "Дополнительный ID для отслеживания этого запроса", "How to return the screenshot. Default: base64 for viewport, url for page/scan": "Как вернуть снимок экрана. По умолчанию: base64 для просмотра, url для страницы/сканирования", "What part of the page to capture. Default: auto": "Какая часть страницы для захвата. По умолчанию: автоматическое", "Maximum height of screenshot. Will scale down if needed, preserving aspect ratio.": "Максимальная высота скриншота. При необходимости будет уменьшаться при сохранении пропорций.", "Maximum width of screenshot. Will scale down if needed, preserving aspect ratio.": "Максимальная ширина скриншота. При необходимости будет уменьшаться при сохранении пропорций.", "Image quality for JPEG compression. Higher = better quality. Note: Feature in development.": "Качество изображения для сжатия JPEG. Высокое качество = лучшее. Примечание: Функция в разработке.", "Enable advanced visual analysis features for better page processing": "Включить расширенные функции визуального анализа для лучшей обработки страниц", "Stop screenshot if it costs more than this. Leave blank for default limit.": "Остановить скриншот, если он стоит больше, чем это. Оставьте пустым для стандартного ограничения.", "Stop screenshot if it takes longer than this. Leave blank for default timeout.": "Остановить скриншот, если он занимает больше времени. Оставьте поле пустым для тайм-аута по умолчанию.", "The question or instruction for Airtop to answer about the current page.": "Вопрос или инструкция для Airtop ответить на текущую страницу.", "An optional ID for your internal tracking.": "Необязательный идентификатор для вашего внутреннего слежения.", "JSON schema defining the structure of the output. Must be valid JSON schema format.": "Схема JSON, определяющая структуру выходной информации. Должен быть корректным форматом схемы JSON.", "Whether to include visual analysis of the page (default: auto)": "Включить визуальный анализ страницы (по умолчанию: авто)", "Improve scraping performance by optimizing URLs (default: auto)": "Улучшить производительность scraping путем оптимизации URL (по умолчанию: авто)", "Abort if the credit cost exceeds this amount. Set to 0 to disable.": "Прервать, если стоимость кредита превышает указанную сумму. Установите в 0, чтобы отключить.", "Abort if the operation takes longer than this. Set to 0 to disable.": "Прервать, если операция занимает больше времени. Установите значение 0 для отключения.", "If enabled, Airtop will attempt to load more content from pagination, scrolling, etc. (default: false)": "Если включено, Airtop попытается загрузить больше содержимого из пагинации, прокрутки и т. д. (по умолчанию: false)", "Optional ID to track this request on your end.": "Необязательный идентификатор для отслеживания этого запроса в вашем конце.", "Instructions on what to extract and how to paginate (e.g. \"Navigate through 3 pages and extract titles and prices\").": "Инструкции о том, что извлекать и как печатать (например \"Перейдите через 3 страницы и извлеките заголовки и цены\").", "Optional ID to track this request.": "Необязательный ID для отслеживания этого запроса.", "Describe the scrollable area (e.g. \"results container in middle of page\").": "Опишите область прокрутки (например, «контейнер результатов посередине страницы»).", "Choose how to navigate through pages (default: auto)": "Выберите способ навигации по страницам (по умолчанию: авто)", "Balance between speed and accuracy (default: auto)": "Баланс между скоростью и точностью (по умолчанию: авто)", "Describe the element to click (e.g. \"Login button\").": "Опишите элемент для нажатия (например, \"Кнопка входа\").", "The type of click to perform (default: left click).": "Тип клика для выполнения (по умолчанию: левый клик).", "Describe the scrollable area to search within (e.g. \"main content area\").": "Опишите область прокрутки для поиска внутри (например, \"основная область содержимого\").", "Controls how much of the page is visually analyzed (default: auto).": "Определяет, сколько страниц анализируется визуально (по умолчанию: авто).", "How to select from multiple matches (default: auto).": "Как выбрать из нескольких совпадений (по умолчанию: авто).", "How to partition screenshots for analysis (default: vertical).": "Как разметить скриншоты для анализа (по умолчанию: вертикальный).", "Maximum number of scrolls in scan mode (default: 50).": "Максимальное количество прокрутки в режиме сканирования (по умолчанию: 50).", "Delay between scrolls in scan mode (default: 1000ms).": "Задержка между прокруткой в режиме сканирования (по умолчанию: 1000мс).", "Percentage of overlap between screenshot chunks (default: 30).": "Процент перекрытия между чанками скриншота (по умолчанию: 30).", "Wait for page navigation to complete after clicking (default: false).": "Дождитесь завершения навигации по странице после нажатия (по умолчанию: false).", "When to consider navigation complete (default: load).": "Когда рассматривать навигацию завершенной (по умолчанию: загрузка).", "Max seconds to wait for navigation (default: 30).": "Максимум секунд ожидания навигации (по умолчанию: 30).", "The text to type into the browser window.": "Текст для ввода в окне браузера.", "Describe the element (e.g., \"search box\", \"username field\").": "Опишите элемент (например, \"search box\", \"username field\").", "Clear the input field before typing text.": "Очистите поле ввода перед вводом.", "Press Enter key after typing text.": "Нажмите клавишу Enter после ввода текста.", "Press Tab key after typing text (after Enter if both enabled).": "Нажмите клавишу Tab после ввода текста (после Enter, если оба включены).", "Wait for page navigation to complete after typing (default: false).": "Дождитесь завершения навигации по страницам после ввода текста (по умолчанию: false).", "Max time to wait for navigation after typing (default: 30).": "Максимальное время ожидания навигации после ввода текста (по умолчанию: 30).", "Condition to consider navigation complete (default: load).": "Условие для рассмотрения завершенной навигации (по умолчанию: загрузка).", "Controls how much of the page is analyzed to find the input (default: auto).": "Определяет, сколько страниц будет проанализировано для поиска ввода (по умолчанию: авто).", "Percentage of overlap between visual chunks (default: 30).": "Процент перекрытия между чанками (по умолчанию: 30).", "Direction to partition screenshots (default: vertical).": "Направление к разделу скриншоты (по умолчанию: вертикальный).", "Describe the scrollable container (e.g., \"table body\", \"product list\").": "Опишите контейнер с прокруткой (например, \"table body\", \"list продуктов\").", "Optional request ID for traceability.": "Необязательный идентификатор запроса для трассировки.", "Cancel if this limit is exceeded. Set 0 to disable.": "Отменить, если этот лимит превышен. Установите 0 для отключения.", "Cancel if exceeded. Set 0 to disable.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, если превышено. Установите 0 для отключения.", "Select a file that has been uploaded to Airtop": "Выберите файл, который был загружен в Airtop", "Select one or more sessions to make the file available on. Leave empty to make available to all sessions.": "Выберите одну или несколько сессий, чтобы сделать файл доступным. Оставьте пустым, чтобы сделать файл доступным для всех сессий.", "Describe the element to hover, e.g. \"the search box input in the top right corner\".": "Опишите элемент навеса, например \"Ввод поля поиска в правом верхнем углу\".", "Wait for page navigation to complete after hovering (default: false).": "Дождитесь завершения навигации после наведения курсора (по умолчанию: false).", "Authorization headers are injected automatically from your connection.": "Заголовки авторизации включаются автоматически из вашего соединения.", "1280x720 (Default)": "1280x720 (по умолчанию)", "1920x1080": "1920 x 1080", "1366x768": "1366x768", "1024x768": "1024 x 768", "800x600": "800х600", "Load (Page + Assets) - Default": "Загрузка страницы + активы) - по умолчанию", "DOM Content Loaded": "Содержимое DOM загружено", "Complete (Page + Iframes)": "Полная страница + Iframes)", "No Wait (Return Immediately)": "Нет ожидания (Немедленно (Return)", "Base64 Data (Default for Viewport)": "Base64 данные (по умолчанию для просмотра)", "Download URL (Default for Page/Scan)": "URL загрузки (по умолчанию для страницы/сканирования)", "Auto (Recommended)": "Авто (рекомендуется)", "Current View Only": "Только текущий вид", "Full Page": "Полная страница", "Scan Mode (For Problem Pages)": "Режим сканирования (для страниц заданий)", "Auto (Default)": "Авто (по умолчанию)", "Enabled": "Включено", "Disabled": "Отключено", "Click Next/Previous Links": "Нажмите Далее/Предыдущие ссылки", "Infinite Scroll": "Бесконечная прокрутка", "Auto (Balanced)": "<PERSON><PERSON><PERSON><PERSON> (Балансирован)", "More Accurate (Slower)": "Больше точности (медленнее)", "Faster (Less Accurate)": "Быстрее (меньше точности)", "Left Click": "Щелчок левой кнопкой", "Double Click": "Двойной клик", "Right Click": "Правый клик", "Scan Mode": "Режим сканирования", "Auto": "Авто", "First Match": "Первый матч", "Best Match": "<PERSON><PERSON><PERSON><PERSON><PERSON> матч", "Vertical": "Вертикальный", "Horizontal": "Горизонтально", "Bidirectional": "Двунаправленный", "load": "нагрузка", "domcontentloaded": "domcontent: загру<PERSON><PERSON>н", "networkidle0": "сетевой сервер0", "networkidle2": "сетевой сервер2", "Load (Default)": "Загрузить (по умолчанию)", "Network Idle 0": "Отсутствие сети 0", "Network Idle 2": "Отсутствие сети 2", "Viewport Only": "Только просмотр", "Vertical (Default)": "Вертикальный (по умолчанию)", "GET": "ПОЛУЧИТЬ", "POST": "ПОСТ", "PATCH": "ПАТЧ", "PUT": "ПОКУПИТЬ", "DELETE": "УДАЛИТЬ", "HEAD": "HEAD"}
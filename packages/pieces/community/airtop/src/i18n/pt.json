{"Airtop": "Airtop", "\n\t\tEnter your Airtop API key. You can get your API key from the [Airtop Dashboard](https://portal.airtop.ai/api-keys).\n\t\t\n\t\t**How to get your API key:**\n\t\t1. Go to the [Airtop Dashboard](https://portal.airtop.ai/api-keys)\n\t\t2. Sign in to your account\n\t\t3. Navigate to API Keys section\n\t\t4. Create a new API key or copy an existing one\n\t\t5. Paste the key here\n\t": "\n\t\t『Digite sua chave de API Airtop. Você pode obter sua chave de API no [Painel do Airtop](https://portal.airtop.ai/api-keys).\n\t\t├\t\t\n\t\t├**Como obter a sua chave de API:**\n\t\t├1. Acesse o [Painel do Airtop](https://portal.airtop.ai/api-keys)\n\t\t£2. Faça login na sua conta\n\t\t├3. Navegue para a seção Chaves de API\n\t\t£4. Crie uma nova chave de API ou copie uma já existente\n\t\t£5. Cole a chave aqui\n\t", "Create Session": "<PERSON><PERSON><PERSON>", "Terminate Session": "<PERSON><PERSON><PERSON><PERSON>", "Create New Browser Window": "Criar nova janela do navegador", "Take Screenshot": "Tirar Screenshot", "Page Query": "Consulta de Página", "Smart Scrape": "Scrape Inteligente", "Paginated Extraction": "Extração Paginada", "Click": "Click", "Type": "tipo", "Upload File to Sessions": "Enviar Arquivo para Sessões", "Hover on an Element": "Passe o mouse sobre um elemento", "Custom API Call": "Chamada de API personalizada", "Starts a new browser session in Airtop.": "Inicia uma nova sessão de navegador no Airtop.", "Ends an existing browser session in Airtop.": "Termina uma sessão do navegador existente no Airtop.", "Opens a new window within a session, optionally navigating to a URL.": "Abre uma nova janela dentro de uma sessão, opcionalmente navegando para uma URL.", "Captures a screenshot of the current window.": "Captura uma captura de tela da janela atual.", "Query a page to extract data or ask a question given the data on the page.": "Consulte uma página para extrair dados ou fazer uma pergunta dados dados na página.", "Scrape a page and return the data as Markdown.": "Scrape uma página e devolve os dados como Markdown.", "Extract content from paginated or dynamically loaded pages.": "Extraia conteúdo de páginas paginadas ou carregadas dinamicamente.", "Execute a click interaction in a specific browser window.": "Executar uma interação de um clique em uma janela específica do navegador.", "Type into a browser window at the specified field.": "Digite em uma janela do navegador no campo especificado.", "Push an existing file to one or more sessions, making it available for use in file inputs or downloads.": "Faça push de um arquivo existente para uma ou mais sessões, disponibilizando-o para uso em entradas de arquivo ou downloads.", "Moves mouse pointer over an element in the browser window.": "Move o mouse sobre um elemento na janela do navegador.", "Make a custom API call to a specific endpoint": "Faça uma chamada de API personalizada para um ponto de extremidade específico", "Profile Name": "Nome do perfil", "Extension IDs": "IDs de extensão", "Use Airtop Proxy?": "Usar o Airtop Proxy?", "Custom Proxy Configuration": "Configuração de proxy personalizado", "Advanced Proxy Settings": "Configurações avançadas de Proxy", "Solve Captcha": "Resolver Captcha", "Session Timeout (minutes)": "Tempo limite da sessão (minutos)", "Session": "Sessão", "Initial URL": "URL inicial", "Screen Resolution": "Resolução da tela", "Custom Resolution": "Resolução Personalizada", "Page Load Strategy": "Carga da Página", "Page Load Timeout (seconds)": "Tempo limite de carregamento da página (segundos)", "Window": "<PERSON><PERSON>", "Client Request ID": "ID de Requisição de Cliente", "Screenshot Format": "Formato de Screenshot", "Screenshot Scope": "Captura de tela", "Max Height (pixels)": "Altura Máxima (pixels)", "Max Width (pixels)": "<PERSON><PERSON><PERSON> máxima (pixels)", "JPEG Quality (1-100)": "Qualidade JPEG (1-100)", "Enable Advanced Visual Analysis": "Ativar análise visual avançada", "Visual Analysis Settings": "Configurações de análise visual", "Maximum Credits to Spend": "Máximo de Créditos para Gastar", "Maximum Time (seconds)": "Tempo máximo (segundos)", "Prompt": "Aviso", "Output Schema (JSON)": "Esquema de saída (JSON)", "Visual Analysis": "<PERSON><PERSON><PERSON><PERSON> visual", "Optimize URLs": "<PERSON><PERSON><PERSON>zar <PERSON>s", "Maximum Time (Seconds)": "Tempo máximo (segundos)", "Follow Pagination Links": "Seguir links de paginação", "Scroll Within": "Rolar Dentro", "How to Load More Content": "<PERSON> carregar mais conte<PERSON>do", "Speed vs Accuracy": "Velocidade vs Precisão", "Element Description": "Descrição do Elemento", "Click Type": "Tipo de Clique", "Page Analysis Scope": "Escopo Análise da Página", "Result Selection Strategy": "Estratégia de Seleção de Resultados", "Partition Direction": "Direção de partição", "Maximum Scan Scrolls": "Rolagem máxima de escaneamento", "Scan Scroll Delay (ms)": "Atraso de rolagem de escaneamento (ms)", "Overlap Percentage": "Percentagem de sobreposição", "Wait for Navigation": "Esperar por Navegação", "Navigation Wait Until": "Esperar por Navegação Até", "Navigation Timeout (Seconds)": "Timeout de Navegação (Segundos)", "Text to Type": "Texto para Tipo", "Clear Input Field Before Typing": "Limpar Campo de Entrada Antes de Digitar", "Press Enter After Typing": "Pressione Enter Após Digitar", "Press Tab After Typing": "Pressione Tab Após Digitar", "Wait for Navigation After Typing": "Esperar por Navegação Após Digitar", "Navigation Wait Strategy": "Estratégia de Espera de Navegação", "Max Scrolls (Scan Mode)": "Máximo de rolagens (Modo pode)", "Chunk Overlap (%)": "Sobreposição de Pedaço (%)", "Scroll Delay (ms)": "Atraso de rolagem (ms)", "Max Credits to Spend": "Máximo de Créditos para Gastar", "Max Time to Wait (Seconds)": "Tempo máximo de espera (segundos)", "File": "Arquivo", "Session IDs": "IDs de sessão", "Method": "<PERSON><PERSON><PERSON><PERSON>", "Headers": "Cabeçalhos", "Query Parameters": "Parâ<PERSON><PERSON> da consult<PERSON>", "Body": "<PERSON><PERSON><PERSON><PERSON>", "No Error on Failure": "Nenhum erro no Failure", "Timeout (in seconds)": "Tempo limite (em segundos)", "Name of a profile to load into the session.": "Nome de um perfil para carregar na sessão.", "List of Chrome extension IDs from Google Web Store.": "Lista de IDs de extensões do Chrome do Google Web Store.", "Enable Airtop-provided proxy. If disabled, configure a custom proxy.": "Ativa o proxy fornecido pelo Airtop. Se desativado, configure um proxy personalizado.", "Automatically solve captcha challenges.": "Automaticamente resolva desafios de captcha.", "How long before the session times out due to inactivity (1-10080 minutes). Default: 10.": "Quanto tempo antes do tempo limite da sessão por inatividade (1-10080 minutos). Padrão: 10.", "Select an active Airtop session to use for browser automation": "Selecione uma sessão do Airtop ativa para automação do navegador", "URL to open in the new window. Default: https://www.google.com": "URL para abrir na nova janela. Padrão: https://www.google.com", "Fixed dimensions for the browser window. Affects live view size.": "Dimensões fixas para a janela do navegador. Afeta tamanho de visualização ao vivo.", "Custom resolution in format \"widthxheight\" (e.g., \"1440x900\"). Leave blank to use selected resolution above.": "Resolução personalizada no formato \"widthxheight\" (ex: \"1440x900\"). Deixe em branco para usar a resolução selecionada acima.", "When to consider the page loaded. Default: load": "Quando considerar a página carregada. Padrão: carregar", "Maximum time to wait for page loading. Default: 30 seconds": "Tempo máximo de espera para carregamento da página. Padrão: 30 segundos", "Select a browser window within the chosen session": "Selecione uma janela do navegador na sessão escolhida", "Optional ID for tracking this request": "ID opcional para rastrear este pedido", "How to return the screenshot. Default: base64 for viewport, url for page/scan": "Como devolver a captura de tela. Padrão: base64 para viewport, url para página/escanear", "What part of the page to capture. Default: auto": "Qual parte da página a capturar. Padrão: auto", "Maximum height of screenshot. Will scale down if needed, preserving aspect ratio.": "Altura máxima da captura de tela. Redimensionará se necessário, preservando proporção de aspecto.", "Maximum width of screenshot. Will scale down if needed, preserving aspect ratio.": "Largura máxima da captura de tela. Redimensionará se necessário, preservando proporção de aspecto.", "Image quality for JPEG compression. Higher = better quality. Note: Feature in development.": "Qualidade de imagem para compressão JPEG. Maior = melhor qualidade. Nota: Recurso em desenvolvimento.", "Enable advanced visual analysis features for better page processing": "Ativar recursos avançados de análise visual para melhor processamento de página", "Stop screenshot if it costs more than this. Leave blank for default limit.": "Parar captura de tela se isso custar mais do que esta. Deixe em branco para o limite padrão.", "Stop screenshot if it takes longer than this. Leave blank for default timeout.": "Interromper a captura de tela se demorar mais do que este. Deixe em branco para o tempo limite padrão.", "The question or instruction for Airtop to answer about the current page.": "A pergunta ou instrução para o Airtop para responder sobre a página atual.", "An optional ID for your internal tracking.": "Um ID opcional para seu rastreamento interno.", "JSON schema defining the structure of the output. Must be valid JSON schema format.": "O esquema JSON que define a estrutura da saída. Deve ser um formato de esquema JSON válido.", "Whether to include visual analysis of the page (default: auto)": "Se deve incluir a análise visual da página (padrão: automático)", "Improve scraping performance by optimizing URLs (default: auto)": "Melhore o desempenho do scraping otimizando URLs (padrão: automático)", "Abort if the credit cost exceeds this amount. Set to 0 to disable.": "Abortar se o custo de crédito exceder este valor. Defina como 0 para desativar.", "Abort if the operation takes longer than this. Set to 0 to disable.": "Abortar se a operação demorar mais do que isso. Ajuste para 0 para desativar.", "If enabled, Airtop will attempt to load more content from pagination, scrolling, etc. (default: false)": "Se ativado, o Airtop tentará carregar mais conteúdo da paginação, rolagem, etc. (padrão: falso)", "Optional ID to track this request on your end.": "ID opcional para acompanhar esta requisição no seu lado.", "Instructions on what to extract and how to paginate (e.g. \"Navigate through 3 pages and extract titles and prices\").": "Instruções sobre o que extrair e como paginar (por exemplo, \"Navegar por 3 páginas e extrair títulos e preços\").", "Optional ID to track this request.": "ID opcional para acompanhar esta solicitação.", "Describe the scrollable area (e.g. \"results container in middle of page\").": "Descreva a área de rolagem (por exemplo, \"resultados do contêiner no meio da página\").", "Choose how to navigate through pages (default: auto)": "Escolha como navegar através de páginas (padrão: automático)", "Balance between speed and accuracy (default: auto)": "Saldo entre a velocidade e precisão (padrão: automático)", "Describe the element to click (e.g. \"Login button\").": "Descrever o elemento para clicar (por exemplo, \"Botão de login\").", "The type of click to perform (default: left click).": "O tipo de clique para executar (padrão: clique com o esquerdo).", "Describe the scrollable area to search within (e.g. \"main content area\").": "Descreva a área de rolagem a ser pesquisada dentro (por exemplo, \"a área principal de conteúdo\").", "Controls how much of the page is visually analyzed (default: auto).": "Controla quanto da página é visualmente analisada (padrão: automática).", "How to select from multiple matches (default: auto).": "Como selecionar de várias correspondências (padrão: automático).", "How to partition screenshots for analysis (default: vertical).": "Como dividir capturas de tela para análise (padrão: vertical).", "Maximum number of scrolls in scan mode (default: 50).": "Número máximo de rolagens no modo de varredura (padrão: 50).", "Delay between scrolls in scan mode (default: 1000ms).": "<PERSON><PERSON><PERSON> entre as rolagens no modo de varredura (padrão: 1000ms).", "Percentage of overlap between screenshot chunks (default: 30).": "Porcentagem de sobreposição entre blocos de captura de tela (padrão: 30).", "Wait for page navigation to complete after clicking (default: false).": "Aguarde a conclusão da navegação da página após clicar (padrão: falso).", "When to consider navigation complete (default: load).": "Quando considerar a navegação concluída (padrão: carregar).", "Max seconds to wait for navigation (default: 30).": "Máximo de segundos para esperar pela navegação (padrão: 30).", "The text to type into the browser window.": "O texto para digitar na janela do navegador.", "Describe the element (e.g., \"search box\", \"username field\").": "Descreva o elemento (por exemplo, \"caixa de pesquisa\", \"campo de usuário\").", "Clear the input field before typing text.": "Limpa o campo de entrada antes de digitar texto.", "Press Enter key after typing text.": "Pressione Enter na tecla após digitar texto.", "Press Tab key after typing text (after Enter if both enabled).": "Pressione a tecla Tab após digitar o texto (após Enter se habilitado).", "Wait for page navigation to complete after typing (default: false).": "Aguarde a conclusão da navegação da página após digitar (padrão: falso).", "Max time to wait for navigation after typing (default: 30).": "Tempo máximo de espera pela navegação após digitar (padrão: 30).", "Condition to consider navigation complete (default: load).": "Condição para considerar a navegação completa (padrão: carregar).", "Controls how much of the page is analyzed to find the input (default: auto).": "Controla quanto da página é analisada para encontrar a entrada (padrão: auto).", "Percentage of overlap between visual chunks (default: 30).": "Porcentagem de sobreposição entre chunks visuais (padrão: 30).", "Direction to partition screenshots (default: vertical).": "Direção para capturas de tela (padrão: vertical).", "Describe the scrollable container (e.g., \"table body\", \"product list\").": "<PERSON><PERSON><PERSON> o cont<PERSON>iner des<PERSON> (por exemplo, \"corpo da tabela\", \"lista de produto\").", "Optional request ID for traceability.": "ID opcional da requisição de rastreabilidade.", "Cancel if this limit is exceeded. Set 0 to disable.": "Cancelar se este limite for excedido. Defina 0 para desativar.", "Cancel if exceeded. Set 0 to disable.": "Cancelar se for excedido. Defina 0 para desativar.", "Select a file that has been uploaded to Airtop": "Selecione um arquivo que foi enviado para o Airtop", "Select one or more sessions to make the file available on. Leave empty to make available to all sessions.": "Selecione uma ou mais sessões para disponibilizar o arquivo. Deixe em branco para disponibilizar para todas as sessões.", "Describe the element to hover, e.g. \"the search box input in the top right corner\".": "Descreva o elemento para passar, por exemplo, \"a caixa de pesquisa na parte superior direita\".", "Wait for page navigation to complete after hovering (default: false).": "Aguarde a conclusão da navegação da página após o cursor (padrão: falso).", "Authorization headers are injected automatically from your connection.": "Os cabeçalhos de autorização são inseridos automaticamente a partir da sua conexão.", "1280x720 (Default)": "1280x720 (Padrão)", "1920x1080": "1920x1080", "1366x768": "1366x768", "1024x768": "1024x768", "800x600": "800x600", "Load (Page + Assets) - Default": "Carregar (Página + Ativos) - Padrão", "DOM Content Loaded": "Conteúdo do DOM carregado", "Complete (Page + Iframes)": "Complete (Page + <PERSON><PERSON><PERSON>)", "No Wait (Return Immediately)": "<PERSON><PERSON><PERSON> (Retorno Imediatamente)", "Base64 Data (Default for Viewport)": "Dados Base 64 (padr<PERSON> para Viewport)", "Download URL (Default for Page/Scan)": "Baixar URL (padrão para Página/Escante)", "Auto (Recommended)": "Automático (recomendado)", "Current View Only": "Apenas Visualização Atual", "Full Page": "Página inteira", "Scan Mode (For Problem Pages)": "Modo de Escaneo (Para páginas de problemas)", "Auto (Default)": "Automático (Padrão)", "Enabled": "<PERSON><PERSON>do", "Disabled": "Desabilitado", "Click Next/Previous Links": "Clique em Links Próximo/Anterior", "Infinite Scroll": "Pergaminho infinito", "Auto (Balanced)": "Automático (Balanceado)", "More Accurate (Slower)": "Mais precisa (lento)", "Faster (Less Accurate)": "<PERSON><PERSON> (Menos Precisão)", "Left Click": "Clique Esquerdo", "Double Click": "Clique Duplo", "Right Click": "Clique direito", "Scan Mode": "Modo de Escaneo", "Auto": "Automático", "First Match": "Primeira Partida", "Best Match": "<PERSON><PERSON> partida", "Vertical": "Vertical", "Horizontal": "Horizontal", "Bidirectional": "Bidirecional", "load": "carregar", "domcontentloaded": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "networkidle0": "redeidle0", "networkidle2": "rede2", "Load (Default)": "Carregar (Padrão)", "Network Idle 0": "Rede ociosa 0", "Network Idle 2": "Rede ociosa 2", "Viewport Only": "Somente Visualizar", "Vertical (Default)": "Vertical (Padrão)", "GET": "OBTER", "POST": "POSTAR", "PATCH": "COMPRAR", "PUT": "COLOCAR", "DELETE": "EXCLUIR", "HEAD": "CABEÇA"}
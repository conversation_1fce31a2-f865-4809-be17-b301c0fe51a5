{"Airtop": "Airtop", "\n\t\tEnter your Airtop API key. You can get your API key from the [Airtop Dashboard](https://portal.airtop.ai/api-keys).\n\t\t\n\t\t**How to get your API key:**\n\t\t1. Go to the [Airtop Dashboard](https://portal.airtop.ai/api-keys)\n\t\t2. Sign in to your account\n\t\t3. Navigate to API Keys section\n\t\t4. Create a new API key or copy an existing one\n\t\t5. Paste the key here\n\t": "\n\t\tIntroduzca su clave API Airtop. Puede obtener su clave API desde el [Panel de control Airtop](https://portal.airtop.ai/api-keys).\n\t\t\n\t\t**Cómo obtener tu clave API:**\n\t\t1. Ve al [Tablero de Airtop](https://portal.airtop.ai/api-keys)\n\t\t2. Inicia sesión en tu cuenta\n\t\t3. Navegue a la sección de claves API\n\t\t4. Cree una nueva clave API o copie una existente\n\t\t5. Pegue la clave aquí\n\t", "Create Session": "<PERSON><PERSON><PERSON>", "Terminate Session": "<PERSON>rm<PERSON>r <PERSON>", "Create New Browser Window": "Crear nueva ventana del navegador", "Take Screenshot": "Captura de pantalla", "Page Query": "Consulta de página", "Smart Scrape": "Raspe inteligente", "Paginated Extraction": "Extracción paginada", "Click": "Click", "Type": "Tipo", "Upload File to Sessions": "Subir archivo a sesiones", "Hover on an Element": "Aplique sobre un elemento", "Custom API Call": "Llamada API personalizada", "Starts a new browser session in Airtop.": "Inicia una nueva sesión de navegador en Airtop.", "Ends an existing browser session in Airtop.": "Termina una sesión de navegador existente en Airtop.", "Opens a new window within a session, optionally navigating to a URL.": "Abre una nueva ventana dentro de una sesión, opcionalmente navegando a una URL.", "Captures a screenshot of the current window.": "Captura una captura de pantalla de la ventana actual.", "Query a page to extract data or ask a question given the data on the page.": "Consulta una página para extraer datos o hacer una pregunta dada los datos de la página.", "Scrape a page and return the data as Markdown.": "Raspe una página y devuelve los datos como Markdown.", "Extract content from paginated or dynamically loaded pages.": "Extraer contenido de páginas paginadas o cargadas dinámicamente.", "Execute a click interaction in a specific browser window.": "Ejecutar una interacción de clic en una ventana específica del navegador.", "Type into a browser window at the specified field.": "Escriba en una ventana del navegador en el campo especificado.", "Push an existing file to one or more sessions, making it available for use in file inputs or downloads.": "Enviar un archivo existente a una o más sesiones, haciéndolo disponible para su uso en entradas o descargas de archivos.", "Moves mouse pointer over an element in the browser window.": "Mueve el puntero del ratón sobre un elemento en la ventana del navegador.", "Make a custom API call to a specific endpoint": "Hacer una llamada API personalizada a un extremo específico", "Profile Name": "Nombre de perfil", "Extension IDs": "IDs de extensión", "Use Airtop Proxy?": "¿Usar proxy de Airtop?", "Custom Proxy Configuration": "Configuración de proxy personalizado", "Advanced Proxy Settings": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> de proxy", "Solve Captcha": "Resolver Captcha", "Session Timeout (minutes)": "Tiempo de espera de sesión (minutos)", "Session": "Sesión", "Initial URL": "URL inicial", "Screen Resolution": "Resolución de pantalla", "Custom Resolution": "Resolución personalizada", "Page Load Strategy": "Estrategia de carga de página", "Page Load Timeout (seconds)": "Tiempo de espera de carga de página (segundos)", "Window": "Ventana", "Client Request ID": "ID de Solicitud de Cliente", "Screenshot Format": "Formato de captura de pantalla", "Screenshot Scope": "Alcance de captura", "Max Height (pixels)": "Altura máxima (píxeles)", "Max Width (pixels)": "<PERSON><PERSON> (píxeles)", "JPEG Quality (1-100)": "Calidad JPEG (1-100)", "Enable Advanced Visual Analysis": "Activar análisis visual avanzado", "Visual Analysis Settings": "Ajustes de análisis visual", "Maximum Credits to Spend": "Máximos créditos a gastar", "Maximum Time (seconds)": "Tiempo máximo (segundos)", "Prompt": "Petición", "Output Schema (JSON)": "Esquema de salida (JSON)", "Visual Analysis": "<PERSON><PERSON><PERSON><PERSON> visual", "Optimize URLs": "Optimizar URLs", "Maximum Time (Seconds)": "Tiempo máximo (segundos)", "Follow Pagination Links": "<PERSON><PERSON><PERSON> enlaces de paginación", "Scroll Within": "<PERSON><PERSON><PERSON><PERSON>", "How to Load More Content": "Cómo cargar más contenido", "Speed vs Accuracy": "Velocidad vs Precisión", "Element Description": "Descripción del elemento", "Click Type": "Tipo de clic", "Page Analysis Scope": "Ámbito de análisis de página", "Result Selection Strategy": "Estrategia de selección de resultados", "Partition Direction": "Dirección de la partición", "Maximum Scan Scrolls": "Pergamino máximo de escaneo", "Scan Scroll Delay (ms)": "Escanear retardo de desplazam<PERSON>o (ms)", "Overlap Percentage": "Porcentaje de superposición", "Wait for Navigation": "Espere a Navegación", "Navigation Wait Until": "Espera Navegación hasta", "Navigation Timeout (Seconds)": "Tiempo de navegación agotado (segundos)", "Text to Type": "Texto a escribir", "Clear Input Field Before Typing": "Borrar campo de entrada antes de escribir", "Press Enter After Typing": "Pulse Entrar después de escribir", "Press Tab After Typing": "Pulse la pestaña después de escribir", "Wait for Navigation After Typing": "Esperar a Navegación Después de Typing", "Navigation Wait Strategy": "Estrategia de Navegación de Espera", "Max Scrolls (Scan Mode)": "<PERSON><PERSON><PERSON> (Modo Escanear)", "Chunk Overlap (%)": "Superposición de trozo (%)", "Scroll Delay (ms)": "Retraso de desplazamiento (ms)", "Max Credits to Spend": "Créditos máximos a gastar", "Max Time to Wait (Seconds)": "Tiempo máximo de espera (segundos)", "File": "Archivo", "Session IDs": "ID de sesión", "Method": "<PERSON><PERSON><PERSON><PERSON>", "Headers": "Encabezados", "Query Parameters": "Parámetros de consulta", "Body": "<PERSON><PERSON><PERSON>", "No Error on Failure": "No hay ningún error en fallo", "Timeout (in seconds)": "Tiempo de espera (en segundos)", "Name of a profile to load into the session.": "Nombre de un perfil a cargar en la sesión.", "List of Chrome extension IDs from Google Web Store.": "Lista de ID de extensión Chrome de Google Web Store.", "Enable Airtop-provided proxy. If disabled, configure a custom proxy.": "Habilitar proxy Airtop. Si está deshabilitado, configure un proxy personalizado.", "Automatically solve captcha challenges.": "Resolver automáticamente los desafíos captcha.", "How long before the session times out due to inactivity (1-10080 minutes). Default: 10.": "<PERSON><PERSON><PERSON><PERSON> tiempo antes de que la sesión termine debido a inactividad (1-10080 minutos). <PERSON><PERSON> defecto: 10.", "Select an active Airtop session to use for browser automation": "Seleccione una sesión activa de Airtop para la automatización del navegador", "URL to open in the new window. Default: https://www.google.com": "URL a abrir en la nueva ventana. Por defecto: https://www.google.com", "Fixed dimensions for the browser window. Affects live view size.": "Dimensiones fijas para la ventana del navegador. Afecta al tamaño de la vista en vivo.", "Custom resolution in format \"widthxheight\" (e.g., \"1440x900\"). Leave blank to use selected resolution above.": "Resolución personalizada en formato \"widthxheight\" (por ejemplo, \"1440x900\"). Deje en blanco para usar la resolución seleccionada arriba.", "When to consider the page loaded. Default: load": "<PERSON><PERSON><PERSON><PERSON> considerar la página cargada. Por defecto: carga", "Maximum time to wait for page loading. Default: 30 seconds": "Tiempo máximo de espera para la carga de la página. Por defecto: 30 segundos", "Select a browser window within the chosen session": "Seleccione una ventana del navegador dentro de la sesión seleccionada", "Optional ID for tracking this request": "ID opcional para rastrear esta solicitud", "How to return the screenshot. Default: base64 for viewport, url for page/scan": "Cómo devolver la captura de pantalla. Por defecto: base64 para viewport, url para pagina/escaneo", "What part of the page to capture. Default: auto": "Qué parte de la página para capturar. Por defecto: auto", "Maximum height of screenshot. Will scale down if needed, preserving aspect ratio.": "Altura máxima de la captura de pantalla. Reducirá si es necesario, preservando la relación de aspecto.", "Maximum width of screenshot. Will scale down if needed, preserving aspect ratio.": "<PERSON>cho m<PERSON>ximo de la captura de pantalla. Reducirá si es necesario, preservando la relación de aspecto.", "Image quality for JPEG compression. Higher = better quality. Note: Feature in development.": "Calidad de imagen para compresión JPEG. Más alta = mejor calidad. Nota: Característica en desarrollo.", "Enable advanced visual analysis features for better page processing": "Habilitar características avanzadas de análisis visual para un mejor procesamiento de página", "Stop screenshot if it costs more than this. Leave blank for default limit.": "Detener captura de pantalla si cuesta más de esto. Dejar en blanco para el límite predeterminado.", "Stop screenshot if it takes longer than this. Leave blank for default timeout.": "Detener captura de pantalla si tarda más de esto. Dejar en blanco para el tiempo de espera predeterminado.", "The question or instruction for Airtop to answer about the current page.": "La pregunta o instrucciones para que Airtop responda acerca de la página actual.", "An optional ID for your internal tracking.": "Un ID opcional para su seguimiento interno.", "JSON schema defining the structure of the output. Must be valid JSON schema format.": "Esquema JSON que define la estructura de la salida. Debe ser un formato JSON válido.", "Whether to include visual analysis of the page (default: auto)": "Si incluir el análisis visual de la página (por defecto: auto)", "Improve scraping performance by optimizing URLs (default: auto)": "Mejorar el rendimiento del raspado optimizando URLs (por defecto: auto)", "Abort if the credit cost exceeds this amount. Set to 0 to disable.": "Cancelar si el coste del crédito supera esta cantidad. Establecer a 0 para desactivar.", "Abort if the operation takes longer than this. Set to 0 to disable.": "Cancelar si la operación tarda más de esto. Establecer en 0 para desactivar.", "If enabled, Airtop will attempt to load more content from pagination, scrolling, etc. (default: false)": "Si está activado, Airtop intentará cargar más contenido de la paginación, desplazamiento, etc. (por defecto: falso)", "Optional ID to track this request on your end.": "ID opcional para rastrear esta solicitud al final.", "Instructions on what to extract and how to paginate (e.g. \"Navigate through 3 pages and extract titles and prices\").": "Instrucciones sobre qué extraer y cómo paginar (por ejemplo, \"Navegar a través de 3 páginas y extraer títulos y precios\").", "Optional ID to track this request.": "ID opcional para rastrear esta solicitud.", "Describe the scrollable area (e.g. \"results container in middle of page\").": "Describa el área desplazable (por ejemplo, \"contenedor de resultados en el centro de la página\").", "Choose how to navigate through pages (default: auto)": "Elegir cómo navegar a través de páginas (por defecto: auto)", "Balance between speed and accuracy (default: auto)": "Balance entre velocidad y precisión (por defecto: auto)", "Describe the element to click (e.g. \"Login button\").": "Describa el elemento a pulsar (p.e. \"Botón de conexión\").", "The type of click to perform (default: left click).": "El tipo de clic a realizar (por defecto: clic izquierdo).", "Describe the scrollable area to search within (e.g. \"main content area\").": "Describa el área desplazable en la que buscar (por ejemplo, \"área principal de contenido\").", "Controls how much of the page is visually analyzed (default: auto).": "Controla cuánto de la página se analiza visualmente (por defecto: auto).", "How to select from multiple matches (default: auto).": "Cómo seleccionar de múltiples coincidencias (por defecto: auto).", "How to partition screenshots for analysis (default: vertical).": "Cómo particionar las capturas de pantalla para el análisis (por defecto: vertical).", "Maximum number of scrolls in scan mode (default: 50).": "Número máximo de desplazamientos en modo de escaneo (por defecto: 50).", "Delay between scrolls in scan mode (default: 1000ms).": "<PERSON><PERSON><PERSON> entre desplazamientos en modo escaneo (por defecto: 1000ms).", "Percentage of overlap between screenshot chunks (default: 30).": "Porcentaje de superposición entre fragmentos de captura de pantalla (por defecto: 30).", "Wait for page navigation to complete after clicking (default: false).": "Espere a que la página de navegación se complete después de hacer clic (por defecto: falso).", "When to consider navigation complete (default: load).": "<PERSON><PERSON><PERSON><PERSON> considerar la navegación completa (por defecto: carga).", "Max seconds to wait for navigation (default: 30).": "Máximo de segundos para esperar a la navegación (por defecto: 30).", "The text to type into the browser window.": "El texto a escribir en la ventana del navegador.", "Describe the element (e.g., \"search box\", \"username field\").": "Describa el elemento (por ejemplo, \"caja de búsqueda\", \"campo nombre de usuario\").", "Clear the input field before typing text.": "Elimina el campo de entrada antes de escribir texto.", "Press Enter key after typing text.": "Pulse la tecla Enter después de escribir texto.", "Press Tab key after typing text (after Enter if both enabled).": "Pulse la tecla Tab después de escribir texto (después de Enter si ambos habilitados).", "Wait for page navigation to complete after typing (default: false).": "Espere a que la navegación de la página se complete después de escribir (por defecto: falso).", "Max time to wait for navigation after typing (default: 30).": "Tiempo máximo de espera para la navegación después de escribir (por defecto: 30).", "Condition to consider navigation complete (default: load).": "Condición para considerar la navegación completa (por defecto: carga).", "Controls how much of the page is analyzed to find the input (default: auto).": "Controla cuánto de la página se analiza para encontrar la entrada (por defecto: auto).", "Percentage of overlap between visual chunks (default: 30).": "Porcentaje de superposición entre fragmentos visuales (por defecto: 30).", "Direction to partition screenshots (default: vertical).": "Dirección a las capturas de pantalla de particiones (por defecto: vertical).", "Describe the scrollable container (e.g., \"table body\", \"product list\").": "Describa el contenedor desplazable (por ejemplo, \"cuerpo de la tabla\", \"lista de productos\").", "Optional request ID for traceability.": "ID de solicitud opcional para trazabilidad.", "Cancel if this limit is exceeded. Set 0 to disable.": "Cancelar si este límite es excedido. Establecer 0 para desactivar.", "Cancel if exceeded. Set 0 to disable.": "Cancelar si se excede. Establecer 0 para desactivar.", "Select a file that has been uploaded to Airtop": "Seleccione un archivo que se ha subido a Airtop", "Select one or more sessions to make the file available on. Leave empty to make available to all sessions.": "Seleccione una o más sesiones para que el archivo esté disponible. Deje en blanco para poner a disposición de todas las sesiones.", "Describe the element to hover, e.g. \"the search box input in the top right corner\".": "Describa el elemento a actualizar, por ejemplo, \"la entrada del cuadro de búsqueda en la esquina superior derecha\".", "Wait for page navigation to complete after hovering (default: false).": "Espere a que la navegación de la página se complete después de pasar el cursor (por defecto: falso).", "Authorization headers are injected automatically from your connection.": "Las cabeceras de autorización se inyectan automáticamente desde tu conexión.", "1280x720 (Default)": "1280x720 (por defecto)", "1920x1080": "1920 x 1080", "1366x768": "1366x768", "1024x768": "1024x768", "800x600": "800x600", "Load (Page + Assets) - Default": "Cargar (Página + Activos) - <PERSON><PERSON> defecto", "DOM Content Loaded": "Contenido DOM cargado", "Complete (Page + Iframes)": "Completa (Página + Iframes)", "No Wait (Return Immediately)": "<PERSON> Espera (Return Inmediatamente)", "Base64 Data (Default for Viewport)": "Datos Base64 (por defecto para el puerto de visualización)", "Download URL (Default for Page/Scan)": "URL de descarga (por defecto para página/escanear)", "Auto (Recommended)": "Auto (recomendado)", "Current View Only": "Solo vista actual", "Full Page": "Página completa", "Scan Mode (For Problem Pages)": "Modo de escaneo (para páginas de problemas)", "Auto (Default)": "Auto (por defecto)", "Enabled": "Activado", "Disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Click Next/Previous Links": "Haga clic en Siguiente/Anterior Enlaces", "Infinite Scroll": "Pergamino infinito", "Auto (Balanced)": "Auto (equilibrado)", "More Accurate (Slower)": "<PERSON>ás preciso (más lento)", "Faster (Less Accurate)": "<PERSON><PERSON> r<PERSON> (menos preciso)", "Left Click": "<PERSON><PERSON>", "Double Click": "Doble clic", "Right Click": "Clic derecho", "Scan Mode": "Modo de <PERSON>o", "Auto": "Auto", "First Match": "Primera partida", "Best Match": "Mejor partido", "Vertical": "Vertical", "Horizontal": "Horizontal", "Bidirectional": "Bidireccional", "load": "cargar", "domcontentloaded": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "networkidle0": "idle de red", "networkidle2": "idle2 de red", "Load (Default)": "<PERSON><PERSON> (por defecto)", "Network Idle 0": "Red inactiva 0", "Network Idle 2": "Red inactiva 2", "Viewport Only": "<PERSON><PERSON><PERSON> ver puerto", "Vertical (Default)": "Vertical (por defecto)", "GET": "RECOGER", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "BORRAR", "HEAD": "LIMPIO"}
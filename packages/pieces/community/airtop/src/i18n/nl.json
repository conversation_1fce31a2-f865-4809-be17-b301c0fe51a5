{"Airtop": "Airtop", "\n\t\tEnter your Airtop API key. You can get your API key from the [Airtop Dashboard](https://portal.airtop.ai/api-keys).\n\t\t\n\t\t**How to get your API key:**\n\t\t1. Go to the [Airtop Dashboard](https://portal.airtop.ai/api-keys)\n\t\t2. Sign in to your account\n\t\t3. Navigate to API Keys section\n\t\t4. Create a new API key or copy an existing one\n\t\t5. Paste the key here\n\t": "\n\t\t→enter your Airtop API key. You can get your API key from [Airtop Dashboard](https://portal.airtop.ai/api-keys).\n\t\t・\t\t\n\t\t½ **Hoe je je API-sleutel te verkrijgen:**\n\t\t・1. Ga naar het Airtop Dashboard](https://portal.airtop.ai/api-keys)\n\t\tgithub2. Meld u aan op uw account\n\t\t~~3. Navigeer naar de API Keys sectie\n\t\t・4. Maak een nieuwe API sleutel of kopieer een bestaande\n\t\tľ5. Plak de key hier\n\t", "Create Session": "<PERSON><PERSON>", "Terminate Session": "<PERSON><PERSON>", "Create New Browser Window": "<PERSON>euw browservenster maken", "Take Screenshot": "Maak Screenshot", "Page Query": "<PERSON><PERSON>a query", "Smart Scrape": "<PERSON>me scrape", "Paginated Extraction": "Geïntegreerde Extractie", "Click": "Click", "Type": "Type", "Upload File to Sessions": "Bestand uploaden naar sessies", "Hover on an Element": "Beweeg op een element", "Custom API Call": "Custom API Call", "Starts a new browser session in Airtop.": "Start een nieuwe browser sessie in Airtop.", "Ends an existing browser session in Airtop.": "Beëind<PERSON>t een bestaande browser sessie in Airtop.", "Opens a new window within a session, optionally navigating to a URL.": "Opent een nieuw venster binnen een sessie, eventueel navigeren naar een URL.", "Captures a screenshot of the current window.": "Maakt een schermafbeelding van het huidige venster.", "Query a page to extract data or ask a question given the data on the page.": "Vraag een pagina aan om gegevens uit te pakken of vraag te stellen met het oog op de gegevens op de pagina.", "Scrape a page and return the data as Markdown.": "Scrape een pagina en retourneer de gegevens als Markdown.", "Extract content from paginated or dynamically loaded pages.": "Inhoud extraheren uit gepagineerde of dynamisch geladen pagina's.", "Execute a click interaction in a specific browser window.": "<PERSON>en klik interactie uitvoeren in een specifiek browser venster.", "Type into a browser window at the specified field.": "Typ in een browservenster op het opgegeven veld.", "Push an existing file to one or more sessions, making it available for use in file inputs or downloads.": "<PERSON>uur een bestaand bestand naar een of meer sessies en maak het beschikbaar voor gebruik in bestandsinputs of downloads.", "Moves mouse pointer over an element in the browser window.": "Verplaatst de muisaanwijzer over een element in het browservenster.", "Make a custom API call to a specific endpoint": "Maak een aangepaste API call naar een specifiek eindpunt", "Profile Name": "<PERSON><PERSON>", "Extension IDs": "Extensie ID's", "Use Airtop Proxy?": "Airtop Proxy gebruiken?", "Custom Proxy Configuration": "Aangepaste Proxyconfiguratie", "Advanced Proxy Settings": "Geavanceerde Proxy Instellingen", "Solve Captcha": "<PERSON><PERSON>", "Session Timeout (minutes)": "Sessie time-out (minuten)", "Session": "<PERSON><PERSON>", "Initial URL": "Initiële URL", "Screen Resolution": "Schermresolutie", "Custom Resolution": "Aangepaste resolutie", "Page Load Strategy": "<PERSON><PERSON><PERSON> strategie", "Page Load Timeout (seconds)": "<PERSON> Load Timeout (seconden)", "Window": "<PERSON><PERSON><PERSON>", "Client Request ID": "<PERSON>lant aanvraag ID", "Screenshot Format": "Schermafbeelding formaat", "Screenshot Scope": "Schermafbeelding Toepassing", "Max Height (pixels)": "Maximale hoogte (pixels)", "Max Width (pixels)": "Maximale breedte (pixels)", "JPEG Quality (1-100)": "JPEG kwaliteit (1-100)", "Enable Advanced Visual Analysis": "Geavanceerde Visuele Analyse <PERSON>", "Visual Analysis Settings": "<PERSON><PERSON><PERSON><PERSON>", "Maximum Credits to Spend": "Maximum te besteden credits", "Maximum Time (seconds)": "Maximale tijd (seconden)", "Prompt": "Prompt", "Output Schema (JSON)": "Uit<PERSON><PERSON> (JSON)", "Visual Analysis": "Visuele analyse", "Optimize URLs": "Optimaliseer URLs", "Maximum Time (Seconds)": "Maximale tijd (seconden)", "Follow Pagination Links": "Volg paginering links", "Scroll Within": "<PERSON><PERSON> naar binnen", "How to Load More Content": "Hoe meer inhoud laden", "Speed vs Accuracy": "<PERSON><PERSON><PERSON><PERSON> vs nauw<PERSON>urigheid", "Element Description": "Beschrijving element", "Click Type": "Klik op Type", "Page Analysis Scope": "<PERSON><PERSON><PERSON><PERSON>", "Result Selection Strategy": "Resultaat selectie strategie", "Partition Direction": "Richting partitie", "Maximum Scan Scrolls": "Maximale Scan Sc<PERSON>", "Scan Scroll Delay (ms)": "<PERSON><PERSON> (ms)", "Overlap Percentage": "Overlap Percentage", "Wait for Navigation": "Wachten op navigatie", "Navigation Wait Until": "Navigatie Wacht tot", "Navigation Timeout (Seconds)": "Navigatie time-out (seconden)", "Text to Type": "Tekst om te typen", "Clear Input Field Before Typing": "Leeg invoerveld voor typen", "Press Enter After Typing": "Druk op Enter na typen", "Press Tab After Typing": "Druk op Tab Na het typen", "Wait for Navigation After Typing": "Wacht op navigatie na het typen", "Navigation Wait Strategy": "Navigatie wachtstrategie", "Max Scrolls (Scan Mode)": "<PERSON> (<PERSON><PERSON>)", "Chunk Overlap (%)": "Chunk Overzicht (%)", "Scroll Delay (ms)": "<PERSON><PERSON> Vertraging (ms)", "Max Credits to Spend": "Max credits om te besteden", "Max Time to Wait (Seconds)": "<PERSON> to<PERSON> (seconden)", "File": "Bestand", "Session IDs": "Sessie ID's", "Method": "<PERSON>e", "Headers": "Kopteksten", "Query Parameters": "Query parameters", "Body": "<PERSON><PERSON><PERSON>", "No Error on Failure": "<PERSON><PERSON> fout bij fout", "Timeout (in seconds)": "Time-out (in seconden)", "Name of a profile to load into the session.": "<PERSON><PERSON> van een profiel om te laden in de sessie.", "List of Chrome extension IDs from Google Web Store.": "<PERSON><PERSON><PERSON> van Chrome-extensie ID's van Google Web Store.", "Enable Airtop-provided proxy. If disabled, configure a custom proxy.": "Schakel Airtop-provided proxy in. Indien uitgeschakeld, configureer een aangepaste proxy.", "Automatically solve captcha challenges.": "Captcha automatisch oplossen.", "How long before the session times out due to inactivity (1-10080 minutes). Default: 10.": "Hoe lang voordat de sessie verloopt vanwege inactiviteit (1-10080 minuten). Standaard: 10.", "Select an active Airtop session to use for browser automation": "Selecteer een actieve Airtop sessie om te gebruiken voor browser automatisering", "URL to open in the new window. Default: https://www.google.com": "URL om te openen in het nieuwe venster. Standaard: https://www.google.com", "Fixed dimensions for the browser window. Affects live view size.": "Vaste afmetingen voor het browservenster. <PERSON> invlo<PERSON> is de live weergavegrootte.", "Custom resolution in format \"widthxheight\" (e.g., \"1440x900\"). Leave blank to use selected resolution above.": "Aangepaste resolutie in formaat \"widthxheight\" (bijv. \"1440x900\"). Laat leeg om de geselecteerde resolutie hierboven te gebruiken.", "When to consider the page loaded. Default: load": "Wanneer de pagina wordt bekeken. Standaard: laden", "Maximum time to wait for page loading. Default: 30 seconds": "Maximale tijd om te wachten voor het laden van pagina. Standaard: 30 seconden", "Select a browser window within the chosen session": "Selecteer een <PERSON><PERSON><PERSON> binnen de gekozen sessie", "Optional ID for tracking this request": "Optionele ID voor het bijhouden van dit verzoek", "How to return the screenshot. Default: base64 for viewport, url for page/scan": "Hoe de schermafbeelding terug te geven. Standaard: base64 voor viewport, url voor pagina/scan", "What part of the page to capture. Default: auto": "<PERSON>lk deel van de op te nemen pagina. Standaard: automatisch", "Maximum height of screenshot. Will scale down if needed, preserving aspect ratio.": "Maximale hoogte van schermafbeelding. Wordt indien nodig naar beneden geschaald en de hoogteverhouding bewaren.", "Maximum width of screenshot. Will scale down if needed, preserving aspect ratio.": "Maximale breedte van schermafbeelding. Wordt indien nodig verkleind en de hoogte-breedteverhouding bewaren.", "Image quality for JPEG compression. Higher = better quality. Note: Feature in development.": "Afbeeldingskwaliteit voor JPEG compressie. Hoger = betere kwaliteit. Opmerking: Functie in ontwikkeling.", "Enable advanced visual analysis features for better page processing": "<PERSON><PERSON><PERSON> gea<PERSON><PERSON>e visuele analyse-functies in voor een betere pagina-verwerking", "Stop screenshot if it costs more than this. Leave blank for default limit.": "Stop schermafbeelding als het meer dan dit kost. Laat leeg voor de standaard limiet.", "Stop screenshot if it takes longer than this. Leave blank for default timeout.": "Screenshot stoppen als het langer duurt dan dit. Laat leeg voor de standaard time-out.", "The question or instruction for Airtop to answer about the current page.": "<PERSON> vra<PERSON> of instructie voor Airtop om te antwoorden over de huidige pagina.", "An optional ID for your internal tracking.": "Een optionele ID voor uw interne tracking.", "JSON schema defining the structure of the output. Must be valid JSON schema format.": "JSON-schema dat de structuur van de output definieert. Moet een geldig JSON-schema formaat zijn.", "Whether to include visual analysis of the page (default: auto)": "Of de visuele analyse van de pagina moet worden opgenomen (standaard: auto)", "Improve scraping performance by optimizing URLs (default: auto)": "Betere scraping prestaties door het optimaliseren van URL's (standaard: auto)", "Abort if the credit cost exceeds this amount. Set to 0 to disable.": "Afbreken als de kredietkosten dit bedrag overschrijden. Stel in op 0 om uit te schakelen.", "Abort if the operation takes longer than this. Set to 0 to disable.": "Afbreken als de bewerking langer duurt dan deze. Zet op 0 om uit te schakelen.", "If enabled, Airtop will attempt to load more content from pagination, scrolling, etc. (default: false)": "<PERSON><PERSON> inges<PERSON>, zal Airtop proberen meer inhoud te laden vanuit paginering, scrolling, etc. (standaard: false)", "Optional ID to track this request on your end.": "Optionele ID om dit verzoek op uw einde te volgen.", "Instructions on what to extract and how to paginate (e.g. \"Navigate through 3 pages and extract titles and prices\").": "Instructies over wat moet worden uitgepakt en hoe te pagineren (bijvoorbeeld \"Navigeer door 3 pagina's en extracten van titels en prijzen\").", "Optional ID to track this request.": "Optionele ID om dit verzoek te volgen.", "Describe the scrollable area (e.g. \"results container in middle of page\").": "Beschrijf het scrollbare gebied (bijvoorbeeld \"resultaten container in het midden van de pagina\").", "Choose how to navigate through pages (default: auto)": "<PERSON>es hoe te navigeren door pagina's (standaard: auto)", "Balance between speed and accuracy (default: auto)": "<PERSON><PERSON> tussen snelheid en nauwkeurigheid (standaard: auto)", "Describe the element to click (e.g. \"Login button\").": "Beschrijf het te klikken element (bv. de \"Login knop\").", "The type of click to perform (default: left click).": "Het type klik om uit te voeren (standaard: linker klik).", "Describe the scrollable area to search within (e.g. \"main content area\").": "<PERSON><PERSON><PERSON><PERSON><PERSON> het te zoeken gebied (bijv. \"hoofdgebied van de in<PERSON>d\").", "Controls how much of the page is visually analyzed (default: auto).": "<PERSON><PERSON><PERSON><PERSON> hoeveel van de pagina visueel wordt geanalyseerd (standaard: auto).", "How to select from multiple matches (default: auto).": "Hoe te selecteren uit meerdere overeenkomsten (standaard: auto).", "How to partition screenshots for analysis (default: vertical).": "<PERSON><PERSON> screenshots te partiteren voor analyse (standaard: verticaal).", "Maximum number of scrolls in scan mode (default: 50).": "Maximum aantal scrolls in scanmodus (standaard: 50).", "Delay between scrolls in scan mode (default: 1000ms).": "Vertraging tussen scrolls in scanmodus (standaard: 1000ms).", "Percentage of overlap between screenshot chunks (default: 30).": "Percentage overlap tussen schermafbeelding chunks (standaard: 30).", "Wait for page navigation to complete after clicking (default: false).": "Wacht tot pagina navigatie is voltooid na het klikken (standaard: false).", "When to consider navigation complete (default: load).": "<PERSON><PERSON> om de navigatie compleet te overwegen (standaard: laden).", "Max seconds to wait for navigation (default: 30).": "<PERSON> seconden wachten op navigatie (standaard: 30).", "The text to type into the browser window.": "De tekst om in te typen in het browservenster.", "Describe the element (e.g., \"search box\", \"username field\").": "Beschrijf het element (bijv. \"zoekveld\", \"gebruikersnaam veld\").", "Clear the input field before typing text.": "Wis het invoerveld voor het typen van tekst.", "Press Enter key after typing text.": "Druk op Enter toets na het typen van tekst.", "Press Tab key after typing text (after Enter if both enabled).": "Druk op Tab toets na het typen van tekst (na Enter indien beide ingeschakeld).", "Wait for page navigation to complete after typing (default: false).": "Wacht tot pagina navigatie compleet is na het typen (standaard: false).", "Max time to wait for navigation after typing (default: 30).": "<PERSON> tijd om te wachten op navigatie na het typen (standaard: 30).", "Condition to consider navigation complete (default: load).": "Voorwaarde om de navigatie volledig te overwegen (standaard: laden).", "Controls how much of the page is analyzed to find the input (default: auto).": "<PERSON><PERSON><PERSON><PERSON> hoeveel van de pagina wordt geanalyseerd om de invoer te vinden (standaard: auto).", "Percentage of overlap between visual chunks (default: 30).": "Percentage van overlap tussen visuele segmenten (standaard: 30).", "Direction to partition screenshots (default: vertical).": "Richting naar partitie schermafbeeldingen (standaard: verticaal).", "Describe the scrollable container (e.g., \"table body\", \"product list\").": "Besch<PERSON>jf de scrollbare container (bijv. \"tabelbody\", \"productlijst\").", "Optional request ID for traceability.": "Optionele aanvraag ID voor traceerbaarheid.", "Cancel if this limit is exceeded. Set 0 to disable.": "Annuleren als deze limiet wordt overschreden. Stel 0 in om uit te schakelen.", "Cancel if exceeded. Set 0 to disable.": "Annuleren indien overschreden. Stel 0 in om uit te schakelen.", "Select a file that has been uploaded to Airtop": "Selecteer een bestand dat is geüpload naar Airtop", "Select one or more sessions to make the file available on. Leave empty to make available to all sessions.": "Selecteer een of meer sessies om het bestand beschikbaar te maken. Laat leeg om beschikbaar te maken voor alle sessies.", "Describe the element to hover, e.g. \"the search box input in the top right corner\".": "Beschrijf het element als hover, bijvoorbeeld \"het zoekvak invoer in de rechterbovenhoek\".", "Wait for page navigation to complete after hovering (default: false).": "Wacht tot de pagina navigatie compleet is na de muis (standaard: false).", "Authorization headers are injected automatically from your connection.": "Autorisatie headers worden automatisch geïnjecteerd vanuit uw verbinding.", "1280x720 (Default)": "1280x720 (standaard)", "1920x1080": "1920x1080", "1366x768": "1366x768", "1024x768": "1024x768", "800x600": "800x600", "Load (Page + Assets) - Default": "Laden (Pagina + <PERSON>sets) - Standaard", "DOM Content Loaded": "DOM-inhoud geladen", "Complete (Page + Iframes)": "Voltooi (Page + Iframes)", "No Wait (Return Immediately)": "<PERSON><PERSON> (Retourneer Onmiddellijk)", "Base64 Data (Default for Viewport)": "Base64 gegevens (Standaard voor Viewport)", "Download URL (Default for Page/Scan)": "Download URL (Standaard voor Page/Scann)", "Auto (Recommended)": "Auto (Aanbevolen)", "Current View Only": "Alleen huidige weergave", "Full Page": "Volledige pagina", "Scan Mode (For Problem Pages)": "Scan Mode (Voor Probleem Pagina's)", "Auto (Default)": "Automatisch (standaard)", "Enabled": "Ingeschakeld", "Disabled": "Uitgeschakeld", "Click Next/Previous Links": "Klik op volgende/vorige links", "Infinite Scroll": "Oneindige boekrol", "Auto (Balanced)": "Automatisch (Saldo)", "More Accurate (Slower)": "<PERSON><PERSON> (lang<PERSON>er)", "Faster (Less Accurate)": "<PERSON><PERSON><PERSON> (minder)", "Left Click": "Klik links", "Double Click": "Dubbele Klik", "Right Click": "<PERSON><PERSON> met rechts", "Scan Mode": "<PERSON><PERSON>", "Auto": "Automatisch", "First Match": "Eerste overeenkomst", "Best Match": "Beste overeen<PERSON>t", "Vertical": "Verticaal", "Horizontal": "Horizontaal", "Bidirectional": "Bidirectioneel", "load": "laden", "domcontentloaded": "domcontentload", "networkidle0": "netwerk0", "networkidle2": "netwerk2", "Load (Default)": "<PERSON><PERSON> (standaard)", "Network Idle 0": "Netwerk Idle 0", "Network Idle 2": "Netwerk Idle 2", "Viewport Only": "<PERSON><PERSON>", "Vertical (Default)": "Verticaal (standaard)", "GET": "KRIJG", "POST": "POSTE", "PATCH": "BEKIJK", "PUT": "PUT", "DELETE": "VERWIJDEREN", "HEAD": "HOOFD"}
{"Airtop": "Airtop", "\n\t\tEnter your Airtop API key. You can get your API key from the [Airtop Dashboard](https://portal.airtop.ai/api-keys).\n\t\t\n\t\t**How to get your API key:**\n\t\t1. Go to the [Airtop Dashboard](https://portal.airtop.ai/api-keys)\n\t\t2. Sign in to your account\n\t\t3. Navigate to API Keys section\n\t\t4. Create a new API key or copy an existing one\n\t\t5. Paste the key here\n\t": "\n\t\tGib deinen Airtop API-Schlüssel ein. Du kannst deinen API-Schlüssel über das [Airtop Dashboard](https://portal.airtop.ai/api-keys).\n\t\t\n\t\t**Wie Sie Ihren API-Schlüssel erhalten:**\n\t\t1. <PERSON><PERSON><PERSON> Sie zur [Airtop Dashboard](https://portal.airtop.ai/api-keys)\n\t\t2. Melden Sie sich bei Ihrem Konto\n\t\t3 an. Navigieren Sie zum Abschnitt\n\t\t4. Erstellen Sie einen neuen API-Schlüssel oder kopieren Sie einen vorhandenen\n\t\t5. Fügen Sie den Schlüssel hier\n\tein\t", "Create Session": "Sitzung erstellen", "Terminate Session": "<PERSON><PERSON><PERSON> beenden", "Create New Browser Window": "Neues Browserfenster erstellen", "Take Screenshot": "Screenshot machen", "Page Query": "Seitenabfrage", "Smart Scrape": "Smart Scrape", "Paginated Extraction": "Paginierte Extraktion", "Click": "Click", "Type": "<PERSON><PERSON>", "Upload File to Sessions": "<PERSON><PERSON> zu <PERSON>ngen hochladen", "Hover on an Element": "Hover auf einem Element", "Custom API Call": "Eigener API-Aufruf", "Starts a new browser session in Airtop.": "Startet eine neue Browsersitzung in Airtop.", "Ends an existing browser session in Airtop.": "<PERSON><PERSON> eine bestehende Browsersitzung in Airtop.", "Opens a new window within a session, optionally navigating to a URL.": "<PERSON><PERSON><PERSON> ein neues Fenster innerhalb einer Sitzung, wahlweise auf eine URL.", "Captures a screenshot of the current window.": "Erfasst einen Screenshot des aktuellen Fensters.", "Query a page to extract data or ask a question given the data on the page.": "Abfragen einer <PERSON>, um Daten zu extrahieren oder eine Frage zu stellen, die die Daten auf der Seite.", "Scrape a page and return the data as Markdown.": "Scrape eine Seite und gib die Daten als Markdown zurück.", "Extract content from paginated or dynamically loaded pages.": "Extrahieren Sie Inhalte aus paginierten oder dynamisch geladenen Seiten.", "Execute a click interaction in a specific browser window.": "Führen Sie eine Click-Interaktion in einem bestimmten Browserfenster aus.", "Type into a browser window at the specified field.": "Geben Sie im angegebenen Feld in ein Browserfenster ein.", "Push an existing file to one or more sessions, making it available for use in file inputs or downloads.": "Eine existierende Datei in eine oder mehrere Sessions schieben und zur Verwendung in Datei-Eingaben oder -Downloads bereitstellen.", "Moves mouse pointer over an element in the browser window.": "Bewegt den Mauszeiger über ein Element im Browserfenster.", "Make a custom API call to a specific endpoint": "Einen benutzerdefinierten API-Aufruf an einen bestimmten Endpunkt machen", "Profile Name": "Profilname", "Extension IDs": "Erweiterungs-IDs", "Use Airtop Proxy?": "Airtop Proxy verwenden?", "Custom Proxy Configuration": "Benutzerdefinierte Proxy-Konfiguration", "Advanced Proxy Settings": "Erweiterte Proxy-Einstellungen", "Solve Captcha": "<PERSON><PERSON>", "Session Timeout (minutes)": "Sitzungs-Timeout (Minuten)", "Session": "Sit<PERSON>ng", "Initial URL": "Initiale URL", "Screen Resolution": "Bildschirmauflösung", "Custom Resolution": "<PERSON><PERSON><PERSON>", "Page Load Strategy": "Seitenladestrategie", "Page Load Timeout (seconds)": "Seitenlade-Timeout (Sekunden)", "Window": "<PERSON><PERSON>", "Client Request ID": "Kundenanfrage-ID", "Screenshot Format": "Screenshot-Format", "Screenshot Scope": "Screenshot-<PERSON><PERSON><PERSON>", "Max Height (pixels)": "<PERSON><PERSON> (Pixel)", "Max Width (pixels)": "<PERSON><PERSON> (Pixel)", "JPEG Quality (1-100)": "JPEG-Qualität (1-100)", "Enable Advanced Visual Analysis": "Erweiterte Visuelle Analyse aktivieren", "Visual Analysis Settings": "Visuelle Analyseeinstellungen", "Maximum Credits to Spend": "Maximale Anzahl auszugebender Credits", "Maximum Time (seconds)": "Maximale Zeit (Sekunden)", "Prompt": "Prompt", "Output Schema (JSON)": "Ausgabeschema (JSON)", "Visual Analysis": "<PERSON><PERSON><PERSON><PERSON>", "Optimize URLs": "URLs optimieren", "Maximum Time (Seconds)": "Maximale Zeit (Sekunden)", "Follow Pagination Links": "Links zur Pagination folgen", "Scroll Within": "<PERSON>rolle innen", "How to Load More Content": "Wie man mehr Inhalte lädt", "Speed vs Accuracy": "Geschwindigkeit vs Genauigkeit", "Element Description": "Elementbeschreibung", "Click Type": "Klicktyp", "Page Analysis Scope": "Seitenanalysebereich", "Result Selection Strategy": "Ergebnisauswahlstrategie", "Partition Direction": "Partitionsrichtung", "Maximum Scan Scrolls": "Maximale Scan-Rollen", "Scan Scroll Delay (ms)": "Scan-Scroll-Verzögerung (ms)", "Overlap Percentage": "Prozentsatz Überlappung", "Wait for Navigation": "<PERSON>ten auf Navigation", "Navigation Wait Until": "Navigation warten bis", "Navigation Timeout (Seconds)": "Navigations-Timeout (Sekunden)", "Text to Type": "Text eingeben", "Clear Input Field Before Typing": "Eingabefeld vor Eingabe löschen", "Press Enter After Typing": "Drücken Sie Enter nach Eingabe", "Press Tab After Typing": "Nach Eingabe Tab drücken", "Wait for Navigation After Typing": "Warte auf Navigation nach Eingabe", "Navigation Wait Strategy": "Navigationswartungsstrategie", "Max Scrolls (Scan Mode)": "<PERSON><PERSON> (Scan-Modus)", "Chunk Overlap (%)": "Chunk-Überlappung (%)", "Scroll Delay (ms)": "Scroll-Verzögerung (ms)", "Max Credits to Spend": "<PERSON><PERSON> au<PERSON>", "Max Time to Wait (Seconds)": "Maximale Wartezeit (Sekunden)", "File": "<PERSON><PERSON>", "Session IDs": "Sitzungs-IDs", "Method": "<PERSON>e", "Headers": "Kopfzeilen", "Query Parameters": "Abfrageparameter", "Body": "<PERSON><PERSON><PERSON><PERSON>", "No Error on Failure": "<PERSON><PERSON> bei <PERSON>hler", "Timeout (in seconds)": "Timeout (in Sekunden)", "Name of a profile to load into the session.": "Name eines Profils, das in die Sitzung geladen werden soll.", "List of Chrome extension IDs from Google Web Store.": "Liste der Chrome-Erweiterungs-IDs von Google Web Store.", "Enable Airtop-provided proxy. If disabled, configure a custom proxy.": "Aktivieren Sie den Proxy mit Airtop. Falls deaktiviert, konfigurieren Sie einen benutzerdefinierten Proxy.", "Automatically solve captcha challenges.": "Captcha-Herausforderungen automatisch lösen.", "How long before the session times out due to inactivity (1-10080 minutes). Default: 10.": "Dauer vor Ablauf der Sitzung wegen Inaktivität (1-10080 Minuten). Standard: 10.", "Select an active Airtop session to use for browser automation": "Wählen Sie eine aktive Airtop Session für die Browser-Automatisierung", "URL to open in the new window. Default: https://www.google.com": "URL zum Öffnen im neuen Fenster. Standard: https://www.google.com", "Fixed dimensions for the browser window. Affects live view size.": "Feste Abmessungen für das Browserfenster. Beeinflusst die Größe der Live-Ansicht.", "Custom resolution in format \"widthxheight\" (e.g., \"1440x900\"). Leave blank to use selected resolution above.": "Benutzerdefinierte Auflösung im Format \"widthxheight\" (z.B. \"1440x900\"). <PERSON><PERSON> lassen um die ausgewählte Auflösung oben zu verwenden.", "When to consider the page loaded. Default: load": "Wann die Seite geladen ist. Standard: laden", "Maximum time to wait for page loading. Default: 30 seconds": "Maximale Wartezeit für das Laden der Seite. Standard: 30 Sekunden", "Select a browser window within the chosen session": "Wählen Si<PERSON> ein Browserfenster innerhalb der gewählten Sitzung", "Optional ID for tracking this request": "Optionale ID für die Verfolgung dieser Anfrage", "How to return the screenshot. Default: base64 for viewport, url for page/scan": "Wie der Screenshot zurückgegeben wird. Standard: base64 für Viewport, url für Seite/Scan", "What part of the page to capture. Default: auto": "Welcher Teil der Seite erfasst werden soll. Standard: automatisch", "Maximum height of screenshot. Will scale down if needed, preserving aspect ratio.": "Maximale Höhe des Screenshots. Wird bei <PERSON><PERSON><PERSON>, Seitenverhältnis beibehalten.", "Maximum width of screenshot. Will scale down if needed, preserving aspect ratio.": "Maximale Breite des Screenshots. Wird bei Bedar<PERSON>, Seitenverhältnis beibehalten.", "Image quality for JPEG compression. Higher = better quality. Note: Feature in development.": "Bildqualität für JPEG-Kompression. Höhere = bessere Qualität. Hinweis: Merkmal in der Entwicklung.", "Enable advanced visual analysis features for better page processing": "Erweiterte visuelle Analysefunktionen für eine bessere Seitenverarbeitung aktivieren", "Stop screenshot if it costs more than this. Leave blank for default limit.": "Bildschirmfoto stoppen, wenn dies mehr kostet. <PERSON>r lassen für Standardlimit.", "Stop screenshot if it takes longer than this. Leave blank for default timeout.": "Screenshot stoppen, wenn dies länger dauert. <PERSON>r lassen für Standard-Timeout.", "The question or instruction for Airtop to answer about the current page.": "Die Frage oder Anweisung an Airtop über die aktuelle Seite zu beantworten.", "An optional ID for your internal tracking.": "Eine optionale ID für Ihr internes Tracking.", "JSON schema defining the structure of the output. Must be valid JSON schema format.": "JSON-<PERSON><PERSON><PERSON>, das die Struktur der Ausgabe definiert. Es muss ein gültiges JSON-Schema-Format sein.", "Whether to include visual analysis of the page (default: auto)": "G<PERSON><PERSON> an, ob die visuelle Analyse der Seite aufgenommen werden soll (Standard: auto)", "Improve scraping performance by optimizing URLs (default: auto)": "Verbessern Sie die Scraping-Leistung durch Optimierung der URLs (Standard: auto)", "Abort if the credit cost exceeds this amount. Set to 0 to disable.": "Abbre<PERSON>, wenn die Kreditkosten diesen Betrag überschreiten. Auf 0 setzen um zu deaktivieren.", "Abort if the operation takes longer than this. Set to 0 to disable.": "<PERSON><PERSON><PERSON><PERSON>, wenn die Operation länger dauert. Setzen Sie 0 auf deaktivieren.", "If enabled, Airtop will attempt to load more content from pagination, scrolling, etc. (default: false)": "<PERSON><PERSON> a<PERSON><PERSON>, wird <PERSON> ve<PERSON>, mehr Inhalte von Se<PERSON>n zu laden, scrollen, etc. (Standard: falsch)", "Optional ID to track this request on your end.": "Optionale ID, um diese Anfrage an Ihrem Ende zu verfolgen.", "Instructions on what to extract and how to paginate (e.g. \"Navigate through 3 pages and extract titles and prices\").": "Anleit<PERSON>, was extrahiert werden soll und wie paginiert werden soll (z.B. \"Navigiere durch 3 Seiten und entpacke Titel und Preise\").", "Optional ID to track this request.": "Optionale ID, um diese Anfrage zu verfolgen.", "Describe the scrollable area (e.g. \"results container in middle of page\").": "Beschreiben Sie den scrollbaren Bereich (z.B. \"Ergebniscontainer in der Mitte der Seite\").", "Choose how to navigate through pages (default: auto)": "<PERSON><PERSON><PERSON><PERSON>, wie Sie durch Seiten navigieren sollen (Standard: Auto)", "Balance between speed and accuracy (default: auto)": "Balance zwischen Geschwindigkeit und Genauigkeit (Standard: Auto)", "Describe the element to click (e.g. \"Login button\").": "Beschreiben Sie das Element zum Klicken (z.B. \"Anmelden\").", "The type of click to perform (default: left click).": "Der Typ des auszuführenden Klicks (Standard: Linksklick).", "Describe the scrollable area to search within (e.g. \"main content area\").": "Beschreiben Sie den scrollbaren Bereich zur Suche (z.B. \"Hauptinhaltsbereich\").", "Controls how much of the page is visually analyzed (default: auto).": "Legt fest, wie viel der Seite visuell analysiert wird (Standard: auto).", "How to select from multiple matches (default: auto).": "Wie man aus mehreren Treffer auswählen kann (Standard: auto).", "How to partition screenshots for analysis (default: vertical).": "Wie Partition Screenshots für die Analyse (Standard: vertikal).", "Maximum number of scrolls in scan mode (default: 50).": "Maximale Anzahl der Scrolls im Scanmodus (Standard: 50).", "Delay between scrolls in scan mode (default: 1000ms).": "Verzögerung zwischen Scrollen im Scanmodus (Standard: 1000ms).", "Percentage of overlap between screenshot chunks (default: 30).": "Prozentsatz der Überlappung zwischen Screenshot-Chunks (Standard: 30).", "Wait for page navigation to complete after clicking (default: false).": "<PERSON><PERSON> Si<PERSON>, bis die Seitennavigation nach einem Klick abgeschlossen ist (Standard: falsch).", "When to consider navigation complete (default: load).": "Wenn die Navigation abgeschlossen ist (Standard: Laden).", "Max seconds to wait for navigation (default: 30).": "Maximale Sekunden warten auf die Navigation (Standard: 30).", "The text to type into the browser window.": "Der Text, der in das Browserfenster eingegeben werden soll.", "Describe the element (e.g., \"search box\", \"username field\").": "Beschreiben Sie das Element (z.B. \"search box\", \"username field\").", "Clear the input field before typing text.": "<PERSON><PERSON> das Eingabefeld, bevor <PERSON> eingeben.", "Press Enter key after typing text.": "Drücken Sie die Eingabetaste, nachdem Sie Text eingegeben haben.", "Press Tab key after typing text (after Enter if both enabled).": "Drücken Sie die Tab-Taste nach Eingabe des Textes (nach Enter wenn beide aktiviert sind).", "Wait for page navigation to complete after typing (default: false).": "<PERSON><PERSON> Si<PERSON>, bis die Seitennavigation nach dem Tippen abgeschlossen ist (Standard: falsch).", "Max time to wait for navigation after typing (default: 30).": "Maximale Wartezeit nach Eingabe der Navigation (Standard: 30).", "Condition to consider navigation complete (default: load).": "<PERSON><PERSON><PERSON>, dass die Navigation abgeschlossen ist (Standard: Laden).", "Controls how much of the page is analyzed to find the input (default: auto).": "Legt fest, wie viel der Seite analysiert wird, um die Eingabe zu finden (Standard: auto).", "Percentage of overlap between visual chunks (default: 30).": "Prozentsatz der Überlappung zwischen visuellen Chunks (Standard: 30).", "Direction to partition screenshots (default: vertical).": "Richtung der Partition Screenshots (Standard: vertikal).", "Describe the scrollable container (e.g., \"table body\", \"product list\").": "Beschreiben Sie den scrollbaren Container (z.B. \"table body\", \"product list\").", "Optional request ID for traceability.": "Optionale Request-ID für Rückverfolgbarkeit.", "Cancel if this limit is exceeded. Set 0 to disable.": "<PERSON><PERSON><PERSON><PERSON>, wenn dieses Limit überschritten wurde. Setzen Sie 0 zum Deaktivieren.", "Cancel if exceeded. Set 0 to disable.": "Abbrechen wenn überschritten. Setzen Sie 0 zum Deaktivieren.", "Select a file that has been uploaded to Airtop": "<PERSON><PERSON><PERSON><PERSON> Sie e<PERSON> Datei, die nach Airtop hochgeladen wurde", "Select one or more sessions to make the file available on. Leave empty to make available to all sessions.": "<PERSON><PERSON><PERSON>en Si<PERSON> eine oder mehrere Sitzungen, um die Datei verfügbar zu machen. <PERSON><PERSON>, um alle Sitzungen zur Verfügung zu stellen.", "Describe the element to hover, e.g. \"the search box input in the top right corner\".": "Beschreiben Sie das zu schwebende Element, z.B. \"das Suchfeld in der oberen rechten Ecke\".", "Wait for page navigation to complete after hovering (default: false).": "<PERSON><PERSON> Si<PERSON>, bis die Seitennavigation nach dem Schweben abgeschlossen ist (Standard: falsch).", "Authorization headers are injected automatically from your connection.": "Autorisierungs-Header werden automatisch von Ihrer Verbindung injiziert.", "1280x720 (Default)": "1280x720 (Standard)", "1920x1080": "1920 x 1080", "1366x768": "1366x768", "1024x768": "1024x768", "800x600": "800x600", "Load (Page + Assets) - Default": "Laden (Seite + Assets) - Standard", "DOM Content Loaded": "DOM Inhalt geladen", "Complete (Page + Iframes)": "Abschließen (Seite + Iframes)", "No Wait (Return Immediately)": "<PERSON><PERSON>ze<PERSON> (sofort zurückkehren)", "Base64 Data (Default for Viewport)": "Base64 Daten (Standard für Viewport)", "Download URL (Default for Page/Scan)": "Download-URL (Standard für Seite/Scan)", "Auto (Recommended)": "Auto (empfohlen)", "Current View Only": "Nur aktuelle Ansicht", "Full Page": "Vollseite", "Scan Mode (For Problem Pages)": "Scan-Modus (für Problemseiten)", "Auto (Default)": "Auto (Standard)", "Enabled": "Aktiviert", "Disabled": "Deaktiviert", "Click Next/Previous Links": "Klicken Sie Next/Vorherige Links", "Infinite Scroll": "Unendliche Schriftrolle", "Auto (Balanced)": "Auto (ausgeglichen)", "More Accurate (Slower)": "Mehr Genauigkeit (niedriger)", "Faster (Less Accurate)": "Schnellere (weniger Genauigkeit)", "Left Click": "Linksklick", "Double Click": "<PERSON><PERSON>k<PERSON>", "Right Click": "Rechtsklick", "Scan Mode": "Scan-Modus", "Auto": "Auto", "First Match": "<PERSON><PERSON><PERSON>", "Best Match": "Bestes Match", "Vertical": "Vertikal", "Horizontal": "Horizontal", "Bidirectional": "Bidirektional", "load": "laden", "domcontentloaded": "domcontentgeladen", "networkidle0": "networkidle0", "networkidle2": "networkidle2", "Load (Default)": "<PERSON><PERSON> (Standard)", "Network Idle 0": "Netzwerk Leerlauf 0", "Network Idle 2": "Netzwerk Leerlauf 2", "Viewport Only": "Nur Viewport", "Vertical (Default)": "<PERSON><PERSON><PERSON><PERSON> (Standard)", "GET": "ERHALTEN", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "LÖSCHEN", "HEAD": "HEAD"}
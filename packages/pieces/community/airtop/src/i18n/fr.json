{"Airtop": "Airtop", "\n\t\tEnter your Airtop API key. You can get your API key from the [Airtop Dashboard](https://portal.airtop.ai/api-keys).\n\t\t\n\t\t**How to get your API key:**\n\t\t1. Go to the [Airtop Dashboard](https://portal.airtop.ai/api-keys)\n\t\t2. Sign in to your account\n\t\t3. Navigate to API Keys section\n\t\t4. Create a new API key or copy an existing one\n\t\t5. Paste the key here\n\t": "\n\t\tEnter your Airtop API key. You can get your API key from the [Airtop Dashboard](https://portal.airtop.ai/api-keys).\n\t\t\n\t\t**How to get your API key:**\n\t\t1. Go to the [Airtop Dashboard](https://portal.airtop.ai/api-keys)\n\t\t2. Sign in to your account\n\t\t3. Navigate to API Keys section\n\t\t4. Create a new API key or copy an existing one\n\t\t5. Paste the key here\n\t", "Create Session": "<PERSON><PERSON><PERSON> une session", "Terminate Session": "Te<PERSON><PERSON> la session", "Create New Browser Window": "Créer une nouvelle fenêtre de navigation", "Take Screenshot": "<PERSON><PERSON><PERSON> une capture d'é<PERSON>ran", "Page Query": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Smart Scrape": "Scrape Intelligent", "Paginated Extraction": "Extraction paginée", "Click": "Click", "Type": "Type de texte", "Upload File to Sessions": "Télécharger le fichier dans les sessions", "Hover on an Element": "Survoler sur un élément", "Custom API Call": "Appel API personnalisé", "Starts a new browser session in Airtop.": "D<PERSON><PERSON>re une nouvelle session de navigateur dans Airtop.", "Ends an existing browser session in Airtop.": "Termine une session de navigateur existante dans Airtop.", "Opens a new window within a session, optionally navigating to a URL.": "Ouvre une nouvelle fenêtre dans une session en naviguant éventuellement vers une URL.", "Captures a screenshot of the current window.": "Capture une capture d'écran de la fenêtre actuelle.", "Query a page to extract data or ask a question given the data on the page.": "Interroger une page pour extraire des données ou poser une question donnée par les données sur la page.", "Scrape a page and return the data as Markdown.": "Scrape une page et retourne les données en tant que Markdown.", "Extract content from paginated or dynamically loaded pages.": "Extraire le contenu des pages paginées ou chargées dynamiquement.", "Execute a click interaction in a specific browser window.": "Exécuter une interaction de clic dans une fenêtre de navigateur spécifique.", "Type into a browser window at the specified field.": "<PERSON><PERSON><PERSON> dans une fenêtre du navigateur dans le champ spécifié.", "Push an existing file to one or more sessions, making it available for use in file inputs or downloads.": "<PERSON>ussez un fichier existant vers une ou plusieurs sessions, en le rendant disponible dans les entrées ou les téléchargements de fichiers.", "Moves mouse pointer over an element in the browser window.": "<PERSON><PERSON><PERSON> le pointeur de la souris sur un élément de la fenêtre du navigateur.", "Make a custom API call to a specific endpoint": "Passez un appel API personnalisé à un point de terminaison spécifique", "Profile Name": "Nom du profil", "Extension IDs": "IDs d'extension", "Use Airtop Proxy?": "Utiliser le proxy Airtop ?", "Custom Proxy Configuration": "Configuration du proxy personnalisé", "Advanced Proxy Settings": "Paramètres avancés du proxy", "Solve Captcha": "Résoudre le Captcha", "Session Timeout (minutes)": "<PERSON><PERSON><PERSON> (minutes)", "Session": "<PERSON><PERSON><PERSON>", "Initial URL": "URL initiale", "Screen Resolution": "Résolution de l'écran", "Custom Resolution": "Résolution personnalisée", "Page Load Strategy": "Stratégie de chargement de page", "Page Load Timeout (seconds)": "<PERSON><PERSON><PERSON> de chargement de la page (secondes)", "Window": "<PERSON><PERSON><PERSON>", "Client Request ID": "ID de la requête du client", "Screenshot Format": "Format de capture d'écran", "Screenshot Scope": "Portée de la capture d'écran", "Max Height (pixels)": "Hauteur max (pixels)", "Max Width (pixels)": "Largeur max (pixels)", "JPEG Quality (1-100)": "Qualité JPEG (1-100)", "Enable Advanced Visual Analysis": "Activer l'analyse visuelle avancée", "Visual Analysis Settings": "Paramètres d'analyse visuelle", "Maximum Credits to Spend": "Nombre maximum de crédits à dépenser", "Maximum Time (seconds)": "Temps maximum (secondes)", "Prompt": "Prompt", "Output Schema (JSON)": "<PERSON><PERSON><PERSON><PERSON> de sortie (JSON)", "Visual Analysis": "<PERSON><PERSON><PERSON> visuelle", "Optimize URLs": "Optimiser les URL", "Maximum Time (Seconds)": "Temps maximum (secondes)", "Follow Pagination Links": "Suivre les liens de pagination", "Scroll Within": "Défiler à l'intérieur", "How to Load More Content": "Comment charger plus de contenu", "Speed vs Accuracy": "Vitesse vs Précision", "Element Description": "Description de l'élément", "Click Type": "Type de clic", "Page Analysis Scope": "Portée d'analyse de la page", "Result Selection Strategy": "Stratégie de sélection des résultats", "Partition Direction": "Direction de la partition", "Maximum Scan Scrolls": "Défilement de balayage maximum", "Scan Scroll Delay (ms)": "<PERSON><PERSON><PERSON> du <PERSON> (ms)", "Overlap Percentage": "Pourcentage de chevauchement", "Wait for Navigation": "Attendre la navigation", "Navigation Wait Until": "Attendre la navigation jusqu'à", "Navigation Timeout (Seconds)": "<PERSON><PERSON><PERSON> (secondes)", "Text to Type": "Texte à Type", "Clear Input Field Before Typing": "Effacer le champ de saisie avant de taper", "Press Enter After Typing": "Appuyez sur Entrée après la saisie", "Press Tab After Typing": "Appuyez sur Tab après avoir tapé", "Wait for Navigation After Typing": "Attendre la navigation après la frappe", "Navigation Wait Strategy": "Stratégie de navigation en attente", "Max Scrolls (Scan Mode)": "Défilement max (mode scan)", "Chunk Overlap (%)": "Chevauchement de tronçons (%)", "Scroll Delay (ms)": "<PERSON><PERSON><PERSON> (ms)", "Max Credits to Spend": "Nombre maximum de crédits à dépenser", "Max Time to Wait (Seconds)": "Temps maximum d'attente (secondes)", "File": "<PERSON><PERSON>", "Session IDs": "ID de session", "Method": "Méthode", "Headers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Query Parameters": "Paramètres de requête", "Body": "Corps", "No Error on Failure": "Aucune erreur en cas d'échec", "Timeout (in seconds)": "<PERSON><PERSON><PERSON>atten<PERSON> (en secondes)", "Name of a profile to load into the session.": "Nom du profil à charger dans la session.", "List of Chrome extension IDs from Google Web Store.": "Liste des ID d'extension Chrome de Google Web Store.", "Enable Airtop-provided proxy. If disabled, configure a custom proxy.": "Activer le proxy fourni par Airtop. <PERSON>és<PERSON>, configurer un proxy personnalisé.", "Automatically solve captcha challenges.": "Résoudre automatiquement les défis du captcha.", "How long before the session times out due to inactivity (1-10080 minutes). Default: 10.": "Combien de temps avant la sortie de la session en raison de l'inactivité (1-10080 minutes). Par d<PERSON><PERSON>ut: 10.", "Select an active Airtop session to use for browser automation": "Sélectionnez une session Airtop active à utiliser pour l'automatisation du navigateur", "URL to open in the new window. Default: https://www.google.com": "URL à ouvrir dans la nouvelle fenêtre. Par défaut: https://www.google.com", "Fixed dimensions for the browser window. Affects live view size.": "Dimensions fixes pour la fenêtre du navigateur. Affecte la taille de la vue live.", "Custom resolution in format \"widthxheight\" (e.g., \"1440x900\"). Leave blank to use selected resolution above.": "Résolution personnalisée au format \"widthxheight\" (par exemple, \"1440x900\"). Laisser vide pour utiliser la résolution sélectionnée ci-dessus.", "When to consider the page loaded. Default: load": "Quand consid<PERSON>rer la page chargée. Par défaut: charger", "Maximum time to wait for page loading. Default: 30 seconds": "Temps d'attente maximum pour le chargement de la page. Pa<PERSON> <PERSON><PERSON><PERSON><PERSON>: 30 secondes", "Select a browser window within the chosen session": "Sélectionnez une fenêtre de navigateur dans la session choisie", "Optional ID for tracking this request": "ID facultatif pour le suivi de cette requête", "How to return the screenshot. Default: base64 for viewport, url for page/scan": "Comment retourner la capture d'écran. Par défaut: base64 pour le port d'affichage, url pour la page/scan", "What part of the page to capture. Default: auto": "Quelle partie de la page à capturer. Par défaut: auto", "Maximum height of screenshot. Will scale down if needed, preserving aspect ratio.": "Hauteur maximale de la capture d'écran. Diminuera la taille si nécessaire, en préservant le rapport d'aspect.", "Maximum width of screenshot. Will scale down if needed, preserving aspect ratio.": "Largeur maximale de la capture d'écran. Diminuera si nécessaire, en préservant le rapport d'aspect.", "Image quality for JPEG compression. Higher = better quality. Note: Feature in development.": "Qualité d'image pour la compression JPEG. Plus élevé = meilleure qualité. Note : Fonctionnalité en développement.", "Enable advanced visual analysis features for better page processing": "Activer les fonctionnalités avancées d'analyse visuelle pour un meilleur traitement des pages", "Stop screenshot if it costs more than this. Leave blank for default limit.": "Arrêter la capture d'écran si cela coûte plus cher. Laisser vide pour la limite par défaut.", "Stop screenshot if it takes longer than this. Leave blank for default timeout.": "Arrêter la capture d'écran si cela prend plus de temps. Laisser vide pour le délai par défaut.", "The question or instruction for Airtop to answer about the current page.": "La question ou les instructions pour que Airtop réponde à la page courante.", "An optional ID for your internal tracking.": "Un ID optionnel pour votre suivi interne.", "JSON schema defining the structure of the output. Must be valid JSON schema format.": "Schéma JSON définissant la structure de la sortie. Doit être un format de schéma JSON valide.", "Whether to include visual analysis of the page (default: auto)": "Inclure ou non l'analyse visuelle de la page (par défaut: auto)", "Improve scraping performance by optimizing URLs (default: auto)": "Améliore les performances de scraping en optimisant les URLs (par défaut: auto)", "Abort if the credit cost exceeds this amount. Set to 0 to disable.": "Annuler si le coût du crédit dépasse ce montant. Mettre à 0 pour désactiver.", "Abort if the operation takes longer than this. Set to 0 to disable.": "Interrompre si l'opération prend plus de temps. Réglez sur 0 pour désactiver.", "If enabled, Airtop will attempt to load more content from pagination, scrolling, etc. (default: false)": "Si activé, Airtop tentera de charger plus de contenu à partir de la pagination, du défilement, etc. (par défaut: false)", "Optional ID to track this request on your end.": "ID facultatif pour suivre cette requête à votre fin.", "Instructions on what to extract and how to paginate (e.g. \"Navigate through 3 pages and extract titles and prices\").": "Instructions sur ce qu'il faut extraire et comment paginer (par exemple, \"Naviguer à travers 3 pages et extraire les titres et les prix\").", "Optional ID to track this request.": "ID optionnel pour suivre cette requête.", "Describe the scrollable area (e.g. \"results container in middle of page\").": "Décrire la zone de défilement (par exemple \"conteneur de résultats au milieu de la page\").", "Choose how to navigate through pages (default: auto)": "Choisir comment naviguer à travers les pages (par défaut: auto)", "Balance between speed and accuracy (default: auto)": "Balance entre la vitesse et la précision (par défaut: auto)", "Describe the element to click (e.g. \"Login button\").": "Décrivez l'élément à cliquer (par exemple \"Bouton de connexion\").", "The type of click to perform (default: left click).": "Le type de clic à effectuer (par défaut: clic gauche).", "Describe the scrollable area to search within (e.g. \"main content area\").": "Décrivez la zone de défilement dans laquelle rechercher (par exemple \"zone de contenu principale\").", "Controls how much of the page is visually analyzed (default: auto).": "Contrôle la quantité de la page analysée visuellement (par défaut: auto).", "How to select from multiple matches (default: auto).": "Comment choisir parmi plusieurs correspondances (par défaut: auto).", "How to partition screenshots for analysis (default: vertical).": "Comment partitionner des captures d'écran pour l'analyse (par défaut: vertical).", "Maximum number of scrolls in scan mode (default: 50).": "Nombre maximum de défilements en mode scan (par défaut: 50).", "Delay between scrolls in scan mode (default: 1000ms).": "<PERSON><PERSON><PERSON> entre les défilements en mode scan (par défaut: 1000ms).", "Percentage of overlap between screenshot chunks (default: 30).": "Pourcentage de chevauchement entre les chunks de capture d'écran (par défaut: 30).", "Wait for page navigation to complete after clicking (default: false).": "Attendez que la navigation de la page soit terminée après avoir cliqué (par défaut: false).", "When to consider navigation complete (default: load).": "Quand considérer la navigation comme terminée (par défaut: chargement).", "Max seconds to wait for navigation (default: 30).": "Max secondes à attendre pour la navigation (par défaut: 30).", "The text to type into the browser window.": "Le texte à taper dans la fenêtre du navigateur.", "Describe the element (e.g., \"search box\", \"username field\").": "Décrivez l'élément (par exemple, \"case de recherche\", \"champ utilisateur\").", "Clear the input field before typing text.": "Effacer le champ de saisie avant de taper du texte.", "Press Enter key after typing text.": "Appuyez sur la touche Entrée après avoir tapé du texte.", "Press Tab key after typing text (after Enter if both enabled).": "Appuyez sur la touche Tab après avoir tapé du texte (après Entrée si les deux sont activés).", "Wait for page navigation to complete after typing (default: false).": "Attendez que la navigation de la page se termine après avoir tapé (par défaut: false).", "Max time to wait for navigation after typing (default: 30).": "Temps maximum pour attendre la navigation après avoir tapé (par défaut: 30).", "Condition to consider navigation complete (default: load).": "Condition à considérer la navigation complète (par défaut: chargement).", "Controls how much of the page is analyzed to find the input (default: auto).": "Contrôle la quantité de la page analysée pour trouver l'entrée (par défaut: auto).", "Percentage of overlap between visual chunks (default: 30).": "Pourcentage de chevauchement entre les chunks visuels (par défaut : 30).", "Direction to partition screenshots (default: vertical).": "Direction vers les captures d'écran de partition (par défaut: vertical).", "Describe the scrollable container (e.g., \"table body\", \"product list\").": "<PERSON><PERSON><PERSON><PERSON><PERSON> le conteneur scrollable (par exemple, \"corps du tableau\", \"liste des produits\").", "Optional request ID for traceability.": "ID de requête optionnel pour la traçabilité.", "Cancel if this limit is exceeded. Set 0 to disable.": "Annuler si cette limite est dépassée. Mettre 0 pour désactiver.", "Cancel if exceeded. Set 0 to disable.": "Annuler si dépassé. Définir 0 pour désactiver.", "Select a file that has been uploaded to Airtop": "Sélectionnez un fichier qui a été téléchargé sur Airtop", "Select one or more sessions to make the file available on. Leave empty to make available to all sessions.": "Sélectionnez une ou plusieurs sessions pour rendre le fichier disponible. Laissez vide pour rendre disponible à toutes les sessions.", "Describe the element to hover, e.g. \"the search box input in the top right corner\".": "Décrivez l'élément à survoler, par exemple \"le champ de recherche dans le coin supérieur droit\".", "Wait for page navigation to complete after hovering (default: false).": "Attendez que la navigation de la page soit terminée après le survol (par défaut: false).", "Authorization headers are injected automatically from your connection.": "Les en-têtes d'autorisation sont injectés automatiquement à partir de votre connexion.", "1280x720 (Default)": "1280x720 (par défaut)", "1920x1080": "1920 x 1080", "1366x768": "1366 x 768", "1024x768": "1024x768", "800x600": "800x600", "Load (Page + Assets) - Default": "Charge (Page + actifs) - Défaut", "DOM Content Loaded": "Contenu DOM chargé", "Complete (Page + Iframes)": "Complet (Page + Iframes)", "No Wait (Return Immediately)": "Pa<PERSON> d'attente (Revenir immédiatement)", "Base64 Data (Default for Viewport)": "Données Base64 (par défaut pour le Viewport)", "Download URL (Default for Page/Scan)": "URL de téléchargement (Par défaut pour la Page/Scan)", "Auto (Recommended)": "Auto (Recommandé)", "Current View Only": "Vue actuelle uniquement", "Full Page": "Page complète", "Scan Mode (For Problem Pages)": "Mode de scan (Pour les pages de problèmes)", "Auto (Default)": "Auto (par défaut)", "Enabled": "Activé", "Disabled": "Désactivé", "Click Next/Previous Links": "Cliquez sur les liens suivants/précédents", "Infinite Scroll": "<PERSON><PERSON><PERSON><PERSON> infini", "Auto (Balanced)": "Auto (Équilibre)", "More Accurate (Slower)": "Plus de précision (plus lent)", "Faster (Less Accurate)": "Plus rapide (moins précis)", "Left Click": "Clic gauche", "Double Click": "Double clic", "Right Click": "Clic droit", "Scan Mode": "Mode de scan", "Auto": "Automatique", "First Match": "Premier match", "Best Match": "Meilleure correspondance", "Vertical": "Vertical", "Horizontal": "Horizontal", "Bidirectional": "Bidirectionnel", "load": "Charger", "domcontentloaded": "format@@0 domcontent loaded", "networkidle0": "sans réseau0", "networkidle2": "réseau2", "Load (Default)": "Charger (par défaut)", "Network Idle 0": "Inactivité du réseau 0", "Network Idle 2": "Inactivité réseau 2", "Viewport Only": "Affichage uniquement", "Vertical (Default)": "Vertical (par défaut)", "GET": "OBTENIR", "POST": "POSTER", "PATCH": "PATCH", "PUT": "EFFACER", "DELETE": "SUPPRIMER", "HEAD": "TÊTE"}
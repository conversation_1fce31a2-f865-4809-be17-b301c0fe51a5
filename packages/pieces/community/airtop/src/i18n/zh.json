{"Airtop": "Airtop", "\n\t\tEnter your Airtop API key. You can get your API key from the [Airtop Dashboard](https://portal.airtop.ai/api-keys).\n\t\t\n\t\t**How to get your API key:**\n\t\t1. Go to the [Airtop Dashboard](https://portal.airtop.ai/api-keys)\n\t\t2. Sign in to your account\n\t\t3. Navigate to API Keys section\n\t\t4. Create a new API key or copy an existing one\n\t\t5. Paste the key here\n\t": "\n\t\tEnter your Airtop API key. You can get your API key from the [Airtop Dashboard](https://portal.airtop.ai/api-keys).\n\t\t\n\t\t**How to get your API key:**\n\t\t1. Go to the [Airtop Dashboard](https://portal.airtop.ai/api-keys)\n\t\t2. Sign in to your account\n\t\t3. Navigate to API Keys section\n\t\t4. Create a new API key or copy an existing one\n\t\t5. Paste the key here\n\t", "Create Session": "Create Session", "Terminate Session": "Terminate Session", "Create New Browser Window": "Create New Browser Window", "Take Screenshot": "Take Screenshot", "Page Query": "Page Query", "Smart Scrape": "Smart Scrape", "Paginated Extraction": "Paginated Extraction", "Click": "Click", "Type": "类型", "Upload File to Sessions": "Upload File to Sessions", "Hover on an Element": "Hover on an Element", "Custom API Call": "自定义 API 呼叫", "Starts a new browser session in Airtop.": "Starts a new browser session in Airtop.", "Ends an existing browser session in Airtop.": "Ends an existing browser session in Airtop.", "Opens a new window within a session, optionally navigating to a URL.": "Opens a new window within a session, optionally navigating to a URL.", "Captures a screenshot of the current window.": "Captures a screenshot of the current window.", "Query a page to extract data or ask a question given the data on the page.": "Query a page to extract data or ask a question given the data on the page.", "Scrape a page and return the data as Markdown.": "Scrape a page and return the data as Mark<PERSON>.", "Extract content from paginated or dynamically loaded pages.": "Extract content from paginated or dynamically loaded pages.", "Execute a click interaction in a specific browser window.": "Execute a click interaction in a specific browser window.", "Type into a browser window at the specified field.": "Type into a browser window at the specified field.", "Push an existing file to one or more sessions, making it available for use in file inputs or downloads.": "Push an existing file to one or more sessions, making it available for use in file inputs or downloads.", "Moves mouse pointer over an element in the browser window.": "Moves mouse pointer over an element in the browser window.", "Make a custom API call to a specific endpoint": "将一个自定义 API 调用到一个特定的终点", "Profile Name": "Profile Name", "Extension IDs": "Extension IDs", "Use Airtop Proxy?": "Use Airtop Proxy?", "Custom Proxy Configuration": "Custom Proxy Configuration", "Advanced Proxy Settings": "Advanced Proxy Settings", "Solve Captcha": "<PERSON><PERSON>", "Session Timeout (minutes)": "Session Timeout (minutes)", "Session": "Session", "Initial URL": "Initial URL", "Screen Resolution": "Screen Resolution", "Custom Resolution": "Custom Resolution", "Page Load Strategy": "Page Load Strategy", "Page Load Timeout (seconds)": "Page Load Timeout (seconds)", "Window": "Window", "Client Request ID": "Client Request ID", "Screenshot Format": "Screenshot Format", "Screenshot Scope": "Screenshot Scope", "Max Height (pixels)": "Max Height (pixels)", "Max Width (pixels)": "<PERSON> (pixels)", "JPEG Quality (1-100)": "JPEG Quality (1-100)", "Enable Advanced Visual Analysis": "Enable Advanced Visual Analysis", "Visual Analysis Settings": "Visual Analysis Settings", "Maximum Credits to Spend": "Maximum Credits to Spend", "Maximum Time (seconds)": "Maximum Time (seconds)", "Prompt": "Prompt", "Output Schema (JSON)": "Output Schema (JSON)", "Visual Analysis": "Visual Analysis", "Optimize URLs": "Optimize URLs", "Maximum Time (Seconds)": "Maximum Time (Seconds)", "Follow Pagination Links": "Follow Pagination Links", "Scroll Within": "<PERSON><PERSON> Within", "How to Load More Content": "How to Load More Content", "Speed vs Accuracy": "Speed vs Accuracy", "Element Description": "Element Description", "Click Type": "Click Type", "Page Analysis Scope": "Page Analysis Scope", "Result Selection Strategy": "Result Selection Strategy", "Partition Direction": "Partition Direction", "Maximum Scan Scrolls": "Maximum <PERSON>an <PERSON>rolls", "Scan Scroll Delay (ms)": "<PERSON><PERSON> (ms)", "Overlap Percentage": "Overlap Percentage", "Wait for Navigation": "Wait for Navigation", "Navigation Wait Until": "Navigation Wait Until", "Navigation Timeout (Seconds)": "Navigation Timeout (Seconds)", "Text to Type": "Text to Type", "Clear Input Field Before Typing": "Clear Input Field Before Typing", "Press Enter After Typing": "Press Enter After Typing", "Press Tab After Typing": "Press Tab After Typing", "Wait for Navigation After Typing": "Wait for Navigation After Typing", "Navigation Wait Strategy": "Navigation Wait Strategy", "Max Scrolls (Scan Mode)": "<PERSON> (Scan Mode)", "Chunk Overlap (%)": "<PERSON><PERSON> (%)", "Scroll Delay (ms)": "<PERSON><PERSON> (ms)", "Max Credits to Spend": "Max Credits to Spend", "Max Time to Wait (Seconds)": "Max Time to Wait (Seconds)", "File": "文件", "Session IDs": "Session IDs", "Method": "方法", "Headers": "信头", "Query Parameters": "查询参数", "Body": "正文内容", "No Error on Failure": "失败时没有错误", "Timeout (in seconds)": "超时(秒)", "Name of a profile to load into the session.": "Name of a profile to load into the session.", "List of Chrome extension IDs from Google Web Store.": "List of Chrome extension IDs from Google Web Store.", "Enable Airtop-provided proxy. If disabled, configure a custom proxy.": "Enable Airtop-provided proxy. If disabled, configure a custom proxy.", "Automatically solve captcha challenges.": "Automatically solve captcha challenges.", "How long before the session times out due to inactivity (1-10080 minutes). Default: 10.": "How long before the session times out due to inactivity (1-10080 minutes). Default: 10.", "Select an active Airtop session to use for browser automation": "Select an active Airtop session to use for browser automation", "URL to open in the new window. Default: https://www.google.com": "URL to open in the new window. Default: https://www.google.com", "Fixed dimensions for the browser window. Affects live view size.": "Fixed dimensions for the browser window. Affects live view size.", "Custom resolution in format \"widthxheight\" (e.g., \"1440x900\"). Leave blank to use selected resolution above.": "Custom resolution in format \"widthxheight\" (e.g., \"1440x900\"). Leave blank to use selected resolution above.", "When to consider the page loaded. Default: load": "When to consider the page loaded. Default: load", "Maximum time to wait for page loading. Default: 30 seconds": "Maximum time to wait for page loading. Default: 30 seconds", "Select a browser window within the chosen session": "Select a browser window within the chosen session", "Optional ID for tracking this request": "Optional ID for tracking this request", "How to return the screenshot. Default: base64 for viewport, url for page/scan": "How to return the screenshot. Default: base64 for viewport, url for page/scan", "What part of the page to capture. Default: auto": "What part of the page to capture. Default: auto", "Maximum height of screenshot. Will scale down if needed, preserving aspect ratio.": "Maximum height of screenshot. Will scale down if needed, preserving aspect ratio.", "Maximum width of screenshot. Will scale down if needed, preserving aspect ratio.": "Maximum width of screenshot. Will scale down if needed, preserving aspect ratio.", "Image quality for JPEG compression. Higher = better quality. Note: Feature in development.": "Image quality for JPEG compression. Higher = better quality. Note: Feature in development.", "Enable advanced visual analysis features for better page processing": "Enable advanced visual analysis features for better page processing", "Stop screenshot if it costs more than this. Leave blank for default limit.": "Stop screenshot if it costs more than this. Leave blank for default limit.", "Stop screenshot if it takes longer than this. Leave blank for default timeout.": "Stop screenshot if it takes longer than this. Leave blank for default timeout.", "The question or instruction for Airtop to answer about the current page.": "The question or instruction for Airtop to answer about the current page.", "An optional ID for your internal tracking.": "An optional ID for your internal tracking.", "JSON schema defining the structure of the output. Must be valid JSON schema format.": "JSON schema defining the structure of the output. Must be valid JSON schema format.", "Whether to include visual analysis of the page (default: auto)": "Whether to include visual analysis of the page (default: auto)", "Improve scraping performance by optimizing URLs (default: auto)": "Improve scraping performance by optimizing URLs (default: auto)", "Abort if the credit cost exceeds this amount. Set to 0 to disable.": "Abort if the credit cost exceeds this amount. Set to 0 to disable.", "Abort if the operation takes longer than this. Set to 0 to disable.": "Abort if the operation takes longer than this. Set to 0 to disable.", "If enabled, Airtop will attempt to load more content from pagination, scrolling, etc. (default: false)": "If enabled, Airtop will attempt to load more content from pagination, scrolling, etc. (default: false)", "Optional ID to track this request on your end.": "Optional ID to track this request on your end.", "Instructions on what to extract and how to paginate (e.g. \"Navigate through 3 pages and extract titles and prices\").": "Instructions on what to extract and how to paginate (e.g. \"Navigate through 3 pages and extract titles and prices\").", "Optional ID to track this request.": "Optional ID to track this request.", "Describe the scrollable area (e.g. \"results container in middle of page\").": "Describe the scrollable area (e.g. \"results container in middle of page\").", "Choose how to navigate through pages (default: auto)": "Choose how to navigate through pages (default: auto)", "Balance between speed and accuracy (default: auto)": "Balance between speed and accuracy (default: auto)", "Describe the element to click (e.g. \"Login button\").": "Describe the element to click (e.g. \"Login button\").", "The type of click to perform (default: left click).": "The type of click to perform (default: left click).", "Describe the scrollable area to search within (e.g. \"main content area\").": "Describe the scrollable area to search within (e.g. \"main content area\").", "Controls how much of the page is visually analyzed (default: auto).": "Controls how much of the page is visually analyzed (default: auto).", "How to select from multiple matches (default: auto).": "How to select from multiple matches (default: auto).", "How to partition screenshots for analysis (default: vertical).": "How to partition screenshots for analysis (default: vertical).", "Maximum number of scrolls in scan mode (default: 50).": "Maximum number of scrolls in scan mode (default: 50).", "Delay between scrolls in scan mode (default: 1000ms).": "Delay between scrolls in scan mode (default: 1000ms).", "Percentage of overlap between screenshot chunks (default: 30).": "Percentage of overlap between screenshot chunks (default: 30).", "Wait for page navigation to complete after clicking (default: false).": "Wait for page navigation to complete after clicking (default: false).", "When to consider navigation complete (default: load).": "When to consider navigation complete (default: load).", "Max seconds to wait for navigation (default: 30).": "Max seconds to wait for navigation (default: 30).", "The text to type into the browser window.": "The text to type into the browser window.", "Describe the element (e.g., \"search box\", \"username field\").": "Describe the element (e.g., \"search box\", \"username field\").", "Clear the input field before typing text.": "Clear the input field before typing text.", "Press Enter key after typing text.": "Press Enter key after typing text.", "Press Tab key after typing text (after Enter if both enabled).": "Press Tab key after typing text (after En<PERSON> if both enabled).", "Wait for page navigation to complete after typing (default: false).": "Wait for page navigation to complete after typing (default: false).", "Max time to wait for navigation after typing (default: 30).": "Max time to wait for navigation after typing (default: 30).", "Condition to consider navigation complete (default: load).": "Condition to consider navigation complete (default: load).", "Controls how much of the page is analyzed to find the input (default: auto).": "Controls how much of the page is analyzed to find the input (default: auto).", "Percentage of overlap between visual chunks (default: 30).": "Percentage of overlap between visual chunks (default: 30).", "Direction to partition screenshots (default: vertical).": "Direction to partition screenshots (default: vertical).", "Describe the scrollable container (e.g., \"table body\", \"product list\").": "Describe the scrollable container (e.g., \"table body\", \"product list\").", "Optional request ID for traceability.": "Optional request ID for traceability.", "Cancel if this limit is exceeded. Set 0 to disable.": "Cancel if this limit is exceeded. Set 0 to disable.", "Cancel if exceeded. Set 0 to disable.": "Cancel if exceeded. Set 0 to disable.", "Select a file that has been uploaded to Airtop": "Select a file that has been uploaded to Airtop", "Select one or more sessions to make the file available on. Leave empty to make available to all sessions.": "Select one or more sessions to make the file available on. Leave empty to make available to all sessions.", "Describe the element to hover, e.g. \"the search box input in the top right corner\".": "Describe the element to hover, e.g. \"the search box input in the top right corner\".", "Wait for page navigation to complete after hovering (default: false).": "Wait for page navigation to complete after hovering (default: false).", "Authorization headers are injected automatically from your connection.": "授权头自动从您的连接中注入。", "1280x720 (Default)": "1280x720 (<PERSON><PERSON><PERSON>)", "1920x1080": "1920x1080", "1366x768": "1366x768", "1024x768": "1024x768", "800x600": "800x600", "Load (Page + Assets) - Default": "Load (Page + Assets) - <PERSON><PERSON><PERSON>", "DOM Content Loaded": "DOM Content Loaded", "Complete (Page + Iframes)": "Complete (Page + <PERSON><PERSON><PERSON>)", "No Wait (Return Immediately)": "No Wait (Return Immediately)", "Base64 Data (Default for Viewport)": "Base64 Data (Default for Viewport)", "Download URL (Default for Page/Scan)": "Download URL (De<PERSON><PERSON> for Page/Scan)", "Auto (Recommended)": "Auto (Recommended)", "Current View Only": "Current View Only", "Full Page": "Full Page", "Scan Mode (For Problem Pages)": "<PERSON>an Mode (For Problem Pages)", "Auto (Default)": "Auto (Default)", "Enabled": "已启用", "Disabled": "已禁用", "Click Next/Previous Links": "Click Next/Previous Links", "Infinite Scroll": "Infinite Scroll", "Auto (Balanced)": "Auto (Balanced)", "More Accurate (Slower)": "More Accurate (Slower)", "Faster (Less Accurate)": "Faster (Less Accurate)", "Left Click": "Left Click", "Double Click": "Double Click", "Right Click": "Right Click", "Scan Mode": "Scan <PERSON>", "Auto": "Auto", "First Match": "First Match", "Best Match": "Best Match", "Vertical": "Vertical", "Horizontal": "Horizontal", "Bidirectional": "Bidirectional", "load": "load", "domcontentloaded": "domcontentloaded", "networkidle0": "networkidle0", "networkidle2": "networkidle2", "Load (Default)": "Load (<PERSON><PERSON><PERSON>)", "Network Idle 0": "Network Idle 0", "Network Idle 2": "Network Idle 2", "Viewport Only": "Viewport Only", "Vertical (Default)": "Vertical (Default)", "GET": "获取", "POST": "帖子", "PATCH": "PATCH", "PUT": "弹出", "DELETE": "删除", "HEAD": "黑色"}
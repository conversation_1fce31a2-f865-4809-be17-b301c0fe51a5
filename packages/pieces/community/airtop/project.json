{"name": "pieces-airtop", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/pieces/community/airtop/src", "projectType": "library", "release": {"version": {"manifestRootsToUpdate": ["dist/{projectRoot}"], "currentVersionResolver": "git-tag", "fallbackCurrentVersionResolver": "disk"}}, "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/pieces/community/airtop", "tsConfig": "packages/pieces/community/airtop/tsconfig.lib.json", "packageJson": "packages/pieces/community/airtop/package.json", "main": "packages/pieces/community/airtop/src/index.ts", "assets": ["packages/pieces/community/airtop/*.md", {"input": "packages/pieces/community/airtop/src/i18n", "output": "./src/i18n", "glob": "**/!(i18n.json)"}], "buildableProjectDepsInPackageJsonType": "dependencies", "updateBuildableProjectDepsInPackageJson": true}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}}
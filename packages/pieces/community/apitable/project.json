{"name": "pieces-apitable", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/pieces/community/apitable/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/pieces/community/apitable", "tsConfig": "packages/pieces/community/apitable/tsconfig.lib.json", "packageJson": "packages/pieces/community/apitable/package.json", "main": "packages/pieces/community/apitable/src/index.ts", "assets": ["packages/pieces/community/apitable/*.md", {"input": "packages/pieces/community/apitable/src/i18n", "output": "./src/i18n", "glob": "**/!(i18n.json)"}], "buildableProjectDepsInPackageJsonType": "dependencies", "updateBuildableProjectDepsInPackageJson": true}}, "publish": {"command": "node tools/scripts/publish.mjs pieces-apitable {args.ver} {args.tag}", "dependsOn": ["build"]}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}, "tags": []}
export const enum AITableFieldType {
	SINGLE_TEXT = 'SingleText',
	ONE_WAY_LINK = 'OneWayLink',
	TWO_WAY_LINK = 'TwoWayLink',
	MAGIC_LOOKUP = 'MagicLookUp',
	SINGLE_SELECT = 'SingleSelect',
	MEMBER = 'Member',
	DATETIME = 'DateTime',
	NUMBER = 'Number',
	FORMULA = 'Formula',
	TEXT = 'Text',
	MULTI_SELECT = 'MultiSelect',
	CURRENCY = 'Currency',
	PERCENT = 'Percent',
	RATING = 'Rating',
	CHECKBOX = 'Checkbox',
	URL = 'URL',
	PHONE = 'Phone',
	EMAIL = 'Email',
	AUTONUMBER = 'AutoNumber',
	CREATED_BY = 'CreatedBy',
	LAST_MODIFIED_BY = 'LastModifiedBy',
	CASCADER = 'Cascader',
	CREATED_TIME = 'CreatedTime',
	LAST_MODIEFIED_TIME = 'LastModifiedTime',
	ATTACHMENT = 'Attachment',
}

export const AITableNumericFieldTypes = [
	AITableFieldType.NUMBER,
	AITableFieldType.RATING,
	AITableFieldType.CURRENCY,
	AITableFieldType.PERCENT,
];

{"AITable": "AITable", "Interactive spreadsheets with collaboration": "Interactieve spreadsheets met samenwerking", "Token": "Token", "Instance Url": "Instantie U<PERSON>", "The token of the AITable account": "Het token van het AITable account", "The url of the AITable instance.": "De URL van de AITable instantie.", "\n    To obtain your AITable token, follow these steps:\n\n    1. Log in to your AITable account.\n    2. Visit https://apitable.com/workbench\n    3. Click on your profile picture (Bottom left).\n    4. Click on \"My Settings\".\n    5. Click on \"Developer\".\n    6. Click on \"Generate new token\".\n    7. Copy the token.\n    ": "\n    Om je AITable token te verkrijgen, volg deze stappen:\n\n    1. Log in op uw AITable account.\n    2. <PERSON><PERSON>ek https://apitable.com/workbench\n    3. Klik op je profielfoto (Bottom links).\n    4. <PERSON><PERSON> op \"Mijn instellingen\".\n    5. <PERSON><PERSON> op \"Ontwikkelaar\".\n    6. Klik op \"Genereer nieuwe token\".\n    7. <PERSON><PERSON>er de token.\n    ", "Create Record": "Record Maken", "Update Record": "Update Record", "Find Records": "Records zoeken", "Custom API Call": "Custom API Call", "Creates a new record in datasheet.": "Ma<PERSON>t een nieuw record in datasheet.", "Updates an existing record in datasheet.": "Werkt een bestaand record bij in datasheet.", "Finds records in datasheet.": "Gevonden records in datasheet.", "Make a custom API call to a specific endpoint": "Maak een aangepaste API call naar een specifiek eindpunt", "Space": "Spatiebalk", "Datasheet": "Datasheet", "Fields": "<PERSON><PERSON><PERSON>", "Record ID": "Record ID", "Record IDs": "ID's opnemen", "Field Names": "Veldnamen", "Max Records": "Max Records", "Page Size": "Paginagrootte", "Page Number": "<PERSON><PERSON><PERSON>", "Filter": "Filteren", "Method": "<PERSON>e", "Headers": "Kopteksten", "Query Parameters": "Query parameters", "Body": "<PERSON><PERSON><PERSON>", "No Error on Failure": "<PERSON><PERSON> fout bij fout", "Timeout (in seconds)": "Time-out (in seconden)", "The fields to add to the record.": "De velden om toe te voegen aan het record.", "The ID of the record to update.": "Het <PERSON> van het te updaten record", "The IDs of the records to find.": "De ID's van de records om te vinden.", "The returned record results are limited to the specified fields": "De geretourneerde record resultaten zijn beperkt tot de opgegeven velden", "How many records are returned in total": "Hoeveel records worden geretourneerd in totaal", "How many records are returned per page (max 1000)": "Hoeveel records worden geretourneerd per pagina (max 1000)", "Specifies the page number of the page": "Specificeert het paginanummer van de pagina", "The filter to apply to the records (see https://help.aitable.ai/docs/guide/manual-formula-field-overview/)": "Het filter om toe te passen op de records (zie https://help.aitable.ai/docs/guide/manual-forma-field-overview/)", "Authorization headers are injected automatically from your connection.": "Autorisatie headers worden automatisch geïnjecteerd vanuit uw verbinding.", "GET": "KRIJG", "POST": "POSTE", "PATCH": "BEKIJK", "PUT": "PUT", "DELETE": "VERWIJDEREN", "HEAD": "HOOFD", "New Record": "Nieuwe Record", "Triggers when a new record is added to a datasheet.": "<PERSON>ggert wanneer een nieuw record wordt toegevoegd aan een datasheet."}
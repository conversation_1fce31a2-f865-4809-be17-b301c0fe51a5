{"AITable": "AITable", "Interactive spreadsheets with collaboration": "Interactive spreadsheets with collaboration", "Token": "Token", "Instance Url": "Instance Url", "The token of the AITable account": "The token of the AITable account", "The url of the AITable instance.": "The url of the AITable instance.", "\n    To obtain your AITable token, follow these steps:\n\n    1. Log in to your AITable account.\n    2. Visit https://apitable.com/workbench\n    3. Click on your profile picture (Bottom left).\n    4. Click on \"My Settings\".\n    5. Click on \"Developer\".\n    6. Click on \"Generate new token\".\n    7. Copy the token.\n    ": "\n    To obtain your AITable token, follow these steps:\n\n    1. Log in to your AITable account.\n    2. Visit https://apitable.com/workbench\n    3. Click on your profile picture (Bottom left).\n    4. <PERSON>lick on \"My Settings\".\n    5. <PERSON>lick on \"Developer\".\n    6. <PERSON>lick on \"Generate new token\".\n    7. Copy the token.\n    ", "Create Record": "Create Record", "Update Record": "Update Record", "Find Records": "Find Records", "Custom API Call": "Custom API Call", "Creates a new record in datasheet.": "Creates a new record in datasheet.", "Updates an existing record in datasheet.": "Updates an existing record in datasheet.", "Finds records in datasheet.": "Finds records in datasheet.", "Make a custom API call to a specific endpoint": "Make a custom API call to a specific endpoint", "Space": "Space", "Datasheet": "Datasheet", "Fields": "Fields", "Record ID": "Record ID", "Record IDs": "Record IDs", "Field Names": "Field Names", "Max Records": "Max Records", "Page Size": "<PERSON>", "Page Number": "Page Number", "Filter": "Filter", "Method": "Method", "Headers": "Headers", "Query Parameters": "Query Parameters", "Body": "Body", "No Error on Failure": "No Error on Failure", "Timeout (in seconds)": "Timeout (in seconds)", "The fields to add to the record.": "The fields to add to the record.", "The ID of the record to update.": "The ID of the record to update.", "The IDs of the records to find.": "The IDs of the records to find.", "The returned record results are limited to the specified fields": "The returned record results are limited to the specified fields", "How many records are returned in total": "How many records are returned in total", "How many records are returned per page (max 1000)": "How many records are returned per page (max 1000)", "Specifies the page number of the page": "Specifies the page number of the page", "The filter to apply to the records (see https://help.aitable.ai/docs/guide/manual-formula-field-overview/)": "The filter to apply to the records (see https://help.aitable.ai/docs/guide/manual-formula-field-overview/)", "Authorization headers are injected automatically from your connection.": "Authorization headers are injected automatically from your connection.", "GET": "GET", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "DELETE", "HEAD": "HEAD", "New Record": "New Record", "Triggers when a new record is added to a datasheet.": "Triggers when a new record is added to a datasheet."}
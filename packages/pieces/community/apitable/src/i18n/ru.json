{"AITable": "Доступно", "Interactive spreadsheets with collaboration": "Интерактивные электронные таблицы с сотрудничеством", "Token": "Токен", "Instance Url": "Ссылка экземпляра", "The token of the AITable account": "Токен учетной записи AITable", "The url of the AITable instance.": "Адрес url экземпляра AITable .", "\n    To obtain your AITable token, follow these steps:\n\n    1. Log in to your AITable account.\n    2. Visit https://apitable.com/workbench\n    3. Click on your profile picture (Bottom left).\n    4. Click on \"My Settings\".\n    5. Click on \"Developer\".\n    6. Click on \"Generate new token\".\n    7. Copy the token.\n    ": "\n    Для получения токена AITable выполните следующие действия:\n\n    1. Войдите в свою учетную запись AITable.\n    2. Посетите https://apitable.com/workbench\n    3. Нажмите на изображение вашего профиля (слева вниз).\n    4. Нажмите на \"Мои настройки\".\n    5. Нажмите на \"Разработчик\".\n    6. Нажмите на \"Создать новый токен\".\n    7. Скопируйте токен.\n    ", "Create Record": "Создать запись", "Update Record": "Обновить запись", "Find Records": "Найти записи", "Custom API Call": "Пользовательский вызов API", "Creates a new record in datasheet.": "Создает новую запись в datasheet.", "Updates an existing record in datasheet.": "Обновляет существующую запись в datasheet.", "Finds records in datasheet.": "Ищет записи в хранилище.", "Make a custom API call to a specific endpoint": "Сделать пользовательский API вызов к определенной конечной точке", "Space": "Пространство", "Datasheet": "Datasheet", "Fields": "Поля", "Record ID": "ID записи", "Record IDs": "ID записи", "Field Names": "Имена полей", "Max Records": "Макс. записей", "Page Size": "Размер страницы", "Page Number": "Номер страницы", "Filter": "Фильтр", "Method": "Метод", "Headers": "Заголовки", "Query Parameters": "Параметры запроса", "Body": "Тело", "No Error on Failure": "Нет ошибок при ошибке", "Timeout (in seconds)": "Таймаут (в секундах)", "The fields to add to the record.": "Поля для добавления в запись.", "The ID of the record to update.": "ID записи для обновления.", "The IDs of the records to find.": "Идентификаторы записей, которые нужно найти.", "The returned record results are limited to the specified fields": "Результаты возвращенной записи ограничены указанными полями", "How many records are returned in total": "Сколько записей возвращено в общей сложности", "How many records are returned per page (max 1000)": "Сколько записей возвращено на страницу (не более 1000)", "Specifies the page number of the page": "Номер страницы", "The filter to apply to the records (see https://help.aitable.ai/docs/guide/manual-formula-field-overview/)": "Фильтр для применения к записям (см. https://help.aitable.ai/docs/guide/manual-formula-field-overview/)", "Authorization headers are injected automatically from your connection.": "Заголовки авторизации включаются автоматически из вашего соединения.", "GET": "ПОЛУЧИТЬ", "POST": "ПОСТ", "PATCH": "ПАТЧ", "PUT": "ПОКУПИТЬ", "DELETE": "УДАЛИТЬ", "HEAD": "HEAD", "New Record": "Новая запись", "Triggers when a new record is added to a datasheet.": "Включает при добавлении новой записи в таблицу."}
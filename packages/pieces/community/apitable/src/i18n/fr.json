{"AITable": "AITable", "Interactive spreadsheets with collaboration": "Feuilles de calcul interactives avec collaboration", "Token": "<PERSON><PERSON>", "Instance Url": "Url de l'instance", "The token of the AITable account": "Le jeton du compte AITable", "The url of the AITable instance.": "L'url de l'instance AITable.", "\n    To obtain your AITable token, follow these steps:\n\n    1. Log in to your AITable account.\n    2. Visit https://apitable.com/workbench\n    3. Click on your profile picture (Bottom left).\n    4. Click on \"My Settings\".\n    5. Click on \"Developer\".\n    6. Click on \"Generate new token\".\n    7. Copy the token.\n    ": "\n    To obtain your AITable token, follow these steps:\n\n    1. Log in to your AITable account.\n    2. Visit https://apitable.com/workbench\n    3. Click on your profile picture (Bottom left).\n    4. <PERSON>lick on \"My Settings\".\n    5. <PERSON>lick on \"Developer\".\n    6. <PERSON>lick on \"Generate new token\".\n    7. Copy the token.\n    ", "Create Record": "<PERSON><PERSON>er un enregistrement", "Update Record": "Mettre à jour l'enregistrement", "Find Records": "Trouver des enregistrements", "Custom API Call": "Appel API personnalisé", "Creates a new record in datasheet.": "Crée un nouvel enregistrement dans la feuille de données.", "Updates an existing record in datasheet.": "Met à jour un enregistrement existant dans la feuille de données.", "Finds records in datasheet.": "Trouve les enregistrements dans la feuille de données.", "Make a custom API call to a specific endpoint": "Passez un appel API personnalisé à un point de terminaison spécifique", "Space": "Espace libre", "Datasheet": "Datasheet", "Fields": "<PERSON><PERSON>", "Record ID": "ID de l'enregistrement", "Record IDs": "ID d'enregistrement", "Field Names": "Noms des champs", "Max Records": "Nombre maximum d'enregistrements", "Page Size": "Nombre d'élément", "Page Number": "Numéro de <PERSON>", "Filter": "Filtre", "Method": "Méthode", "Headers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Query Parameters": "Paramètres de requête", "Body": "Corps", "No Error on Failure": "Aucune erreur en cas d'échec", "Timeout (in seconds)": "<PERSON><PERSON><PERSON>atten<PERSON> (en secondes)", "The fields to add to the record.": "Les champs à ajouter à l'enregistrement.", "The ID of the record to update.": "L'ID de l'enregistrement à mettre à jour.", "The IDs of the records to find.": "Les identifiants des enregistrements à trouver.", "The returned record results are limited to the specified fields": "Les résultats de l'enregistrement retourné sont limités aux champs spécifiés", "How many records are returned in total": "Combien d'enregistrements sont retournés au total", "How many records are returned per page (max 1000)": "Combien d'enregistrements sont retournés par page (max 1000)", "Specifies the page number of the page": "Spéci<PERSON> le numéro de page de la page", "The filter to apply to the records (see https://help.aitable.ai/docs/guide/manual-formula-field-overview/)": "Le filtre à appliquer aux enregistrements (voir https://help.aitable.ai/docs/guide/manual-formula-field-overview/)", "Authorization headers are injected automatically from your connection.": "Les en-têtes d'autorisation sont injectés automatiquement à partir de votre connexion.", "GET": "OBTENIR", "POST": "POSTER", "PATCH": "PATCH", "PUT": "EFFACER", "DELETE": "SUPPRIMER", "HEAD": "TÊTE", "New Record": "Nouvel enregistrement", "Triggers when a new record is added to a datasheet.": "Déclenche lorsqu'un nouvel enregistrement est ajouté à une fiche de données."}
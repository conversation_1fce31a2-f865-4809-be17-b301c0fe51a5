{"AITable": "AITable", "Interactive spreadsheets with collaboration": "コラボレーションによるインタラクティブな表計算ドキュメント", "Token": "トークン", "Instance Url": "インスタンスURL", "The token of the AITable account": "AITableアカウントのトークン", "The url of the AITable instance.": "AITableインスタンスのURL。", "\n    To obtain your AITable token, follow these steps:\n\n    1. Log in to your AITable account.\n    2. Visit https://apitable.com/workbench\n    3. Click on your profile picture (Bottom left).\n    4. Click on \"My Settings\".\n    5. Click on \"Developer\".\n    6. Click on \"Generate new token\".\n    7. Copy the token.\n    ": "\n    To obtain your AITable token, follow these steps:\n\n    1. Log in to your AITable account.\n    2. Visit https://apitable.com/workbench\n    3. Click on your profile picture (Bottom left).\n    4. <PERSON>lick on \"My Settings\".\n    5. <PERSON>lick on \"Developer\".\n    6. <PERSON>lick on \"Generate new token\".\n    7. Copy the token.\n    ", "Create Record": "レコードを作成", "Update Record": "更新記録", "Find Records": "レコードを検索", "Custom API Call": "カスタムAPI通話", "Creates a new record in datasheet.": "データシートに新しいレコードを作成します。", "Updates an existing record in datasheet.": "データシートの既存のレコードを更新します。", "Finds records in datasheet.": "データシートのレコードを検索します。", "Make a custom API call to a specific endpoint": "特定のエンドポイントへのカスタム API コールを実行します。", "Space": "スペース", "Datasheet": "Datasheet", "Fields": "フィールド", "Record ID": "レコードID", "Record IDs": "レコードID", "Field Names": "フィールド名", "Max Records": "最大レコード", "Page Size": "ページサイズ", "Page Number": "ページ番号", "Filter": "フィルター", "Method": "方法", "Headers": "ヘッダー", "Query Parameters": "クエリパラメータ", "Body": "本文", "No Error on Failure": "失敗時にエラーはありません", "Timeout (in seconds)": "タイムアウト（秒）", "The fields to add to the record.": "レコードに追加するフィールド。", "The ID of the record to update.": "更新するレコードのID。", "The IDs of the records to find.": "検索するレコードのID。", "The returned record results are limited to the specified fields": "返されたレコードの結果は指定されたフィールドに制限されています", "How many records are returned in total": "返されるレコードの合計数", "How many records are returned per page (max 1000)": "1ページあたりのレコードの返却回数（最大1000回）", "Specifies the page number of the page": "ページのページ番号を指定します", "The filter to apply to the records (see https://help.aitable.ai/docs/guide/manual-formula-field-overview/)": "レコードに適用するフィルター (https://help.aitable.ai/docs/guide/manual-formula-field-overview/を参照)", "Authorization headers are injected automatically from your connection.": "認証ヘッダは接続から自動的に注入されます。", "GET": "取得", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "削除", "HEAD": "頭", "New Record": "新しいレコード", "Triggers when a new record is added to a datasheet.": "新しいレコードがデータシートに追加されたときにトリガーされます。"}
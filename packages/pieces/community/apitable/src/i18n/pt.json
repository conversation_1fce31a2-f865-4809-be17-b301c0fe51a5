{"AITable": "AITÍvel", "Interactive spreadsheets with collaboration": "Planilhas interativas com colaboração", "Token": "Identificador", "Instance Url": "URL da Instância", "The token of the AITable account": "O token da conta AITable", "The url of the AITable instance.": "A url da instância AITable.", "\n    To obtain your AITable token, follow these steps:\n\n    1. Log in to your AITable account.\n    2. Visit https://apitable.com/workbench\n    3. Click on your profile picture (Bottom left).\n    4. Click on \"My Settings\".\n    5. Click on \"Developer\".\n    6. Click on \"Generate new token\".\n    7. Copy the token.\n    ": "\n    To obtain your AITable token, follow these steps:\n\n    1. Log in to your AITable account.\n    2. Visit https://apitable.com/workbench\n    3. Click on your profile picture (Bottom left).\n    4. <PERSON>lick on \"My Settings\".\n    5. <PERSON>lick on \"Developer\".\n    6. <PERSON>lick on \"Generate new token\".\n    7. Copy the token.\n    ", "Create Record": "<PERSON><PERSON><PERSON>", "Update Record": "<PERSON><PERSON><PERSON><PERSON>", "Find Records": "Encontrar registros", "Custom API Call": "Chamada de API personalizada", "Creates a new record in datasheet.": "Cria um novo registro na folha de dados.", "Updates an existing record in datasheet.": "Atualiza um registro existente na ficha técnica.", "Finds records in datasheet.": "Encontrar registros na ficha técnica.", "Make a custom API call to a specific endpoint": "Faça uma chamada de API personalizada para um ponto de extremidade específico", "Space": "Sala", "Datasheet": "Datasheet", "Fields": "campos", "Record ID": "ID do Registro", "Record IDs": "IDs de Registro", "Field Names": "Nomes <PERSON>", "Max Records": "Registros Máx.", "Page Size": "<PERSON><PERSON><PERSON>", "Page Number": "Número da página", "Filter": "filtro", "Method": "<PERSON><PERSON><PERSON><PERSON>", "Headers": "Cabeçalhos", "Query Parameters": "Parâ<PERSON><PERSON> da consult<PERSON>", "Body": "<PERSON><PERSON><PERSON><PERSON>", "No Error on Failure": "Nenhum erro no Failure", "Timeout (in seconds)": "Tempo limite (em segundos)", "The fields to add to the record.": "Os campos para adicionar ao registro.", "The ID of the record to update.": "A ID do registro a ser atualizada.", "The IDs of the records to find.": "Os IDs dos registros a serem encontrados.", "The returned record results are limited to the specified fields": "Os resultados dos registros retornados são limitados aos campos especificados", "How many records are returned in total": "Quantos registros são devolvidos no total", "How many records are returned per page (max 1000)": "Quantos registros são devolvidos por página (máx. 1000)", "Specifies the page number of the page": "Especifica o número da página da página", "The filter to apply to the records (see https://help.aitable.ai/docs/guide/manual-formula-field-overview/)": "O filtro a ser aplicado aos registros (consulte https://help.aitable.ai/docs/guide/manual-formula-field-overview/)", "Authorization headers are injected automatically from your connection.": "Os cabeçalhos de autorização são inseridos automaticamente a partir da sua conexão.", "GET": "OBTER", "POST": "POSTAR", "PATCH": "COMPRAR", "PUT": "COLOCAR", "DELETE": "EXCLUIR", "HEAD": "CABEÇA", "New Record": "Novo Registro", "Triggers when a new record is added to a datasheet.": "Aciona quando um novo registro é adicionado a uma ficha técnica."}
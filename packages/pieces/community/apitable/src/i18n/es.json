{"AITable": "AITable", "Interactive spreadsheets with collaboration": "Hojas de cálculo interactivas con colaboración", "Token": "Token", "Instance Url": "Url de Instancia", "The token of the AITable account": "El token de la cuenta AITable", "The url of the AITable instance.": "La url de la instancia de AITable.", "\n    To obtain your AITable token, follow these steps:\n\n    1. Log in to your AITable account.\n    2. Visit https://apitable.com/workbench\n    3. Click on your profile picture (Bottom left).\n    4. Click on \"My Settings\".\n    5. Click on \"Developer\".\n    6. Click on \"Generate new token\".\n    7. Copy the token.\n    ": "\n    Para obtener su token AITable, siga estos pasos:\n\n    1. Inicie sesión en su cuenta de AITable.\n    2. Visite https://apitable.com/workbench\n    3. Haz clic en tu foto de perfil (parte inferior izquierda).\n    4. Haz clic en \"Mis Ajustes\".\n    5. Haz clic en \"Desarrollador\".\n    6. Haz clic en \"Generar nuevo token\".\n    7. Copia el token.\n    ", "Create Record": "<PERSON><PERSON>r registro", "Update Record": "Actualizar registro", "Find Records": "Buscar registros", "Custom API Call": "Llamada API personalizada", "Creates a new record in datasheet.": "Crea un nuevo registro en la hoja de datos.", "Updates an existing record in datasheet.": "Actualiza un registro existente en la hoja de datos.", "Finds records in datasheet.": "Encuentra registros en la hoja de datos.", "Make a custom API call to a specific endpoint": "Hacer una llamada API personalizada a un extremo específico", "Space": "Espacio", "Datasheet": "Datasheet", "Fields": "Campos", "Record ID": "ID de registro", "Record IDs": "ID de registro", "Field Names": "Nombres de campos", "Max Records": "Grabaciones máximas", "Page Size": "Tamaño de página", "Page Number": "Número de página", "Filter": "Filtro", "Method": "<PERSON><PERSON><PERSON><PERSON>", "Headers": "Encabezados", "Query Parameters": "Parámetros de consulta", "Body": "<PERSON><PERSON><PERSON>", "No Error on Failure": "No hay ningún error en fallo", "Timeout (in seconds)": "Tiempo de espera (en segundos)", "The fields to add to the record.": "Los campos a agregar al registro.", "The ID of the record to update.": "El ID del registro a actualizar.", "The IDs of the records to find.": "Los IDs de los registros a encontrar.", "The returned record results are limited to the specified fields": "Los resultados del registro devuelto están limitados a los campos especificados", "How many records are returned in total": "Cuántos registros se devuelven en total", "How many records are returned per page (max 1000)": "Cuántos registros se devuelven por página (máx. 1000)", "Specifies the page number of the page": "Especifica el número de página de la página", "The filter to apply to the records (see https://help.aitable.ai/docs/guide/manual-formula-field-overview/)": "El filtro a aplicar a los registros (ver https://help.aitable.ai/docs/guide/manual-formula-field-overview/)", "Authorization headers are injected automatically from your connection.": "Las cabeceras de autorización se inyectan automáticamente desde tu conexión.", "GET": "RECOGER", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "BORRAR", "HEAD": "LIMPIO", "New Record": "Nuevo registro", "Triggers when a new record is added to a datasheet.": "Se activa cuando se añade un nuevo registro a una hoja de datos."}
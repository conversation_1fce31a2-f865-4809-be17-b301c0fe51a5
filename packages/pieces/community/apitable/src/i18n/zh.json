{"AITable": "AITable", "Interactive spreadsheets with collaboration": "具備合作功能的互動式電子表格", "Token": "令牌", "Instance Url": "實例網址", "The token of the AITable account": "AITable 帳戶的令牌", "The url of the AITable instance.": "AITable 實例的網址。", "\n    To obtain your AITable token, follow these steps:\n\n    1. Log in to your AITable account.\n    2. Visit https://apitable.com/workbench\n    3. Click on your profile picture (Bottom left).\n    4. Click on \"My Settings\".\n    5. Click on \"Developer\".\n    6. Click on \"Generate new token\".\n    7. Copy the token.\n    ": "\n    要獲取您的 AITable 令牌，請按照以下步驟操作：\n\n    1. 登錄到您的 AITable 帳戶。\n    2. 訪問 https://apitable.com/workbench\n    3. 點擊您的頭像（左下角）。\n    4. 點擊「我的設置」。\n    5. 點擊「開發者」。\n    6. 點擊「生成新令牌」。\n    7. 複製令牌。\n    ", "Create Record": "創建記錄", "Update Record": "更新記錄", "Find Records": "查找記錄", "Custom API Call": "自定義 API 調用", "Creates a new record in datasheet.": "在資料表中創建新記錄。", "Updates an existing record in datasheet.": "在資料表中更新現有記錄。", "Finds records in datasheet.": "在資料表中查找記錄。", "Make a custom API call to a specific endpoint": "對特定端點進行自定義 API 調用", "Space": "空間", "Datasheet": "資料表", "Fields": "字段", "Record ID": "記錄 ID", "Record IDs": "記錄 ID 們", "Field Names": "字段名稱", "Max Records": "最大記錄數", "Page Size": "頁面大小", "Page Number": "頁碼", "Filter": "篩選器", "Method": "方法", "Headers": "標頭", "Query Parameters": "查詢參數", "Body": "主體", "No Error on Failure": "失敗時無錯誤", "Timeout (in seconds)": "超時（以秒為單位）", "The fields to add to the record.": "要添加至記錄的字段。", "The ID of the record to update.": "要更新的記錄的 ID。", "The IDs of the records to find.": "要查找的記錄 ID。", "The returned record results are limited to the specified fields": "返回的記錄結果限於指定的字段", "How many records are returned in total": "返回的記錄總數", "How many records are returned per page (max 1000)": "每頁返回多少記錄（最多 1000）", "Specifies the page number of the page": "指定頁面的頁碼", "The filter to apply to the records (see https://help.aitable.ai/docs/guide/manual-formula-field-overview/)": "適用於記錄的篩選器 (參見 https://help.aitable.ai/docs/guide/manual-formula-field-overview/)", "Authorization headers are injected automatically from your connection.": "授權標頭自動從您的連接中注入。", "GET": "GET", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "DELETE", "HEAD": "HEAD", "New Record": "新記錄", "Triggers when a new record is added to a datasheet.": "當新記錄被添加到資料表時觸發。"}
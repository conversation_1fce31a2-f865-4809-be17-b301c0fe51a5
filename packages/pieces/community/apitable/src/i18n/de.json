{"AITable": "AITable", "Interactive spreadsheets with collaboration": "Interaktive Tabellenkalkulationen mit Zusammenarbeit", "Token": "Token", "Instance Url": "Instanz Url", "The token of the AITable account": "Das Token des AITable Accounts", "The url of the AITable instance.": "Die Url der AITable Instanz.", "\n    To obtain your AITable token, follow these steps:\n\n    1. Log in to your AITable account.\n    2. Visit https://apitable.com/workbench\n    3. Click on your profile picture (Bottom left).\n    4. Click on \"My Settings\".\n    5. Click on \"Developer\".\n    6. Click on \"Generate new token\".\n    7. Copy the token.\n    ": "\n    Um Ihr AITable Token zu erhalten, folgen Sie diesen Schritten:\n\n    1. Melden Si<PERSON> sich bei Ihrem AITable Account an.\n    2. Besuchen Sie https://apitable.com/workbench\n    3. Klicken Sie auf Ihr Profilbild (unten links).\n    4. Klicken Sie auf \"Meine Einstellungen\".\n    Klicken Sie auf \"Entwickler\".\n    6. Klicken Sie auf \"Neues Token generieren\".\n    7. Ko<PERSON><PERSON> Si<PERSON> den Token.\n    ", "Create Record": "Datensatz erstellen", "Update Record": "Datensatz aktualisieren", "Find Records": "Datensätze finden", "Custom API Call": "Eigener API-Aufruf", "Creates a new record in datasheet.": "Erstellt einen neuen Datensatz im Datenblatt.", "Updates an existing record in datasheet.": "Aktualisiert einen vorhandenen Datensatz im Datenblatt.", "Finds records in datasheet.": "Findet Datensätze im Datenblatt.", "Make a custom API call to a specific endpoint": "Einen benutzerdefinierten API-Aufruf an einen bestimmten Endpunkt machen", "Space": "<PERSON><PERSON>", "Datasheet": "Datasheet", "Fields": "<PERSON><PERSON>", "Record ID": "Datensatz-ID", "Record IDs": "Datensatz-IDs", "Field Names": "Feldnamen", "Max Records": "<PERSON><PERSON>", "Page Size": "Einträge pro Seite", "Page Number": "Seitenn<PERSON>mer", "Filter": "Filtern", "Method": "<PERSON>e", "Headers": "Kopfzeilen", "Query Parameters": "Abfrageparameter", "Body": "<PERSON><PERSON><PERSON><PERSON>", "No Error on Failure": "<PERSON><PERSON> bei <PERSON>hler", "Timeout (in seconds)": "Timeout (in Sekunden)", "The fields to add to the record.": "<PERSON> Felder, die zum Datensatz hinzugefügt werden sollen.", "The ID of the record to update.": "Die ID des zu aktualisierenden Datensatzes.", "The IDs of the records to find.": "Die IDs der zu findenden Datensätze.", "The returned record results are limited to the specified fields": "Die zurückgegebenen Datensatzergebnisse sind auf die angegebenen Felder beschränkt", "How many records are returned in total": "Wie viele Datensätze insgesamt zurückgegeben werden", "How many records are returned per page (max 1000)": "Wie viele Datensätze pro Seite zurückgegeben werden (max. 1000)", "Specifies the page number of the page": "Gibt die Seitennummer der Seite an", "The filter to apply to the records (see https://help.aitable.ai/docs/guide/manual-formula-field-overview/)": "<PERSON>lter, der auf die Datensätze angewendet wird (siehe https://help.aitable.ai/docs/guide/manual-formula-field-overview/)", "Authorization headers are injected automatically from your connection.": "Autorisierungs-Header werden automatisch von Ihrer Verbindung injiziert.", "GET": "ERHALTEN", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "LÖSCHEN", "HEAD": "HEAD", "New Record": "<PERSON>euer <PERSON>atz", "Triggers when a new record is added to a datasheet.": "Wird aus<PERSON><PERSON><PERSON>, wenn ein neuer Datensatz zu einem Datenblatt hinzugefügt wird."}
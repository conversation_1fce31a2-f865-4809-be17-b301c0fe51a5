{"Ashby": "<PERSON><PERSON><PERSON>", "API key": "<PERSON><PERSON> da <PERSON>", "Custom API Call": "Chamada de API personalizada", "Make a custom API call to a specific endpoint": "Faça uma chamada de API personalizada para um ponto de extremidade específico", "Method": "<PERSON><PERSON><PERSON><PERSON>", "Headers": "Cabeçalhos", "Query Parameters": "Parâ<PERSON><PERSON> da consult<PERSON>", "Body": "<PERSON><PERSON><PERSON><PERSON>", "No Error on Failure": "Nenhum erro no Failure", "Timeout (in seconds)": "Tempo limite (em segundos)", "Authorization headers are injected automatically from your connection.": "Os cabeçalhos de autorização são inseridos automaticamente a partir da sua conexão.", "GET": "OBTER", "POST": "POSTAR", "PATCH": "COMPRAR", "PUT": "COLOCAR", "DELETE": "EXCLUIR", "HEAD": "CABEÇA"}
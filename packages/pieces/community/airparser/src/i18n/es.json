{"Airparser": "Airparser", "Extract structured data from emails, PDFs, or documents with Airparser.": "Extraer datos estructurados de correos electrónicos, PDFs o documentos con Airparser.", "You can find your API key in the Airparser dashboard under Account Settings.": "Puede encontrar su clave API en el panel de control de Airparser en Configuración de la Cuenta.", "Get Data from Document": "Obtener datos del documento", "Upload Document": "Subir Documento", "Retrieves parsed JSON data from a specific document.": "Obtiene datos JSON analizados desde un documento específico.", "Upload a document to an Airparser inbox for parsing.": "Subir un documento a una bandeja de entrada de Airparser para analizarlo.", "Inbox": "Entrada", "Document": "Documento", "File": "Archivo", "File Name": "Nombre del archivo", "Metadata": "Metadatos", "The document file to upload for parsing.": "El archivo de documento a subir para analizar.", "Optional metadata to associate with the document.": "Metadatos opcionales para asociar con el documento.", "Document Parsed": "Documento analizado", "Triggers when a new document is parsed in a specific inbox.": "Dispara cuando un nuevo documento es analizado en una bandeja de entrada específica.", "Markdown": "<PERSON><PERSON>", "## Airparser Webhook Setup\n\t\t\tTo use this trigger, you need to manually set up a webhook in your Airparser account:\n\n\t\t\t1. Login to your Airparser account.\n\t\t\t2. Navigate to **Integrations** > **Webhooks** in the left sidebar.\n\t\t\t3. Enter the following URL in the webhooks field and select **Document Parsed** as webhook trigger:\n\t\t\t```text\n\t\t\t{{webhookUrl}}\n\t\t\t```\n\t\t\t4. Click Save to register the webhook.\n\t\t\t": "## Configuración de Airparser Webhook\n\t\t\tPara utilizar este activador, necesita configurar manualmente un webhook en su cuenta de Airparser:\n\n\t\t\t1. Inicia sesión en tu cuenta de Airparser.\n\t\t\t2. Navega a **Integraciones** > **Webhooks** en la barra lateral izquierda.\n\t\t\t3. Introduzca la siguiente URL en el campo webhooks y seleccione **Document Ansed** como activador de webhook:\n\t\t\t```text\n\t\t\t{{webhookUrl}}\n\t\t\t```\n\t\t\t4. Haga clic en Guardar para registrar el webhook.\n\t\t\t"}
{"Airparser": "<PERSON><PERSON><PERSON>", "Extract structured data from emails, PDFs, or documents with Airparser.": "Pak gestructureerde gegevens uit e-mails, PDF's of documenten samen met Airparser.", "You can find your API key in the Airparser dashboard under Account Settings.": "U kunt uw API-sleutel vinden in het Airparser dashboard onder Accountinstellingen.", "Get Data from Document": "Gegevens uit document ophalen", "Upload Document": "Document uploaden", "Retrieves parsed JSON data from a specific document.": "Haalt de JSON-gegevens op van een specifiek document.", "Upload a document to an Airparser inbox for parsing.": "Upload een document naar een Airparser-postvak om te parsen.", "Inbox": "Inkomend", "Document": "Document", "File": "Bestand", "File Name": "File Name", "Metadata": "Metagegevens", "The document file to upload for parsing.": "Het documentbestand om te uploaden voor parsen.", "Optional metadata to associate with the document.": "Optionele metadata om te koppelen aan het document.", "Document Parsed": "Document verwerkt", "Triggers when a new document is parsed in a specific inbox.": "Triggert wanneer een nieuw document wordt verwerkt in een specifieke inbox.", "Markdown": "<PERSON><PERSON>", "## Airparser Webhook Setup\n\t\t\tTo use this trigger, you need to manually set up a webhook in your Airparser account:\n\n\t\t\t1. Login to your Airparser account.\n\t\t\t2. Navigate to **Integrations** > **Webhooks** in the left sidebar.\n\t\t\t3. Enter the following URL in the webhooks field and select **Document Parsed** as webhook trigger:\n\t\t\t```text\n\t\t\t{{webhookUrl}}\n\t\t\t```\n\t\t\t4. Click Save to register the webhook.\n\t\t\t": "## Airparser Webhook Setup\n\t\t\t・Om deze trigger te gebruiken, moet je handmatig een webhook instellen in je Airparser account:\n\n\t\t\tØ 1. Log in op uw Airparser-account.\n\t\t\t➜ 2. Ga naar **Integraties** > **Webhooks** in de linker zijbalk.\n\t\t\t≤ A33. Voer de volgende URL in het webhooks veld in en selecteer **Document Parsed** als webhook trigger:\n\t\t\tρ```text\n\t\t\tweb+graphie://ka-perseus-graphie.s3.amazonawsρ\n ρA,\t\t\t{{webhookUrl}}\n\t\t\t½ `````\n\t\t\t½ 4. Klik op Opslaan om de webhook te registreren.\n・\t\t\t"}
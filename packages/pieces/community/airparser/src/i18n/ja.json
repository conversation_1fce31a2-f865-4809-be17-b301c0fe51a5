{"Airparser": "Airparser", "Extract structured data from emails, PDFs, or documents with Airparser.": "電子メール、PDF、またはAirparserのドキュメントから構造化されたデータを抽出します。", "You can find your API key in the Airparser dashboard under Account Settings.": "API キーは Airparser ダッシュボードの format@@0 で確認できます。", "Get Data from Document": "ドキュメントからデータを取得する", "Upload Document": "ドキュメントをアップロード", "Retrieves parsed JSON data from a specific document.": "解析された JSON データを特定のドキュメントから取得します。", "Upload a document to an Airparser inbox for parsing.": "解析のためにドキュメントを Airparser 受信トレイにアップロードします。", "Inbox": "受信トレイ", "Document": "ドキュメント", "File": "ファイル", "File Name": "ファイル名", "Metadata": "メタデータ", "The document file to upload for parsing.": "解析用にアップロードするドキュメントファイル。", "Optional metadata to associate with the document.": "ドキュメントに関連付ける任意のメタデータ。", "Document Parsed": "解析された文書", "Triggers when a new document is parsed in a specific inbox.": "新しいドキュメントが特定の受信トレイで解析されたときにトリガーされます。", "Markdown": "<PERSON><PERSON>", "## Airparser Webhook Setup\n\t\t\tTo use this trigger, you need to manually set up a webhook in your Airparser account:\n\n\t\t\t1. Login to your Airparser account.\n\t\t\t2. Navigate to **Integrations** > **Webhooks** in the left sidebar.\n\t\t\t3. Enter the following URL in the webhooks field and select **Document Parsed** as webhook trigger:\n\t\t\t```text\n\t\t\t{{webhookUrl}}\n\t\t\t```\n\t\t\t4. Click Save to register the webhook.\n\t\t\t": "## Airparser Webhook Setup\n\t\t\tTo use this trigger, you need to manually set up a webhook in your Airparser account:\n\n\t\t\t1. Login to your Airparser account.\n\t\t\t2. Navigate to **Integrations** > **Webhooks** in the left sidebar.\n\t\t\t3. Enter the following URL in the webhooks field and select **Document Parsed** as webhook trigger:\n\t\t\t```text\n\t\t\t{{webhookUrl}}\n\t\t\t```\n\t\t\t4. Click Save to register the webhook.\n\t\t\t"}
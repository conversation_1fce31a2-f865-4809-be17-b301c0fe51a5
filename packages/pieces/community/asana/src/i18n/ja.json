{"Asana": "<PERSON><PERSON>", "Work management platform designed to help teams organize, track, and manage their work.": "チームが作業を整理、追跡、管理できるように設計された作業管理プラットフォームです。", "Create Task": "タスクを作成", "Custom API Call": "カスタムAPI通話", "Create a new task": "新しいタスクを作成", "Make a custom API call to a specific endpoint": "特定のエンドポイントへのカスタム API コールを実行します。", "Workspace": "ワークスペース", "Project": "プロジェクト", "Task Name": "タスク名", "Task Description": "タスクの説明", "Due Date": "締切日", "Tags": "タグ", "Assignee": "Assignee", "Method": "方法", "Headers": "ヘッダー", "Query Parameters": "クエリパラメータ", "Body": "本文", "No Error on Failure": "失敗時にエラーはありません", "Timeout (in seconds)": "タイムアウト（秒）", "Asana workspace to create the task in": "タスクを作成するAsana ワークスペース", "Asana Project to create the task in": "タスクを作成するAsana プロジェクト", "The name of the task to create": "作成するタスクの名前", "Free-form textual information associated with the task (i.e. its description).": "タスクに関連付けられたフリーフォームのテキスト情報(すなわち、その説明)。", "The date on which this task is due in any format.": "このタスクが期日を任意の形式で指定します。", "Tags to add to the task": "タスクに追加するタグ", "Assignee for the task": "タスクの担当者", "Authorization headers are injected automatically from your connection.": "認証ヘッダは接続から自動的に注入されます。", "GET": "取得", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "削除", "HEAD": "頭"}
{"Asana": "<PERSON><PERSON>", "Work management platform designed to help teams organize, track, and manage their work.": "Plateforme de gestion du travail conçue pour aider les équipes à organiser, suivre et gérer leur travail.", "Create Task": "<PERSON><PERSON><PERSON> une tâche", "Custom API Call": "Appel API personnalisé", "Create a new task": "<PERSON><PERSON><PERSON> une nouvelle tâche", "Make a custom API call to a specific endpoint": "Passez un appel API personnalisé à un point de terminaison spécifique", "Workspace": "Espace de travail", "Project": "Projet", "Task Name": "Nom de la tâche", "Task Description": "Description de tâche", "Due Date": "Date de fin", "Tags": "Tags", "Assignee": "Assignee", "Method": "Méthode", "Headers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Query Parameters": "Paramètres de requête", "Body": "Corps", "No Error on Failure": "Aucune erreur en cas d'échec", "Timeout (in seconds)": "<PERSON><PERSON><PERSON>atten<PERSON> (en secondes)", "Asana workspace to create the task in": "Espace de travail Asana pour créer la tâche dans", "Asana Project to create the task in": "Asana Projet pour créer la tâche dans", "The name of the task to create": "Le nom de la tâche à créer", "Free-form textual information associated with the task (i.e. its description).": "Information textuelle libre associée à la tâche (c'est-à-dire sa description).", "The date on which this task is due in any format.": "La date à laquelle cette tâche est due dans n'importe quel format.", "Tags to add to the task": "Tags à ajouter à la tâche", "Assignee for the task": "Responsable de la tâche", "Authorization headers are injected automatically from your connection.": "Les en-têtes d'autorisation sont injectés automatiquement à partir de votre connexion.", "GET": "OBTENIR", "POST": "POSTER", "PATCH": "PATCH", "PUT": "EFFACER", "DELETE": "SUPPRIMER", "HEAD": "TÊTE"}
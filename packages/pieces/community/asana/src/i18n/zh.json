{"Asana": "<PERSON><PERSON>", "Work management platform designed to help teams organize, track, and manage their work.": "工作管理平台旨在幫助團隊組織、追踪和管理他們的工作。", "Create Task": "創建任務", "Custom API Call": "自定義 API 呼叫", "Create a new task": "創建新任務", "Make a custom API call to a specific endpoint": "對特定端點進行自定義 API 呼叫", "Workspace": "工作區", "Project": "項目", "Task Name": "任務名稱", "Task Description": "任務描述", "Due Date": "到期日", "Tags": "標籤", "Assignee": "分配人", "Method": "方法", "Headers": "標頭", "Query Parameters": "查詢參數", "Body": "主體", "No Error on Failure": "失敗時無錯誤", "Timeout (in seconds)": "超時（以秒為單位）", "Asana workspace to create the task in": "用於創建任務的 Asana 工作區", "Asana Project to create the task in": "用於創建任務的 Asana 項目", "The name of the task to create": "要創建的任務名稱", "Free-form textual information associated with the task (i.e. its description).": "與任務相關的自由形式文本信息（即其描述）。", "The date on which this task is due in any format.": "在任意格式下，此任務的到期日期。", "Tags to add to the task": "要添加到任務的標籤", "Assignee for the task": "任務的分配人", "Authorization headers are injected automatically from your connection.": "授權標頭自動從您的連接中注入。", "GET": "GET", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "DELETE", "HEAD": "HEAD"}
{"Asana": "<PERSON><PERSON>", "Work management platform designed to help teams organize, track, and manage their work.": "Work management platform designed to help teams organize, track, and manage their work.", "Create Task": "Create Task", "Custom API Call": "Custom API Call", "Create a new task": "Create a new task", "Make a custom API call to a specific endpoint": "Make a custom API call to a specific endpoint", "Workspace": "Workspace", "Project": "Project", "Task Name": "Task Name", "Task Description": "Task Description", "Due Date": "Due Date", "Tags": "Tags", "Assignee": "Assignee", "Method": "Method", "Headers": "Headers", "Query Parameters": "Query Parameters", "Body": "Body", "No Error on Failure": "No Error on Failure", "Timeout (in seconds)": "Timeout (in seconds)", "Asana workspace to create the task in": "Asana workspace to create the task in", "Asana Project to create the task in": "Asana Project to create the task in", "The name of the task to create": "The name of the task to create", "Free-form textual information associated with the task (i.e. its description).": "Free-form textual information associated with the task (i.e. its description).", "The date on which this task is due in any format.": "The date on which this task is due in any format.", "Tags to add to the task": "Tags to add to the task", "Assignee for the task": "Assignee for the task", "Authorization headers are injected automatically from your connection.": "Authorization headers are injected automatically from your connection.", "GET": "GET", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "DELETE", "HEAD": "HEAD"}
{"Asana": "<PERSON><PERSON>", "Work management platform designed to help teams organize, track, and manage their work.": "Werken platform ontworpen om teams te helpen hun werk te organiseren, te volgen en te beheren.", "Create Task": "<PERSON><PERSON> maken", "Custom API Call": "Custom API Call", "Create a new task": "<PERSON><PERSON> nieuwe taak aan<PERSON>ken", "Make a custom API call to a specific endpoint": "Maak een aangepaste API call naar een specifiek eindpunt", "Workspace": "werkruimte", "Project": "Project", "Task Name": "<PERSON><PERSON> naam", "Task Description": "Be<PERSON><PERSON><PERSON>ving taak", "Due Date": "Inleverdatum", "Tags": "Labels", "Assignee": "Assignee", "Method": "<PERSON>e", "Headers": "Kopteksten", "Query Parameters": "Query parameters", "Body": "<PERSON><PERSON><PERSON>", "No Error on Failure": "<PERSON><PERSON> fout bij fout", "Timeout (in seconds)": "Time-out (in seconden)", "Asana workspace to create the task in": "Asana workspace om een taak aan te maken in", "Asana Project to create the task in": "Asana project om de taak aan te maken in", "The name of the task to create": "<PERSON> naam van de taak te maken", "Free-form textual information associated with the task (i.e. its description).": "Vrije tekstuele informatie die is gekoppeld aan de taak (d.w.z. de beschrijving).", "The date on which this task is due in any format.": "De datum waarop deze taak in elke notatie vervalt", "Tags to add to the task": "Tags om toe te voegen aan de taak", "Assignee for the task": "Toegewezen voor de taak", "Authorization headers are injected automatically from your connection.": "Autorisatie headers worden automatisch geïnjecteerd vanuit uw verbinding.", "GET": "KRIJG", "POST": "POSTE", "PATCH": "BEKIJK", "PUT": "PUT", "DELETE": "VERWIJDEREN", "HEAD": "HOOFD"}
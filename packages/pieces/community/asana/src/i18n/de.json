{"Asana": "<PERSON><PERSON>", "Work management platform designed to help teams organize, track, and manage their work.": "Work-Management-Platt<PERSON> entwickelt, um Teams zu helfen, ihre Arbeit zu organisieren, zu verfolgen und zu verwalten.", "Create Task": "Aufgabe erstellen", "Custom API Call": "Eigener API-Aufruf", "Create a new task": "Neue Aufgabe erstellen", "Make a custom API call to a specific endpoint": "Einen benutzerdefinierten API-Aufruf an einen bestimmten Endpunkt machen", "Workspace": "Arbeitsbereich", "Project": "Projekt", "Task Name": "Aufgabenname", "Task Description": "Aufgabenbeschreibung", "Due Date": "Fälligkeitsdatum", "Tags": "Tags", "Assignee": "Assignee", "Method": "<PERSON>e", "Headers": "Kopfzeilen", "Query Parameters": "Abfrageparameter", "Body": "<PERSON><PERSON><PERSON><PERSON>", "No Error on Failure": "<PERSON><PERSON> bei <PERSON>hler", "Timeout (in seconds)": "Timeout (in Sekunden)", "Asana workspace to create the task in": "<PERSON>ana Arbeitsbereich zum Erstellen der Aufgabe in", "Asana Project to create the task in": "Asana Projekt zum Erstellen der Aufgabe in", "The name of the task to create": "Der Name der zu erstellenden Aufgabe", "Free-form textual information associated with the task (i.e. its description).": "Free-form textuelle Informationen, die mit der Aufgabe verbunden sind (d.h. deren Beschreibung).", "The date on which this task is due in any format.": "<PERSON> Datum, an dem diese Aufgabe in jedem Format fällig ist.", "Tags to add to the task": "Tags zum Hinzufügen der Aufgabe", "Assignee for the task": "Beauftragter für die Aufgabe", "Authorization headers are injected automatically from your connection.": "Autorisierungs-Header werden automatisch von Ihrer Verbindung injiziert.", "GET": "ERHALTEN", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "LÖSCHEN", "HEAD": "HEAD"}
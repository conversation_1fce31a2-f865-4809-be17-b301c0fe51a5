{"Asana": "<PERSON><PERSON>", "Work management platform designed to help teams organize, track, and manage their work.": "Платформа управления работой, предназначенная для того, чтобы помочь командам организовать, отслеживать и управлять своей работой.", "Create Task": "Создать задачу", "Custom API Call": "Пользовательский вызов API", "Create a new task": "Создать новую задачу", "Make a custom API call to a specific endpoint": "Сделать пользовательский API вызов к определенной конечной точке", "Workspace": "Рабочая область", "Project": "Проект", "Task Name": "Название задачи", "Task Description": "Описание задачи", "Due Date": "Срок сдачи", "Tags": "Теги", "Assignee": "Assignee", "Method": "Метод", "Headers": "Заголовки", "Query Parameters": "Параметры запроса", "Body": "Тело", "No Error on Failure": "Нет ошибок при ошибке", "Timeout (in seconds)": "Таймаут (в секундах)", "Asana workspace to create the task in": "Асана рабочая область для создания задачи в", "Asana Project to create the task in": "Асана Проект для создания задачи в", "The name of the task to create": "Имя создаваемой задачи", "Free-form textual information associated with the task (i.e. its description).": "Текстовая информация, связанная с задачей (т. е. ее описание).", "The date on which this task is due in any format.": "Дата выполнения этой задачи в любом формате.", "Tags to add to the task": "Теги для добавления к задаче", "Assignee for the task": "Назначен для задачи", "Authorization headers are injected automatically from your connection.": "Заголовки авторизации включаются автоматически из вашего соединения.", "GET": "ПОЛУЧИТЬ", "POST": "ПОСТ", "PATCH": "ПАТЧ", "PUT": "ПОКУПИТЬ", "DELETE": "УДАЛИТЬ", "HEAD": "HEAD"}
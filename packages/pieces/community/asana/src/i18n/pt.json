{"Asana": "<PERSON><PERSON>", "Work management platform designed to help teams organize, track, and manage their work.": "Plataforma de gestão de trabalho projetada para ajudar as equipes a organizar, acompanhar e gerenciar seu trabalho.", "Create Task": "<PERSON><PERSON><PERSON> tarefa", "Custom API Call": "Chamada de API personalizada", "Create a new task": "Criar uma nova tarefa", "Make a custom API call to a specific endpoint": "Faça uma chamada de API personalizada para um ponto de extremidade específico", "Workspace": "Workspace", "Project": "Projecto", "Task Name": "Nome da tarefa", "Task Description": "Descrição da tarefa", "Due Date": "Data de vencimento", "Tags": "Etiquetas", "Assignee": "Assignee", "Method": "<PERSON><PERSON><PERSON><PERSON>", "Headers": "Cabeçalhos", "Query Parameters": "Parâ<PERSON><PERSON> da consult<PERSON>", "Body": "<PERSON><PERSON><PERSON><PERSON>", "No Error on Failure": "Nenhum erro no Failure", "Timeout (in seconds)": "Tempo limite (em segundos)", "Asana workspace to create the task in": "Espaço de trabalho de Asana para criar a tarefa", "Asana Project to create the task in": "Projeto Asana para criar a tarefa em", "The name of the task to create": "O nome da tarefa a criar", "Free-form textual information associated with the task (i.e. its description).": "Informações de texto de forma livre associadas à tarefa (por exemplo, sua descrição).", "The date on which this task is due in any format.": "A data em que esta tarefa expira em qualquer formato.", "Tags to add to the task": "Tags a serem adicionadas à tarefa", "Assignee for the task": "Atribuído para a tarefa", "Authorization headers are injected automatically from your connection.": "Os cabeçalhos de autorização são inseridos automaticamente a partir da sua conexão.", "GET": "OBTER", "POST": "POSTAR", "PATCH": "COMPRAR", "PUT": "COLOCAR", "DELETE": "EXCLUIR", "HEAD": "CABEÇA"}
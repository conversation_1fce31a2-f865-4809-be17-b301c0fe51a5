{"Asana": "<PERSON><PERSON>", "Work management platform designed to help teams organize, track, and manage their work.": "Plataforma de gestión de trabajo diseñada para ayudar a los equipos a organizar, rastrear y gestionar su trabajo.", "Create Task": "<PERSON><PERSON><PERSON> tarea", "Custom API Call": "Llamada API personalizada", "Create a new task": "Crear una nueva tarea", "Make a custom API call to a specific endpoint": "Hacer una llamada API personalizada a un extremo específico", "Workspace": "Espacio de trabajo", "Project": "Projekt", "Task Name": "Nombre de tarea", "Task Description": "Descripción de la tarea", "Due Date": "<PERSON><PERSON> de fin", "Tags": "Etiquetas", "Assignee": "Assignee", "Method": "<PERSON><PERSON><PERSON><PERSON>", "Headers": "Encabezados", "Query Parameters": "Parámetros de consulta", "Body": "<PERSON><PERSON><PERSON>", "No Error on Failure": "No hay ningún error en fallo", "Timeout (in seconds)": "Tiempo de espera (en segundos)", "Asana workspace to create the task in": "Área de trabajo de Asana en la que crear la tarea", "Asana Project to create the task in": "Proyecto Asana para crear la tarea en", "The name of the task to create": "El nombre de la tarea a crear", "Free-form textual information associated with the task (i.e. its description).": "Información textual de forma libre asociada a la tarea (es decir, su descripción).", "The date on which this task is due in any format.": "La fecha en la que esta tarea se debe en cualquier formato.", "Tags to add to the task": "Etiquetas a añadir a la tarea", "Assignee for the task": "Asignado para la tarea", "Authorization headers are injected automatically from your connection.": "Las cabeceras de autorización se inyectan automáticamente desde tu conexión.", "GET": "RECOGER", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "BORRAR", "HEAD": "LIMPIO"}
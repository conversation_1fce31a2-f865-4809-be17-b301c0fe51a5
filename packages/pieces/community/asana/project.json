{"name": "pieces-asana", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/pieces/community/asana/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/pieces/community/asana", "tsConfig": "packages/pieces/community/asana/tsconfig.lib.json", "packageJson": "packages/pieces/community/asana/package.json", "main": "packages/pieces/community/asana/src/index.ts", "assets": ["packages/pieces/community/asana/*.md", {"input": "packages/pieces/community/asana/src/i18n", "output": "./src/i18n", "glob": "**/!(i18n.json)"}], "buildableProjectDepsInPackageJsonType": "dependencies", "updateBuildableProjectDepsInPackageJson": true}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}, "tags": []}
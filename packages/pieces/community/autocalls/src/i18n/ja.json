{"Autocalls": "自動通話", "Automate phone calls using our AI calling platform.": "AI通話プラットフォームを使用して通話を自動化します。", "Create an API key in your Autocalls account and paste the value here. Get API key here -> https://app.autocalls.ai.": "あなたの自動呼び出しアカウントにAPIキーを作成し、ここに値を貼り付けます。ここでAPIキーを取得-> https://app.autocalls.ai。", "Add lead to a campaign": "キャンペーンにリードを追加", "Send SMS to Customer": "顧客にSMSを送信", "Start/Stop Campaign": "キャンペーンの開始/停止", "Make Phone Call": "電話をかける", "Delete Lead": "リードを削除", "Add lead to an outbound campaign, to be called by an assistant from our platform.": "アウトバウンドキャンペーンにリードを追加し、私たちのプラットフォームからアシスタントによって呼び出されます。", "Send an SMS to a customer using a phone number from our platform.": "私たちのプラットフォームから電話番号を使用して顧客にSMSを送信します。", "Start or stop an outbound campaign from our platform.": "私たちのプラットフォームからアウトバウンドキャンペーンを開始または停止します。", "Call a customer by it's phone number using an assistant from our platform.": "当社のプラットフォームのアシスタントを使用して、電話番号でお客様に電話をかけます。", "Delete a lead from a campaign.": "キャンペーンからリードを削除します。", "Campaign": "キャンペーン", "Customer phone number": "顧客電話番号", "Variables": "変数", "Allow duplicates": "重複を許可", "Number of Secondary Contacts": "セカンダリ連絡先の数", "Secondary Contacts": "セカンダリ連絡先", "From phone number": "電話番号から", "Text message": "テキスト メッセージ", "Action": "アクション", "Assistant": "アシスタント", "Lead": "リード", "Select a campaign": "キャンペーンを選択", "Enter the phone number of the customer": "顧客の電話番号を入力", "Variables to pass to the assistant": "アシスタントに渡す変数", "Allow the same phone number to be added to the campaign more than once": "同じ電話番号をキャンペーンに複数回追加することを許可する", "How many secondary contacts do you want to add?": "セカンダリコンタクトを追加しますか？", "Add secondary contacts for this lead. Each contact can have its own phone number and variables.": "このリードの二次連絡先を追加します。各連絡先には独自の電話番号と変数を設定できます。", "Select an SMS capable phone number to send the SMS from": "SMSを送信するSMS対応の電話番号を選択してください", "Enter the text message to send to the customer (max 300 characters)": "顧客に送信するテキストメッセージを入力してください（最大300文字）", "Select action to perform on the campaign": "キャンペーンで実行するアクションを選択", "Select an assistant": "アシスタントを選択", "Select a lead to delete": "削除する見込み客を選択", "Start Campaign": "キャンペーンを開始", "Stop Campaign": "キャンペーンの停止", "Phone Call Ended": "通話が終了しました", "Updated Assistant": "更新されたアシスタント", "Inbound Call": "着信履歴", "Triggers when a phone call ends, with extracted variables.": "電話が終了したときに、抽出された変数でトリガーします。", "Triggers when assistants are fetched or updated in your Autocalls account.": "自動通話アカウントでアシスタントが取得または更新されたときにトリガーされます。", "Triggers for variables before connecting an inbound call.": "受信呼び出しを接続する前に変数をトリガーします。", "Start Date": "開始日", "End Date": "終了日", "Filter assistants created after this date. Example: 2024-01-15T10:30:00Z": "この日付の後に作成されたフィルターアシスタント。例: 2024-01-15T10:30:00Z", "Filter assistants created before this date. Example: 2024-12-31T23:59:59Z": "この日付の前に作成されたフィルターアシスタント。例: 2024-12-31T23:59:59Z"}
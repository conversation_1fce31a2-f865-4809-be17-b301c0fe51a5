{"Autocalls": "Автоповороты", "Automate phone calls using our AI calling platform.": "Автоматизировать телефонные звонки, используя нашу платформу для вызова AI.", "Create an API key in your Autocalls account and paste the value here. Get API key here -> https://app.autocalls.ai.": "Создайте API ключ в вашей учетной записи Autocalls и вставьте сюда значение. Получить API ключ здесь -> https://app.autocalls.ai.", "Add lead to a campaign": "Добавить провод к кампании", "Send SMS to Customer": "Отправить SMS клиенту", "Start/Stop Campaign": "Начать/остановить Кампанию", "Make Phone Call": "Позвонить по телефону", "Delete Lead": "Удалить предв. контакт", "Add lead to an outbound campaign, to be called by an assistant from our platform.": "Добавьте к исходящей кампании, которую будет вызвать помощник с нашей платформы.", "Send an SMS to a customer using a phone number from our platform.": "Отправьте клиенту SMS по телефону с нашей платформы.", "Start or stop an outbound campaign from our platform.": "Начать или остановить исходящую кампанию с нашей платформы.", "Call a customer by it's phone number using an assistant from our platform.": "Позвоните клиенту по номеру телефона, используя ассистент с нашей платформы.", "Delete a lead from a campaign.": "Удалить свинца из кампании.", "Campaign": "Кампания", "Customer phone number": "Телефон клиента", "Variables": "Переменные", "Allow duplicates": "Разрешить дубликаты", "Number of Secondary Contacts": "Количество дополнительных контактов", "Secondary Contacts": "Вторичные контакты", "From phone number": "От номера телефона", "Text message": "Текстовое сообщение", "Action": "Действие", "Assistant": "Помощник", "Lead": "Предв. контакт", "Select a campaign": "Выберите кампанию", "Enter the phone number of the customer": "Введите номер телефона клиента", "Variables to pass to the assistant": "Переменные для передачи помощнику", "Allow the same phone number to be added to the campaign more than once": "Разрешить добавление одного и того же номера телефона в кампанию более одного раза", "How many secondary contacts do you want to add?": "Сколько дополнительных контактов вы хотите добавить?", "Add secondary contacts for this lead. Each contact can have its own phone number and variables.": "Добавьте дополнительные контакты для этого провода. Каждый контакт может иметь свой собственный номер телефона и переменные.", "Select an SMS capable phone number to send the SMS from": "Выберите номер телефона с поддержкой SMS для отправки SMS от", "Enter the text message to send to the customer (max 300 characters)": "Введите текстовое сообщение для отправки клиенту (не более 300 символов)", "Select action to perform on the campaign": "Выберите действие для выполнения кампании", "Select an assistant": "Выберите помощника", "Select a lead to delete": "Выберите провод для удаления", "Start Campaign": "Начать кампанию", "Stop Campaign": "Остановить Кампанию", "Phone Call Ended": "Телефонный вызов завершен", "Updated Assistant": "Обновленный помощник", "Inbound Call": "Входящий вызов", "Triggers when a phone call ends, with extracted variables.": "Включает при завершении телефонного вызова с извлеченными переменными.", "Triggers when assistants are fetched or updated in your Autocalls account.": "Срабатывает при получении или обновлении ассистентов в вашем аккаунте Autocall.", "Triggers for variables before connecting an inbound call.": "Триггеры для переменных перед подключением входящего вызова.", "Start Date": "Дата начала", "End Date": "Дата окончания", "Filter assistants created after this date. Example: 2024-01-15T10:30:00Z": "Фильтр помощников, созданных после этой даты. Пример: 2024-01-15T10:30:00Z", "Filter assistants created before this date. Example: 2024-12-31T23:59:59Z": "Фильтр помощников, созданных до этой даты. Пример: 2024-12-31T23:59:59Z"}
{"Autocalls": "Autocalls", "Automate phone calls using our AI calling platform.": "Automate phone calls using our AI calling platform.", "Create an API key in your Autocalls account and paste the value here. Get API key here -> https://app.autocalls.ai.": "Create an API key in your Autocalls account and paste the value here. Get API key here -> https://app.autocalls.ai.", "Add lead to a campaign": "Add lead to a campaign", "Send SMS to Customer": "Send SMS to Customer", "Start/Stop Campaign": "Start/Stop Campaign", "Make Phone Call": "Make Phone Call", "Delete Lead": "Delete Lead", "Add lead to an outbound campaign, to be called by an assistant from our platform.": "Add lead to an outbound campaign, to be called by an assistant from our platform.", "Send an SMS to a customer using a phone number from our platform.": "Send an SMS to a customer using a phone number from our platform.", "Start or stop an outbound campaign from our platform.": "Start or stop an outbound campaign from our platform.", "Call a customer by it's phone number using an assistant from our platform.": "Call a customer by it's phone number using an assistant from our platform.", "Delete a lead from a campaign.": "Delete a lead from a campaign.", "Campaign": "Campaign", "Customer phone number": "Customer phone number", "Variables": "Variables", "Allow duplicates": "Allow duplicates", "Number of Secondary Contacts": "Number of Secondary Contacts", "Secondary Contacts": "Secondary Contacts", "From phone number": "From phone number", "Text message": "Text message", "Action": "Action", "Assistant": "Assistant", "Lead": "Lead", "Select a campaign": "Select a campaign", "Enter the phone number of the customer": "Enter the phone number of the customer", "Variables to pass to the assistant": "Variables to pass to the assistant", "Allow the same phone number to be added to the campaign more than once": "Allow the same phone number to be added to the campaign more than once", "How many secondary contacts do you want to add?": "How many secondary contacts do you want to add?", "Add secondary contacts for this lead. Each contact can have its own phone number and variables.": "Add secondary contacts for this lead. Each contact can have its own phone number and variables.", "Select an SMS capable phone number to send the SMS from": "Select an SMS capable phone number to send the SMS from", "Enter the text message to send to the customer (max 300 characters)": "Enter the text message to send to the customer (max 300 characters)", "Select action to perform on the campaign": "Select action to perform on the campaign", "Select an assistant": "Select an assistant", "Select a lead to delete": "Select a lead to delete", "Start Campaign": "Start Campaign", "Stop Campaign": "Stop Campaign", "Phone Call Ended": "Phone Call Ended", "Updated Assistant": "Updated Assistant", "Inbound Call": "Inbound Call", "Triggers when a phone call ends, with extracted variables.": "Triggers when a phone call ends, with extracted variables.", "Triggers when assistants are fetched or updated in your Autocalls account.": "Triggers when assistants are fetched or updated in your Autocalls account.", "Triggers for variables before connecting an inbound call.": "Triggers for variables before connecting an inbound call.", "Start Date": "Start Date", "End Date": "End Date", "Filter assistants created after this date. Example: 2024-01-15T10:30:00Z": "Filter assistants created after this date. Example: 2024-01-15T10:30:00Z", "Filter assistants created before this date. Example: 2024-12-31T23:59:59Z": "Filter assistants created before this date. Example: 2024-12-31T23:59:59Z"}
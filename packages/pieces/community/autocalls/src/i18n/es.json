{"Autocalls": "<PERSON><PERSON><PERSON><PERSON>", "Automate phone calls using our AI calling platform.": "Automatiza las llamadas telefónicas usando nuestra plataforma de llamadas de IA.", "Create an API key in your Autocalls account and paste the value here. Get API key here -> https://app.autocalls.ai.": "Crea una clave API en tu cuenta de Autocalls y pega el valor aquí. Obtén la clave API aquí -> https://app.autocalls.ai.", "Add lead to a campaign": "<PERSON><PERSON><PERSON> lead a una campaña", "Send SMS to Customer": "Enviar SMS al cliente", "Start/Stop Campaign": "Inicio/Detener Campaña", "Make Phone Call": "<PERSON><PERSON> llamada telefónica", "Delete Lead": "Eliminar plomo", "Add lead to an outbound campaign, to be called by an assistant from our platform.": "Añadir a una campaña saliente, a ser llamada por un asistente de nuestra plataforma.", "Send an SMS to a customer using a phone number from our platform.": "Enviar un SMS a un cliente usando un número de teléfono de nuestra plataforma.", "Start or stop an outbound campaign from our platform.": "Inicie o detenga una campaña de salida desde nuestra plataforma.", "Call a customer by it's phone number using an assistant from our platform.": "Llame a un cliente por su número de teléfono usando un asistente de nuestra plataforma.", "Delete a lead from a campaign.": "Eliminar un cliente potencial de una campaña.", "Campaign": "Campaña", "Customer phone number": "Número de teléfono del cliente", "Variables": "Variables", "Allow duplicates": "<PERSON><PERSON><PERSON>", "Number of Secondary Contacts": "Número de contactos secundarios", "Secondary Contacts": "Contactos secundarios", "From phone number": "Desde el número de teléfono", "Text message": "Mensaje de texto", "Action": "Accin", "Assistant": "<PERSON><PERSON><PERSON>", "Lead": "Plomo", "Select a campaign": "Seleccione una campaña", "Enter the phone number of the customer": "Introduzca el número de teléfono del cliente", "Variables to pass to the assistant": "Variables para pasar al asistente", "Allow the same phone number to be added to the campaign more than once": "Permitir que se añada el mismo número de teléfono a la campaña más de una vez", "How many secondary contacts do you want to add?": "¿Cuántos contactos secundarios quieres añadir?", "Add secondary contacts for this lead. Each contact can have its own phone number and variables.": "Añadir contactos secundarios para este cliente potencial. Cada contacto puede tener su propio número de teléfono y variables.", "Select an SMS capable phone number to send the SMS from": "Seleccione un número de teléfono con capacidad de SMS desde el que enviar el SMS", "Enter the text message to send to the customer (max 300 characters)": "Introduzca el mensaje de texto para enviar al cliente (máximo 300 caracteres)", "Select action to perform on the campaign": "Seleccione la acción a realizar en la campaña", "Select an assistant": "Seleccione un asistente", "Select a lead to delete": "Seleccione un cliente potencial para eliminar", "Start Campaign": "<PERSON><PERSON><PERSON> camp<PERSON>", "Stop Campaign": "Detener campaña", "Phone Call Ended": "Llamada terminada", "Updated Assistant": "<PERSON>istente actualizado", "Inbound Call": "Llamada entrante", "Triggers when a phone call ends, with extracted variables.": "Dispara cuando termina una llamada telefónica, con variables extraídas.", "Triggers when assistants are fetched or updated in your Autocalls account.": "Dispara cuando los asistentes son recuperados o actualizados en su cuenta de llamadas automáticas.", "Triggers for variables before connecting an inbound call.": "Desencadenadores para variables antes de conectar una llamada entrante.", "Start Date": "Fecha de inicio", "End Date": "<PERSON><PERSON> de fin", "Filter assistants created after this date. Example: 2024-01-15T10:30:00Z": "Filtrar asistentes creados después de esta fecha. Ejemplo: 2024-01-15T10:30:00Z", "Filter assistants created before this date. Example: 2024-12-31T23:59:59Z": "Filtrar asistentes creados antes de esta fecha. Ejemplo: 2024-12-31T23:59:59Z"}
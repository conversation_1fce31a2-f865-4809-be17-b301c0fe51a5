{"Autocalls": "Automatisch bellen", "Automate phone calls using our AI calling platform.": "Automatise<PERSON> telefoongesp<PERSON><PERSON> met be<PERSON><PERSON> van ons AI belplatform.", "Create an API key in your Autocalls account and paste the value here. Get API key here -> https://app.autocalls.ai.": "Maak een API-sleutel aan in je Autocalls account en plak de waarde hier. Ontvang hier een API key -> https://app.autocalls.ai.", "Add lead to a campaign": "Voeg lead toe aan een campagne", "Send SMS to Customer": "Stuur SMS naar de klant", "Start/Stop Campaign": "Start/Stop campagne", "Make Phone Call": "Telefoon bellen", "Delete Lead": "Verwijder Lead", "Add lead to an outbound campaign, to be called by an assistant from our platform.": "<PERSON>oeg lead toe aan een uitgaande campagne, die moet worden opgeroepen door een assistent van ons platform.", "Send an SMS to a customer using a phone number from our platform.": "Stuur een SMS naar een klant met een telefoonnummer vanaf ons platform.", "Start or stop an outbound campaign from our platform.": "Start of stop een uitgaande campagne van ons platform.", "Call a customer by it's phone number using an assistant from our platform.": "<PERSON> een klant via zijn telefoonnummer met behul<PERSON> van een assistent vanaf ons platform.", "Delete a lead from a campaign.": "<PERSON><PERSON><PERSON><PERSON><PERSON> een lead uit een campagne.", "Campaign": "Campagne", "Customer phone number": "Telefoonnummer klant", "Variables": "Variabelen", "Allow duplicates": "<PERSON><PERSON><PERSON>", "Number of Secondary Contacts": "Aantal secundaire contacten", "Secondary Contacts": "Secundaire contactpersonen", "From phone number": "Van telefoonnummer", "Text message": "Tekst bericht", "Action": "actie", "Assistant": "Assistent", "Lead": "<PERSON><PERSON>", "Select a campaign": "Selecteer een campagne", "Enter the phone number of the customer": "<PERSON><PERSON>r het telefoon<PERSON><PERSON> van de <PERSON> in", "Variables to pass to the assistant": "Variabelen om door te geven aan de assistent", "Allow the same phone number to be added to the campaign more than once": "To<PERSON>an dat hetzelfde telefoonnummer meer dan één keer aan de campagne wordt toegevoegd", "How many secondary contacts do you want to add?": "Hoeveel secundaire contacten wilt u toevoegen?", "Add secondary contacts for this lead. Each contact can have its own phone number and variables.": "Voeg secundaire contacten toe voor deze lead. Elk contact kan een eigen telefoonnummer en variabelen hebben.", "Select an SMS capable phone number to send the SMS from": "Selecteer een SMS nummer dat in staat is om de SMS van", "Enter the text message to send to the customer (max 300 characters)": "<PERSON><PERSON>r het tekstbericht in om naar de klant te sturen (max 300 tekens)", "Select action to perform on the campaign": "Selecteer actie om uit te voeren op de campagne", "Select an assistant": "Selecteer een assistent", "Select a lead to delete": "Selecteer een lead om te verwijderen", "Start Campaign": "Start Campagne", "Stop Campaign": "Stoppen campagne", "Phone Call Ended": "Telefoon gesprek beëindigd", "Updated Assistant": "Bijgewerkte assistent", "Inbound Call": "Inkomende oproep", "Triggers when a phone call ends, with extracted variables.": "<PERSON><PERSON> wanneer een telefoongesp<PERSON> eindigt, met uitgepakte variabelen.", "Triggers when assistants are fetched or updated in your Autocalls account.": "Triggers wanneer assistenten worden opgehaald of bijgewerkt in uw Autocalls account.", "Triggers for variables before connecting an inbound call.": "Triggert voor variabelen voor het verbinden van een inkomende oproep.", "Start Date": "Start datum", "End Date": "Eind datum", "Filter assistants created after this date. Example: 2024-01-15T10:30:00Z": "Filter assistenten gemaakt na deze datum. Voorbeeld: 2024-01-15T10:30:00Z", "Filter assistants created before this date. Example: 2024-12-31T23:59:59Z": "Filter assistenten gemaakt voor deze datum. Voorbeeld: 2024-12-31T23:59:59Z"}
{"Autocalls": "Appels automatiques", "Automate phone calls using our AI calling platform.": "Automatiser les appels téléphoniques à l'aide de notre plateforme d'appel IA.", "Create an API key in your Autocalls account and paste the value here. Get API key here -> https://app.autocalls.ai.": "Créez une clé API dans votre compte Autocalls et collez la valeur ici. Obtenez la clé API ici -> https://app.autocalls.ai.", "Add lead to a campaign": "Ajouter un prospect à une campagne", "Send SMS to Customer": "Envoyer un SMS au client", "Start/Stop Campaign": "D<PERSON><PERSON>rer/Arr<PERSON>ter <PERSON> campagne", "Make Phone Call": "Passer un appel téléphonique", "Delete Lead": "Supprimer Prospect", "Add lead to an outbound campaign, to be called by an assistant from our platform.": "Ajouter un lien vers une campagne sortante qui sera appelée par un assistant de notre plateforme.", "Send an SMS to a customer using a phone number from our platform.": "Envoyez un SMS à un client en utilisant un numéro de téléphone depuis notre plateforme.", "Start or stop an outbound campaign from our platform.": "<PERSON><PERSON><PERSON><PERSON> ou arrêtez une campagne sortante depuis notre plateforme.", "Call a customer by it's phone number using an assistant from our platform.": "Appelez un client par son numéro de téléphone à l'aide d'un assistant de notre plateforme.", "Delete a lead from a campaign.": "Supprimer un prospect d'une campagne.", "Campaign": "Campagnes", "Customer phone number": "Numéro de téléphone du client", "Variables": "Variables", "Allow duplicates": "Autoriser les doublons", "Number of Secondary Contacts": "Nombre de contacts secondaires", "Secondary Contacts": "Contacts secondaires", "From phone number": "À partir du numéro de téléphone", "Text message": "Message SMS", "Action": "Action", "Assistant": "Assistant", "Lead": "Prospect", "Select a campaign": "Sélectionnez une campagne", "Enter the phone number of the customer": "Entrez le numéro de téléphone du client", "Variables to pass to the assistant": "Variables à passer à l'assistant", "Allow the same phone number to be added to the campaign more than once": "Autoriser l'ajout du même numéro de téléphone à la campagne plus d'une fois", "How many secondary contacts do you want to add?": "Comb<PERSON> de contacts secondaires voulez-vous ajouter ?", "Add secondary contacts for this lead. Each contact can have its own phone number and variables.": "Ajouter des contacts secondaires pour ce prospect. Chaque contact peut avoir son propre numéro de téléphone et ses propres variables.", "Select an SMS capable phone number to send the SMS from": "Sélectionnez un numéro de téléphone capable de SMS à partir duquel envoyer le SMS", "Enter the text message to send to the customer (max 300 characters)": "Entrez le message texte à envoyer au client (max 300 caractères)", "Select action to perform on the campaign": "Sélectionnez l'action à effectuer sur la campagne", "Select an assistant": "Sélectionnez un assistant", "Select a lead to delete": "Sélectionnez un prospect à supprimer", "Start Campaign": "Lancer la campagne", "Stop Campaign": "Arrêter la campagne", "Phone Call Ended": "<PERSON><PERSON> terminé", "Updated Assistant": "Assistant mis à jour", "Inbound Call": "A<PERSON> entrant", "Triggers when a phone call ends, with extracted variables.": "Déclenche lorsqu'un appel téléphonique se termine, avec des variables extraites.", "Triggers when assistants are fetched or updated in your Autocalls account.": "Déclenche lorsque les assistants sont récupérés ou mis à jour dans votre compte Autocalls.", "Triggers for variables before connecting an inbound call.": "Déclenche les variables avant de connecter un appel entrant.", "Start Date": "Date de début", "End Date": "Date de fin", "Filter assistants created after this date. Example: 2024-01-15T10:30:00Z": "Filtrer les assistants créés après cette date. Exemple: 2024-01-15T10:30:00Z", "Filter assistants created before this date. Example: 2024-12-31T23:59:59Z": "Filtrer les assistants créés avant cette date. Exemple: 2024-12-31T23:59:59Z"}
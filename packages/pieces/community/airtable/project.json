{"name": "pieces-airtable", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/pieces/community/airtable/src", "projectType": "library", "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/pieces/community/airtable", "tsConfig": "packages/pieces/community/airtable/tsconfig.lib.json", "packageJson": "packages/pieces/community/airtable/package.json", "main": "packages/pieces/community/airtable/src/index.ts", "assets": ["packages/pieces/community/airtable/*.md", {"input": "packages/pieces/community/airtable/src/i18n", "output": "./src/i18n", "glob": "**/!(i18n.json)"}], "buildableProjectDepsInPackageJsonType": "dependencies", "updateBuildableProjectDepsInPackageJson": true}}}, "tags": []}
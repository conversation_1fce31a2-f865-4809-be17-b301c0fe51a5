{"Airtable": "Airtable", "Low‒code platform to build apps.": "Plataforma de bajo código para construir aplicaciones.", "\n    To obtain your personal token, follow these steps:\n\n    1. Log in to your Airtable account.\n    2. Visit https://airtable.com/create/tokens/ to create one\n    3. Click on \"+ Add a base\" and select the base you want to use or all bases.\n    4. Click on \"+ Add a scope\" and select \"data.records.read\", \"data.records.write\" and \"schema.bases.read\".\n    5. Click on \"Create token\" and copy the token.\n    ": "\n    Para obtener tu token personal, sigue estos pasos:\n\n    1. Inicie sesión en su cuenta Airtable.\n    2. Visita https://airtable.com/create/tokens/ para crear una\n    3. Haga clic en \"+ Añadir una base\" y seleccione la base que desea utilizar o todas las bases.\n    4. Haga clic en \"+ Añadir un ámbito de aplicación\" y seleccione \"datos. ecords.read\", \"data.records.write\" y \"schema.bases.read\".\n    5. Haga clic en \"Crear token\" y copie el token.\n    ", "Create Airtable Record": "Crear registro Airtable", "Find Airtable Record": "Encontrar registro Airtable", "Update Airtable Record": "Actualizar registro Airtable", "Delete Airtable Record": "Eliminar registro Airtable", "Upload File to Column": "Subir archivo a Column", "Custom API Call": "Llamada API personalizada", "Adds a record into an airtable": "Añade un registro a una tabla aérea", "Find a record in airtable": "Encontrar un registro en la tabla aérea", "Update a record in airtable": "Actualizar un registro en tabla aérea", "Deletes a record in airtable": "Elimina un registro de la tabla aérea", "Uploads a file to attachment type column.": "Sube un archivo a columna de tipo adjunto.", "Make a custom API call to a specific endpoint": "Hacer una llamada API personalizada a un extremo específico", "Base": "Base", "Table": "Tabla", "Search Field": "Campo de búsqueda", "Search Value": "Valor de búsqueda", "View": "<PERSON>er", "Record ID": "ID de registro", "Attachment Column": "Columna de adjunto", "File": "Archivo", "File Content Type": "Tipo de contenido de archivo", "File Name": "Nombre del archivo", "Method": "<PERSON><PERSON><PERSON><PERSON>", "Headers": "Encabezados", "Query Parameters": "Parámetros de consulta", "Body": "<PERSON><PERSON><PERSON>", "No Error on Failure": "No hay ningún error en fallo", "Timeout (in seconds)": "Tiempo de espera (en segundos)", "The ID of the record you want to update. You can find the record ID by clicking on the record and then clicking on the share button. The ID will be in the URL.": "El ID del registro que desea actualizar. Puede encontrar el ID del registro haciendo clic en el registro y luego haciendo clic en el botón compartir. El ID será en la URL.", "The ID of the record to which you want to upload the file.": "El ID del registro al que desea subir el archivo.", "The file to be uploaded, which can be provided either as a public file URL or in Base64 encoded format.": "El archivo a cargar, que puede ser proporcionado ya sea como una URL de archivo público o en formato codificado Base64.", "Specifies the MIME type of the file being uploaded (e.g., 'image/png', 'application/pdf').": "Especifica el tipo MIME del archivo que se está subiendo (por ejemplo, 'image/png', 'application/pdf').", "The name of the file as it should appear after upload.": "El nombre del archivo tal y como debería aparecer después de subir.", "Authorization headers are injected automatically from your connection.": "Las cabeceras de autorización se inyectan automáticamente desde tu conexión.", "GET": "RECOGER", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "BORRAR", "HEAD": "LIMPIO", "New Record": "Nuevo registro", "New or Updated Record": "Registro nuevo o actualizado", "Triggers when a new record is added to the selected table.": "Se activa cuando se añade un nuevo registro a la tabla seleccionada.", "Triggers when a record is created or updated in selected table.": "Se activa cuando se crea o actualiza un registro en la tabla seleccionada.", "Trigger field": "Campo de activación", "**Last Modified Time** field will be used to watch new or updated records.Please create **Last Modified Time** field in your schema,if you don't have any timestamp field.": "El campo **Última Hora Modificada** se utilizará para ver registros nuevos o actualizados. lease create **Last Modified Time** field in your schema,if you don't have any timestamp field."}
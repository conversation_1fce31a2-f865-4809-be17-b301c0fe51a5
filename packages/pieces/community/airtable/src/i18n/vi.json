{"Airtable": "Airtable", "Low‒code platform to build apps.": "Low‒code platform to build apps.", "\n    To obtain your personal token, follow these steps:\n\n    1. Log in to your Airtable account.\n    2. Visit https://airtable.com/create/tokens/ to create one\n    3. Click on \"+ Add a base\" and select the base you want to use or all bases.\n    4. Click on \"+ Add a scope\" and select \"data.records.read\", \"data.records.write\" and \"schema.bases.read\".\n    5. Click on \"Create token\" and copy the token.\n    ": "\n    To obtain your personal token, follow these steps:\n\n    1. Log in to your Airtable account.\n    2. V<PERSON>t https://airtable.com/create/tokens/ to create one\n    3. Click on \"+ Add a base\" and select the base you want to use or all bases.\n    4. Click on \"+ Add a scope\" and select \"data.records.read\", \"data.records.write\" and \"schema.bases.read\".\n    5. <PERSON>lick on \"Create token\" and copy the token.\n    ", "Create Airtable Record": "Create Airtable Record", "Find Airtable Record": "Find Airtable Record", "Update Airtable Record": "Update Airtable Record", "Delete Airtable Record": "Delete Airtable Record", "Upload File to Column": "Upload File to Column", "Custom API Call": "Custom API Call", "Adds a record into an airtable": "Adds a record into an airtable", "Find a record in airtable": "Find a record in airtable", "Update a record in airtable": "Update a record in airtable", "Deletes a record in airtable": "Deletes a record in airtable", "Uploads a file to attachment type column.": "Uploads a file to attachment type column.", "Make a custom API call to a specific endpoint": "Make a custom API call to a specific endpoint", "Base": "Base", "Table": "Table", "Search Field": "Search Field", "Search Value": "Search Value", "View": "View", "Record ID": "Record ID", "Attachment Column": "Attachment Column", "File": "File", "File Content Type": "File Content Type", "File Name": "File Name", "Method": "Method", "Headers": "Headers", "Query Parameters": "Query Parameters", "Body": "Body", "No Error on Failure": "No Error on Failure", "Timeout (in seconds)": "Timeout (in seconds)", "The ID of the record you want to update. You can find the record ID by clicking on the record and then clicking on the share button. The ID will be in the URL.": "The ID of the record you want to update. You can find the record ID by clicking on the record and then clicking on the share button. The ID will be in the URL.", "The ID of the record to which you want to upload the file.": "The ID of the record to which you want to upload the file.", "The file to be uploaded, which can be provided either as a public file URL or in Base64 encoded format.": "The file to be uploaded, which can be provided either as a public file URL or in Base64 encoded format.", "Specifies the MIME type of the file being uploaded (e.g., 'image/png', 'application/pdf').": "Specifies the MIME type of the file being uploaded (e.g., 'image/png', 'application/pdf').", "The name of the file as it should appear after upload.": "The name of the file as it should appear after upload.", "Authorization headers are injected automatically from your connection.": "Authorization headers are injected automatically from your connection.", "GET": "GET", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "DELETE", "HEAD": "HEAD", "New Record": "New Record", "New or Updated Record": "New or Updated Record", "Triggers when a new record is added to the selected table.": "Triggers when a new record is added to the selected table.", "Triggers when a record is created or updated in selected table.": "Triggers when a record is created or updated in selected table.", "Trigger field": "Trigger field", "**Last Modified Time** field will be used to watch new or updated records.Please create **Last Modified Time** field in your schema,if you don't have any timestamp field.": "**Last Modified Time** field will be used to watch new or updated records.Please create **Last Modified Time** field in your schema,if you don't have any timestamp field."}
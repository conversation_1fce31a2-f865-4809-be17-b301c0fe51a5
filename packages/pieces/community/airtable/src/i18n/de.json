{"Airtable": "Airtable", "Low‒code platform to build apps.": "<PERSON><PERSON><PERSON><unk> Code-Plattform zum E<PERSON><PERSON>n von <PERSON>.", "\n    To obtain your personal token, follow these steps:\n\n    1. Log in to your Airtable account.\n    2. Visit https://airtable.com/create/tokens/ to create one\n    3. Click on \"+ Add a base\" and select the base you want to use or all bases.\n    4. Click on \"+ Add a scope\" and select \"data.records.read\", \"data.records.write\" and \"schema.bases.read\".\n    5. Click on \"Create token\" and copy the token.\n    ": "\n    Um Ihr persönliches Zeichen zu erhalten, folgen Si<PERSON> diesen Schritten:\n\n    1. Melden Sie sich bei Ihrem Airtable Konto an.\n    2. Besuche https://airtable.com/create/tokens/ um einen\n    3 zu erstellen. Klicken Sie auf \"+ <PERSON><PERSON> hinzufügen\" und wählen Sie die Basis, die Sie verwenden möchten oder alle Basis.\n    4. Klicken Sie auf \"+ Geltungsbereich hinzufügen\" und wählen Sie \"Daten\". ecords.read\", \"data.records.write\" und \"schema.bases.read\".\n    5. Klicken Si<PERSON> auf \"Token erstellen\" und kopieren Sie den Token.\n    ", "Create Airtable Record": "Airtable Datensatz erstellen", "Find Airtable Record": "Airtable Datensatz finden", "Update Airtable Record": "Airtable Datensatz aktualisieren", "Delete Airtable Record": "Airtable Eintrag löschen", "Upload File to Column": "Datei in Spalte hochladen", "Custom API Call": "Eigener API-Aufruf", "Adds a record into an airtable": "<PERSON><PERSON><PERSON> einen Datensatz zu einer Airtable hinzu", "Find a record in airtable": "<PERSON><PERSON> einen Eintrag in Airtable", "Update a record in airtable": "Eintrag in Airtable aktualisieren", "Deletes a record in airtable": "Löscht einen Eintrag in der Airtable", "Uploads a file to attachment type column.": "Lade eine Datei in die Spalte des Anhangs hoch.", "Make a custom API call to a specific endpoint": "Einen benutzerdefinierten API-Aufruf an einen bestimmten Endpunkt machen", "Base": "<PERSON><PERSON>", "Table": "Tisch", "Search Field": "<PERSON><PERSON>", "Search Value": "Suchwert", "View": "<PERSON><PERSON><PERSON>", "Record ID": "Datensatz-ID", "Attachment Column": "Anhangspalte", "File": "<PERSON><PERSON>", "File Content Type": "Datei-Content-Typ", "File Name": "Dateiname", "Method": "<PERSON>e", "Headers": "Kopfzeilen", "Query Parameters": "Abfrageparameter", "Body": "<PERSON><PERSON><PERSON><PERSON>", "No Error on Failure": "<PERSON><PERSON> bei <PERSON>hler", "Timeout (in seconds)": "Timeout (in Sekunden)", "The ID of the record you want to update. You can find the record ID by clicking on the record and then clicking on the share button. The ID will be in the URL.": "Die ID des Datensatzes, den Sie aktualisieren möchten. Sie können die Datensatz-ID finden, indem Sie auf den Datensatz klicken und dann auf den Teilen-Knopf klicken. Die ID wird in der URL sein.", "The ID of the record to which you want to upload the file.": "Die ID des Datensatzes, in den Si<PERSON> die Datei hochladen möchten.", "The file to be uploaded, which can be provided either as a public file URL or in Base64 encoded format.": "Die hochzuladende Datei, die entweder als öffentliche Datei-URL oder im Base64-kodierten Format zur Verfügung gestellt werden kann.", "Specifies the MIME type of the file being uploaded (e.g., 'image/png', 'application/pdf').": "Gibt den MIME-Typ der hochzuladenden Datei an (z.B. 'image/png', 'application/pdf').", "The name of the file as it should appear after upload.": "Der Name der Datei, wie sie nach dem Ho<PERSON>laden erscheinen soll.", "Authorization headers are injected automatically from your connection.": "Autorisierungs-Header werden automatisch von Ihrer Verbindung injiziert.", "GET": "ERHALTEN", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "LÖSCHEN", "HEAD": "HEAD", "New Record": "<PERSON>euer <PERSON>atz", "New or Updated Record": "Neuer oder aktualisierter Datensatz", "Triggers when a new record is added to the selected table.": "Wird aus<PERSON><PERSON><PERSON>, wenn ein neuer Datensatz zur ausgewählten Tabelle hinzugefügt wird.", "Triggers when a record is created or updated in selected table.": "Wird aus<PERSON><PERSON><PERSON>, wenn ein Datensatz in der ausgewählten Tabelle erstellt oder aktualisiert wird.", "Trigger field": "Auslösefeld", "**Last Modified Time** field will be used to watch new or updated records.Please create **Last Modified Time** field in your schema,if you don't have any timestamp field.": "**Zuletzt geändertes Zeit** Feld wird verwendet, um neue oder aktualisierte Datensätze zu sehen. Leasing erstellen Sie das **Zuletzt geänderte Zeit** Feld in Ihrem Schema, wenn Sie kein Zeitstempelfeld haben."}
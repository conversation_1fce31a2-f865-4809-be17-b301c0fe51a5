{"Airtable": "Airtable", "Low‒code platform to build apps.": "低代碼平台建構應用程式。", "\n    To obtain your personal token, follow these steps:\n\n    1. Log in to your Airtable account.\n    2. Visit https://airtable.com/create/tokens/ to create one\n    3. Click on \"+ Add a base\" and select the base you want to use or all bases.\n    4. Click on \"+ Add a scope\" and select \"data.records.read\", \"data.records.write\" and \"schema.bases.read\".\n    5. Click on \"Create token\" and copy the token.\n    ": "\n    若要取得您的個人代碼，請遵循以下步驟：\n\n    1. 登入您的 Airtable 帳號。\n    2. 訪問 https://airtable.com/create/tokens/ 以創建\n    3. 點擊 \"+ Add a base\" 並選擇您要使用的基礎或所有基礎。\n    4. 點擊 \"+ Add a scope\" 並選擇 \"data.records.read\"、\"data.records.write\" 和 \"schema.bases.read\"。\n    5. 點擊 \"Create token\" 並複製代碼。\n    ", "Create Airtable Record": "創建 Airtable 記錄", "Find Airtable Record": "尋找 Airtable 記錄", "Update Airtable Record": "更新 Airtable 記錄", "Delete Airtable Record": "刪除 Airtable 記錄", "Upload File to Column": "上傳文件到列", "Custom API Call": "自訂 API 呼叫", "Adds a record into an airtable": "新增一條記錄到 Airtable", "Find a record in airtable": "在 Airtable 中尋找一條記錄", "Update a record in airtable": "在 Airtable 中更新一條記錄", "Deletes a record in airtable": "在 Airtable 中刪除一條記錄", "Uploads a file to attachment type column.": "將文件上傳至附件類型的列。", "Make a custom API call to a specific endpoint": "自訂 API 呼叫到特定端點", "Base": "基礎", "Table": "表格", "Search Field": "搜尋欄位", "Search Value": "搜尋值", "View": "檢視", "Record ID": "記錄 ID", "Attachment Column": "附件列", "File": "文件", "File Content Type": "文件內容類型", "File Name": "檔案名稱", "Method": "方法", "Headers": "標頭", "Query Parameters": "查詢參數", "Body": "主體", "No Error on Failure": "失敗時無錯誤", "Timeout (in seconds)": "超時 （秒）", "The ID of the record you want to update. You can find the record ID by clicking on the record and then clicking on the share button. The ID will be in the URL.": "您想要更新的記錄 ID。您可以透過點擊該記錄，然後點擊分享按鈕來找到記錄 ID。ID 會在 URL 中顯示。", "The ID of the record to which you want to upload the file.": "您想要上傳文件的記錄 ID。", "The file to be uploaded, which can be provided either as a public file URL or in Base64 encoded format.": "要上傳的文件，可以提供為公開文件網址或 Base64 編碼格式。", "Specifies the MIME type of the file being uploaded (e.g., 'image/png', 'application/pdf').": "指定上傳文件的 MIME 類型（例如，'image/png'、'application/pdf'）。", "The name of the file as it should appear after upload.": "上傳後文件的顯示名稱。", "Authorization headers are injected automatically from your connection.": "授權標頭會自動從您的連線中插入。", "GET": "GET", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "DELETE", "HEAD": "HEAD", "New Record": "新記錄", "New or Updated Record": "新或更新的記錄", "Triggers when a new record is added to the selected table.": "當選定的表格中新增一條記錄時觸發。", "Triggers when a record is created or updated in selected table.": "當選定的表格中創建或更新記錄時觸發。", "Trigger field": "觸發欄位", "**Last Modified Time** field will be used to watch new or updated records.Please create **Last Modified Time** field in your schema,if you don't have any timestamp field.": "**最後修改時間** 欄位將用於監控新或更新的記錄。如果您沒有任何時間戳欄位，請在架構中創建 **最後修改時間** 欄位。"}
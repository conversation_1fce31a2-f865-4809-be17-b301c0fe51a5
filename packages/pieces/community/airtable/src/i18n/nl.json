{"Airtable": "Airtable", "Low‒code platform to build apps.": "LowØ code platform om apps te bouwen.", "\n    To obtain your personal token, follow these steps:\n\n    1. Log in to your Airtable account.\n    2. Visit https://airtable.com/create/tokens/ to create one\n    3. Click on \"+ Add a base\" and select the base you want to use or all bases.\n    4. Click on \"+ Add a scope\" and select \"data.records.read\", \"data.records.write\" and \"schema.bases.read\".\n    5. Click on \"Create token\" and copy the token.\n    ": "\n    Om je persoonlijke token te krijgen, volg je deze stappen:\n\n    1. Log in op uw Airtable account.\n    2. Bezoek https://airtable.com/create/tokens/ om een\n    3 aan te maken. Klik op \"+ Voeg een basis toe\" en selecteer de basis die je wilt gebruiken of alle basissen.\n    4. Klik op \"+ Een scope toevoegen\" en selecteer \"gegevens\" ecords.read\", \"data.records.write\" en \"schema.bases.read\".\n    5. Klik op \"Maak token\" en kopieer het token.\n    ", "Create Airtable Record": "Airtable Record Maken", "Find Airtable Record": "Vind Airtable Record", "Update Airtable Record": "Airtable Record bijwerken", "Delete Airtable Record": "Airtable Record verwijderen", "Upload File to Column": "Bestand naar kolom uploaden", "Custom API Call": "Custom API Call", "Adds a record into an airtable": "Voegt een record toe in een airtable", "Find a record in airtable": "Vind een record in airtable", "Update a record in airtable": "Een record in airtable bijwerken", "Deletes a record in airtable": "Verwijdert een record in de luchtbel", "Uploads a file to attachment type column.": "Uploaden van een bestand naar kolom bijlagentype.", "Make a custom API call to a specific endpoint": "Maak een aangepaste API call naar een specifiek eindpunt", "Base": "<PERSON><PERSON>", "Table": "<PERSON><PERSON>", "Search Field": "<PERSON><PERSON> veld", "Search Value": "<PERSON><PERSON><PERSON> z<PERSON>ken", "View": "Weergave", "Record ID": "Record ID", "Attachment Column": "<PERSON><PERSON><PERSON><PERSON> kolom", "File": "Bestand", "File Content Type": "Bestand Content Type", "File Name": "File Name", "Method": "<PERSON>e", "Headers": "Kopteksten", "Query Parameters": "Query parameters", "Body": "<PERSON><PERSON><PERSON>", "No Error on Failure": "<PERSON><PERSON> fout bij fout", "Timeout (in seconds)": "Time-out (in seconden)", "The ID of the record you want to update. You can find the record ID by clicking on the record and then clicking on the share button. The ID will be in the URL.": "De ID van het record dat u wilt bijwerken. U kunt het opname-ID vinden door te klikken op het record en vervolgens te klikken op de deel-knop. Het ID staat in de URL.", "The ID of the record to which you want to upload the file.": "Het <PERSON> van het record waarnaar u het bestand wilt uploaden.", "The file to be uploaded, which can be provided either as a public file URL or in Base64 encoded format.": "Het te uploaden bestand, dat kan worden verstrekt als een URL van een publiek bestand of in Base64 gecodeerde formaat.", "Specifies the MIME type of the file being uploaded (e.g., 'image/png', 'application/pdf').": "Geeft het MIME-type op van het bestand dat wordt geüpload (bijv. 'image/png', 'application/pdf').", "The name of the file as it should appear after upload.": "De naam van het bestand zoals het moet worden weergegeven na uploaden.", "Authorization headers are injected automatically from your connection.": "Autorisatie headers worden automatisch geïnjecteerd vanuit uw verbinding.", "GET": "KRIJG", "POST": "POSTE", "PATCH": "BEKIJK", "PUT": "PUT", "DELETE": "VERWIJDEREN", "HEAD": "HOOFD", "New Record": "Nieuwe Record", "New or Updated Record": "<PERSON><PERSON><PERSON> of bijgewerkte record", "Triggers when a new record is added to the selected table.": "<PERSON><PERSON> wanneer een nieuwe record wordt toegevoegd aan de geselecteerde tabel.", "Triggers when a record is created or updated in selected table.": "<PERSON>ggert wanneer een record wordt aangemaakt of bijgewerkt in de geselecteerde tabel.", "Trigger field": "Trigger veld", "**Last Modified Time** field will be used to watch new or updated records.Please create **Last Modified Time** field in your schema,if you don't have any timestamp field.": "**Laatst gewijzigde tijd** veld zal worden gebruikt om nieuwe of bijgewerkte records te bekijken. lease creëer **Last Modified Time** veld in je schema, als je geen timestamp veld hebt."}
{"Airtable": "Airtable", "Low‒code platform to build apps.": "アプリを構築するためのプラットフォームをローコードします。", "\n    To obtain your personal token, follow these steps:\n\n    1. Log in to your Airtable account.\n    2. Visit https://airtable.com/create/tokens/ to create one\n    3. Click on \"+ Add a base\" and select the base you want to use or all bases.\n    4. Click on \"+ Add a scope\" and select \"data.records.read\", \"data.records.write\" and \"schema.bases.read\".\n    5. Click on \"Create token\" and copy the token.\n    ": "\n    To obtain your personal token, follow these steps:\n\n    1. Log in to your Airtable account.\n    2. V<PERSON>t https://airtable.com/create/tokens/ to create one\n    3. Click on \"+ Add a base\" and select the base you want to use or all bases.\n    4. Click on \"+ Add a scope\" and select \"data.records.read\", \"data.records.write\" and \"schema.bases.read\".\n    5. <PERSON>lick on \"Create token\" and copy the token.\n    ", "Create Airtable Record": "対応可能な記録を作成", "Find Airtable Record": "対空式記録を検索", "Update Airtable Record": "対空式記録を更新", "Delete Airtable Record": "空気の記録を削除", "Upload File to Column": "列にファイルをアップロード", "Custom API Call": "カスタムAPI通話", "Adds a record into an airtable": "エアーテーブルにレコードを追加", "Find a record in airtable": "エアテーブルでレコードを見つける", "Update a record in airtable": "エアテーブルのレコードを更新", "Deletes a record in airtable": "エアテーブル内のレコードを削除", "Uploads a file to attachment type column.": "添付ファイルの種類の列にファイルをアップロードします。", "Make a custom API call to a specific endpoint": "特定のエンドポイントへのカスタム API コールを実行します。", "Base": "Base", "Table": "表", "Search Field": "検索フィールド", "Search Value": "検索値", "View": "表示", "Record ID": "レコードID", "Attachment Column": "添付ファイルの列", "File": "ファイル", "File Content Type": "ファイルコンテンツタイプ", "File Name": "ファイル名", "Method": "方法", "Headers": "ヘッダー", "Query Parameters": "クエリパラメータ", "Body": "本文", "No Error on Failure": "失敗時にエラーはありません", "Timeout (in seconds)": "タイムアウト（秒）", "The ID of the record you want to update. You can find the record ID by clicking on the record and then clicking on the share button. The ID will be in the URL.": "更新するレコードの ID です。 レコードIDは、レコードをクリックして共有ボタンをクリックすることで見つけることができます。IDはURL内にあります。", "The ID of the record to which you want to upload the file.": "ファイルをアップロードするレコードの ID 。", "The file to be uploaded, which can be provided either as a public file URL or in Base64 encoded format.": "アップロードするファイルは、公開ファイルの URL または Base64 エンコード形式のいずれかで指定できます。", "Specifies the MIME type of the file being uploaded (e.g., 'image/png', 'application/pdf').": "アップロードするファイルの MIME タイプを指定します(例: 'image/png', 'application/pdf')。", "The name of the file as it should appear after upload.": "アップロード後にファイル名が表示されます。", "Authorization headers are injected automatically from your connection.": "認証ヘッダは接続から自動的に注入されます。", "GET": "取得", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "削除", "HEAD": "頭", "New Record": "新しいレコード", "New or Updated Record": "新規または更新されたレコード", "Triggers when a new record is added to the selected table.": "選択したテーブルに新しいレコードが追加されたときにトリガーされます。", "Triggers when a record is created or updated in selected table.": "選択したテーブルでレコードを作成または更新したときにトリガーします。", "Trigger field": "トリガーフィールド", "**Last Modified Time** field will be used to watch new or updated records.Please create **Last Modified Time** field in your schema,if you don't have any timestamp field.": "**最終更新時刻** フィールドは、新しいレコードや更新を見るために使用されます。 リース作成**最終更新時刻** あなたのスキーマにタイムスタンプフィールドがなければ、あなたのスキーマにフィールドを作成します。"}
{"Airtable": "Airtable", "Low‒code platform to build apps.": "Baixa plataforma de código para construir aplicativos.", "\n    To obtain your personal token, follow these steps:\n\n    1. Log in to your Airtable account.\n    2. Visit https://airtable.com/create/tokens/ to create one\n    3. Click on \"+ Add a base\" and select the base you want to use or all bases.\n    4. Click on \"+ Add a scope\" and select \"data.records.read\", \"data.records.write\" and \"schema.bases.read\".\n    5. Click on \"Create token\" and copy the token.\n    ": "\n    To obtain your personal token, follow these steps:\n\n    1. Log in to your Airtable account.\n    2. V<PERSON>t https://airtable.com/create/tokens/ to create one\n    3. Click on \"+ Add a base\" and select the base you want to use or all bases.\n    4. Click on \"+ Add a scope\" and select \"data.records.read\", \"data.records.write\" and \"schema.bases.read\".\n    5. <PERSON>lick on \"Create token\" and copy the token.\n    ", "Create Airtable Record": "Criar Registro Airtable", "Find Airtable Record": "Encontrar Registro Airtable", "Update Airtable Record": "Atualizar Registro Airtable", "Delete Airtable Record": "Excluir registro da Airtable", "Upload File to Column": "Carregar Arquivo para Coluna", "Custom API Call": "Chamada de API personalizada", "Adds a record into an airtable": "Adiciona um registro a uma tabela aérea", "Find a record in airtable": "Encontrar um registro na tabela aérea", "Update a record in airtable": "Atualizar um registro no airtable", "Deletes a record in airtable": "Exclui um registro na tabela aérea", "Uploads a file to attachment type column.": "Carrega um arquivo para anexar coluna de tipos.", "Make a custom API call to a specific endpoint": "Faça uma chamada de API personalizada para um ponto de extremidade específico", "Base": "Base", "Table": "Classificações", "Search Field": "Campo de pesquisa", "Search Value": "<PERSON><PERSON><PERSON><PERSON>", "View": "Visualizar", "Record ID": "ID do Registro", "Attachment Column": "Coluna de anexos", "File": "Arquivo", "File Content Type": "Tipo de Conteúdo", "File Name": "Nome do arquivo", "Method": "<PERSON><PERSON><PERSON><PERSON>", "Headers": "Cabeçalhos", "Query Parameters": "Parâ<PERSON><PERSON> da consult<PERSON>", "Body": "<PERSON><PERSON><PERSON><PERSON>", "No Error on Failure": "Nenhum erro no Failure", "Timeout (in seconds)": "Tempo limite (em segundos)", "The ID of the record you want to update. You can find the record ID by clicking on the record and then clicking on the share button. The ID will be in the URL.": "A ID do registro que você deseja atualizar. Você pode encontrar o ID do registro clicando no registro e clicando no botão de compartilhamento. A ID estará no URL.", "The ID of the record to which you want to upload the file.": "O ID do registro para o qual você deseja fazer upload do arquivo.", "The file to be uploaded, which can be provided either as a public file URL or in Base64 encoded format.": "O arquivo a ser carregado, que pode ser fornecido como uma URL de arquivo público ou no formato codificado em Base64.", "Specifies the MIME type of the file being uploaded (e.g., 'image/png', 'application/pdf').": "Especifica o tipo MIME do arquivo que está sendo enviado (por exemplo, 'image/png', 'application/pdf').", "The name of the file as it should appear after upload.": "O nome do arquivo como ele deve aparecer após o upload.", "Authorization headers are injected automatically from your connection.": "Os cabeçalhos de autorização são inseridos automaticamente a partir da sua conexão.", "GET": "OBTER", "POST": "POSTAR", "PATCH": "COMPRAR", "PUT": "COLOCAR", "DELETE": "EXCLUIR", "HEAD": "CABEÇA", "New Record": "Novo Registro", "New or Updated Record": "Registro novo ou atualizado", "Triggers when a new record is added to the selected table.": "Dispara quando um novo registro é adicionado à tabela selecionada.", "Triggers when a record is created or updated in selected table.": "Dispara quando um registro é criado ou atualizado na tabela selecionada.", "Trigger field": "Campo gatilho", "**Last Modified Time** field will be used to watch new or updated records.Please create **Last Modified Time** field in your schema,if you don't have any timestamp field.": "O campo **Última Modificação** será usado para assistir registros novos ou atualizados. crie campo **Hora Última Modificação** em seu esquema, se você não tiver nenhum campo de carimbo de data."}
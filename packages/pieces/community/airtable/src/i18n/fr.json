{"Airtable": "Airtable", "Low‒code platform to build apps.": "Plateforme de code faible pour construire des applications.", "\n    To obtain your personal token, follow these steps:\n\n    1. Log in to your Airtable account.\n    2. Visit https://airtable.com/create/tokens/ to create one\n    3. Click on \"+ Add a base\" and select the base you want to use or all bases.\n    4. Click on \"+ Add a scope\" and select \"data.records.read\", \"data.records.write\" and \"schema.bases.read\".\n    5. Click on \"Create token\" and copy the token.\n    ": "\n    To obtain your personal token, follow these steps:\n\n    1. Log in to your Airtable account.\n    2. V<PERSON>t https://airtable.com/create/tokens/ to create one\n    3. Click on \"+ Add a base\" and select the base you want to use or all bases.\n    4. Click on \"+ Add a scope\" and select \"data.records.read\", \"data.records.write\" and \"schema.bases.read\".\n    5. <PERSON>lick on \"Create token\" and copy the token.\n    ", "Create Airtable Record": "Créer un enregistrement Airtable", "Find Airtable Record": "Trouver un enregistrement Airtable", "Update Airtable Record": "Mise à jour de l'enregistrement Airtable", "Delete Airtable Record": "Supprimer l'enregistrement Airtable", "Upload File to Column": "Télécharger le fichier vers la colonne", "Custom API Call": "Appel API personnalisé", "Adds a record into an airtable": "Ajoute un enregistrement dans une table d'air", "Find a record in airtable": "Trouver un enregistrement dans la table d'antenne", "Update a record in airtable": "Mettre à jour un enregistrement dans la table d'antenne", "Deletes a record in airtable": "Supprime un enregistrement dans la table d'antenne", "Uploads a file to attachment type column.": "Télécharge un fichier dans la colonne de type de pièce jointe.", "Make a custom API call to a specific endpoint": "Passez un appel API personnalisé à un point de terminaison spécifique", "Base": "Base", "Table": "<PERSON><PERSON>", "Search Field": "Champ de recherche", "Search Value": "Valeur de la recherche", "View": "<PERSON><PERSON><PERSON><PERSON>", "Record ID": "ID de l'enregistrement", "Attachment Column": "Colonne de pièce jointe", "File": "<PERSON><PERSON>", "File Content Type": "Type de contenu du fichier", "File Name": "Nom du fichier", "Method": "Méthode", "Headers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Query Parameters": "Paramètres de requête", "Body": "Corps", "No Error on Failure": "Aucune erreur en cas d'échec", "Timeout (in seconds)": "<PERSON><PERSON><PERSON>atten<PERSON> (en secondes)", "The ID of the record you want to update. You can find the record ID by clicking on the record and then clicking on the share button. The ID will be in the URL.": "L'ID de l'enregistrement que vous voulez mettre à jour. Vous pouvez trouver l'ID de l'enregistrement en cliquant sur l'enregistrement puis en cliquant sur le bouton de partage. L'ID sera dans l'URL.", "The ID of the record to which you want to upload the file.": "L'ID de l'enregistrement vers lequel vous voulez télécharger le fichier.", "The file to be uploaded, which can be provided either as a public file URL or in Base64 encoded format.": "Le fichier à télécharger, qui peut être fourni sous forme d'URL de fichier publique ou au format encodé en Base64.", "Specifies the MIME type of the file being uploaded (e.g., 'image/png', 'application/pdf').": "Spécifie le type MIME du fichier en cours d'envoi (par exemple, 'image/png', 'application/pdf').", "The name of the file as it should appear after upload.": "Le nom du fichier tel qu'il devrait apparaître après le téléchargement.", "Authorization headers are injected automatically from your connection.": "Les en-têtes d'autorisation sont injectés automatiquement à partir de votre connexion.", "GET": "OBTENIR", "POST": "POSTER", "PATCH": "PATCH", "PUT": "EFFACER", "DELETE": "SUPPRIMER", "HEAD": "TÊTE", "New Record": "Nouvel enregistrement", "New or Updated Record": "Nouvel enregistrement ou mis à jour", "Triggers when a new record is added to the selected table.": "Déclenche lorsqu'un nouvel enregistrement est ajouté à la table sélectionnée.", "Triggers when a record is created or updated in selected table.": "Déclenche lorsqu'un enregistrement est créé ou mis à jour dans la table sélectionnée.", "Trigger field": "Champ de déclenchement", "**Last Modified Time** field will be used to watch new or updated records.Please create **Last Modified Time** field in your schema,if you don't have any timestamp field.": "Le champ **Dernière Modification** sera utilisé pour regarder les enregistrements nouveaux ou mis à jour. créer un champ **Dernière modification** dans votre schéma, si vous n'avez pas de champ d'horodatage."}
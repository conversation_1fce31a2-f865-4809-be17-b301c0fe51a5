{"Airtable": "Airtable", "Low‒code platform to build apps.": "Низкая платформа для создания приложений.", "\n    To obtain your personal token, follow these steps:\n\n    1. Log in to your Airtable account.\n    2. Visit https://airtable.com/create/tokens/ to create one\n    3. Click on \"+ Add a base\" and select the base you want to use or all bases.\n    4. Click on \"+ Add a scope\" and select \"data.records.read\", \"data.records.write\" and \"schema.bases.read\".\n    5. Click on \"Create token\" and copy the token.\n    ": "\n    Для получения личного токена выполните следующие действия:\n\n    1. Войдите в свой аккаунт Airtable.\n    2. Посетите https://airtable.com/create/tokens/ для создания одной\n    3. Нажмите на \"+ Добавить базу\" и выберите базу, которую вы хотите использовать или все базы.\n    4. Нажмите \"+ Добавить область\" и выберите \"данные. ecords.read\", \"data.records.write\" и \"schema.bases.read\".\n    5. Нажмите на \"Create token\" и скопируйте токен.\n    ", "Create Airtable Record": "Создать запись Airtable", "Find Airtable Record": "Найти запись Airtable", "Update Airtable Record": "Обновить запись Airtable", "Delete Airtable Record": "Удалить запись Airtable", "Upload File to Column": "Загрузить файл в столбец", "Custom API Call": "Пользовательский вызов API", "Adds a record into an airtable": "Добавляет запись в авиатаблицу", "Find a record in airtable": "Найти запись в авиатаблице", "Update a record in airtable": "Изменить запись в авиатаблице", "Deletes a record in airtable": "Удаляет запись в авиационной таблице", "Uploads a file to attachment type column.": "Загружает файл для типа вложения столбца.", "Make a custom API call to a specific endpoint": "Сделать пользовательский API вызов к определенной конечной точке", "Base": "Основание", "Table": "Таблица", "Search Field": "Поле поиска", "Search Value": "Поисковое значение", "View": "View", "Record ID": "ID записи", "Attachment Column": "Столбец вложения", "File": "<PERSON>а<PERSON><PERSON>", "File Content Type": "Тип содержимого файла", "File Name": "Имя файла", "Method": "Метод", "Headers": "Заголовки", "Query Parameters": "Параметры запроса", "Body": "Тело", "No Error on Failure": "Нет ошибок при ошибке", "Timeout (in seconds)": "Таймаут (в секундах)", "The ID of the record you want to update. You can find the record ID by clicking on the record and then clicking on the share button. The ID will be in the URL.": "ID записи, котор<PERSON>ю вы хотите обновить. Вы можете найти идентификатор записи, нажав на запись, а затем нажав на кнопку \"Поделиться\".", "The ID of the record to which you want to upload the file.": "ID записи, в которую вы хотите загрузить файл.", "The file to be uploaded, which can be provided either as a public file URL or in Base64 encoded format.": "Загружаемый файл, который может быть предоставлен как публичный URL файла, так и в кодировке Base64.", "Specifies the MIME type of the file being uploaded (e.g., 'image/png', 'application/pdf').": "Тип MIME загружаемого файла (например, 'image/png', 'application/pdf').", "The name of the file as it should appear after upload.": "Имя файла, которое должно появиться после загрузки.", "Authorization headers are injected automatically from your connection.": "Заголовки авторизации включаются автоматически из вашего соединения.", "GET": "ПОЛУЧИТЬ", "POST": "ПОСТ", "PATCH": "ПАТЧ", "PUT": "ПОКУПИТЬ", "DELETE": "УДАЛИТЬ", "HEAD": "HEAD", "New Record": "Новая запись", "New or Updated Record": "Новая или Обновленная запись", "Triggers when a new record is added to the selected table.": "Включает при добавлении новой записи в выбранную таблицу.", "Triggers when a record is created or updated in selected table.": "Триггеры при создании или обновлении записи в выбранной таблице.", "Trigger field": "Триггер поле", "**Last Modified Time** field will be used to watch new or updated records.Please create **Last Modified Time** field in your schema,if you don't have any timestamp field.": "Поле **Время последнего изменения** будет использоваться для просмотра новых или обновленных записей. в аренде создайте поле **Последнее Изменённое время** в вашей схеме, если у вас нет поля отметки времени."}
{"AssemblyAI": "AssemblyAI", "Transcribe and extract data from audio using AssemblyAI's Speech AI.": "Transcribe y extrae los datos del audio utilizando el Speech AI de AssemblyAI.", "You can retrieve your AssemblyAI API key within your AssemblyAI [Account Settings](https://www.assemblyai.com/app/account?utm_source=activepieces).": "Puedes recuperar tu clave de API de AssemblyAI en tu Colmena [Configuración de la cuenta](https://www.assemblyai.com/app/account?utm_source=activepieces).", "Upload File": "Subir archivo", "Transcribe": "Transcribir", "Get Transcript": "Get Transcript", "Get Transcript Sentences": "Obtener frases de transcripción", "Get Transcript Paragraphs": "Obtener párrafos de transcripción", "Get Transcript Subtitles": "Obtener Subtítulos Transcript", "Get Transcript Redacted Audio": "Obtener Transcripción de Audio Redactado", "Search words in transcript": "Buscar palabras en transcripción", "List transcripts": "Lista de transcripciones", "Delete transcript": "Eliminar transcripción", "Run a Task using LeMUR": "Ejecutar una tarea usando LeMUR", "Retrieve LeMUR response": "Recuperar respuesta de LeMUR", "Purge LeMUR request data": "Purgar datos de solicitud de LeMUR", "Custom API Call": "Llamada API personalizada", "Upload a media file to AssemblyAI's servers.": "Subir un archivo multimedia a los servidores de AssemblblyAI.", "Transcribe an audio or video file using AssemblyAI.": "Transcribir un archivo de audio o vídeo usando AssemblblyAI.", "Retrieves a transcript by its ID.": "Recuperar una transcripción por su ID.", "Retrieve the sentences of the transcript by its ID.": "Recuperar las frases de la transcripción por su ID.", "Retrieve the paragraphs of the transcript by its ID.": "Recuperar los párrafos de la transcripción por su ID.", "Export the transcript as SRT or VTT subtitles.": "Exportar la transcripción como subtítulos SRT o VTT.", "Get the result of the redacted audio model.": "Obtener el resultado del modelo de audio redactado.", "Search through the transcript for keywords. You can search for individual words, numbers, or phrases containing up to five words or numbers.": "Busque palabras clave a través de la transcripción. Puede buscar palabras individuales, números o frases que contengan hasta cinco palabras o números.", "Retrieve a list of transcripts you created.\nTranscripts are sorted from newest to oldest. The previous URL always points to a page with older transcripts.": "Recupera una lista de transcripciones creadas.\nLas transcripciones se ordenan de más reciente a más antigua. La URL anterior siempre apunta a una página con transcripciones antiguas.", "Remove the data from the transcript and mark it as deleted.": "Eliminar los datos de la transcripción y marcarlos como eliminados.", "Use the LeMUR task endpoint to input your own LLM prompt.": "Utilice el punto final de la tarea LeMUR para introducir su propia petición LLM.", "Retrieve a LeMUR response that was previously generated.": "Recuperar una respuesta de LeMUR que se generó previamente.", "Delete the data for a previously submitted LeMUR request.\nThe LLM response data, as well as any context provided in the original request will be removed.": "Delete the data for a previously submitted LeMUR request.\nThe LLM response data, as well as any context provided in the original request will be removed.", "Make a custom API call to a specific endpoint": "Hacer una llamada API personalizada a un extremo específico", "Audio File": "Archivo de audio", "Audio URL": "URL de audio", "Language Code": "Código de idioma", "Language Detection": "Detección de idioma", "Language Confidence Threshold": "Umbral de confianza del idioma", "Speech Model": "<PERSON><PERSON> de voz", "Punctuate": "Punctuate", "Format Text": "Formatear texto", "Disfluencies": "Disfluencias", "Dual Channel": "Doble canal", "Webhook URL": "URL de Webhook", "Webhook Auth Header Name": "Nombre Auth Head<PERSON>ho<PERSON>", "Webhook Auth Header Value": "<PERSON><PERSON>", "Key Phrases": "Frases clave", "Audio Start From": "Inicio de audio desde", "Audio End At": "Fin de audio en", "Word Boost": "Optimización de palabras", "Word Boost Level": "<PERSON>vel de impulso de palabras", "Filter Profanity": "Filtrar apariencia", "Redact PII": "Redact PII", "Redact PII Audio": "Redact PII Audio", "Redact PII Audio Quality": "Calidad de audio Redact PII", "Redact PII Policies": "Redact PII Policies", "Redact PII Substitution": "Sustitución Redactar PII", "Speaker Labels": "Etiquetas de altavoz", "Speakers Expected": "Altavoces esperados", "Content Moderation": "Moderación de contenido", "Content Moderation Confidence": "Confianza de moderación de contenido", "Topic Detection": "Detección del tema", "Custom Spellings": "Espellados personalizados", "Sentiment Analysis": "<PERSON><PERSON><PERSON><PERSON> sensato", "Auto Chapters": "Capítulos automá<PERSON>", "Entity Detection": "Detección de entidades", "Speech Threshold": "Umbral de voz", "Enable Summarization": "Activar resumen", "Summary Model": "<PERSON><PERSON>", "Summary Type": "<PERSON><PERSON><PERSON>", "Enable Custom Topics": "Habilitar temas personalizados", "Custom Topics": "<PERSON><PERSON>", "Wait until transcript is ready": "Espere hasta que la transcripción esté lista", "Throw if transcript status is error": "Lanzar si el estado de la transcripción es un error", "Transcript ID": "ID transcripción", "Subtitles Format": "Formato de subtítulos", "Number of Characters per Caption": "Número de caracteres por título", "Download file?": "¿Descargar archivo?", "Download File Name": "Descargar nombre de archivo", "Words": "Palabras", "Limit": "Límite", "Status": "Estado", "Created On": "Creado el", "Before ID": "Antes de ID", "After ID": "Después de ID", "Throttled Only": "Solo lanzados", "Prompt": "Petición", "Transcript IDs": "IDs de transcripción", "Input Text": "Input Text", "Context": "Contexto", "Final Model": "Modelo final", "Maximum Output Size": "<PERSON><PERSON><PERSON> máximo de <PERSON>", "Temperature": "Temperatura", "LeMUR request ID": "ID de solicitud de LeMUR", "Method": "<PERSON><PERSON><PERSON><PERSON>", "Headers": "Encabezados", "Query Parameters": "Parámetros de consulta", "Body": "<PERSON><PERSON><PERSON>", "No Error on Failure": "No hay ningún error en fallo", "Timeout (in seconds)": "Tiempo de espera (en segundos)", "The File or URL of the audio or video file.": "El archivo o URL del archivo de audio o vídeo.", "The URL of the audio or video file to transcribe.": "La URL del archivo de audio o vídeo a transcribir.", "The language of your audio file. Possible values are found in [Supported Languages](https://www.assemblyai.com/docs/concepts/supported-languages).\nThe default value is 'en_us'.\n": "El idioma de tu archivo de audio. Los valores posibles se encuentran en [Idiomas compatibles](https://www.assemblyai.com/docs/concepts/supported-languages).\nEl valor predeterminado es 'en_us'.\n", "Enable [Automatic language detection](https://www.assemblyai.com/docs/models/speech-recognition#automatic-language-detection), either true or false.": "Habilitar [Detección automática de idioma](https://www.assemblyai.com/docs/models/speech-recognition#automatic-language-detection), tanto verdadero como falso.", "The confidence threshold for the automatically detected language.\nAn error will be returned if the language confidence is below this threshold.\nDefaults to 0.\n": "The confidence threshold for the automatically detected language.\nAn error will be returned if the language confidence is below this threshold.\nDefaults to 0.\n", "The speech model to use for the transcription. When `null`, the \"best\" model is used.": "El modelo de voz a usar para la transcripción. Cuando `null`, se utiliza el modelo \"mejor\".", "Enable Automatic Punctuation, can be true or false": "Habilitar Puntuación Automática, puede ser verdadero o falso", "Enable Text Formatting, can be true or false": "Habilitar formato de texto, puede ser verdadero o falso", "Transcribe Filler Words, like \"umm\", in your media file; can be true or false": "Transcribe palabras completas, como \"umm\", en tu archivo multimedia; puede ser verdadero o falso", "Enable [Dual Channel](https://www.assemblyai.com/docs/models/speech-recognition#dual-channel-transcription) transcription, can be true or false.": "Habilitar transcripción [Doble Canal](https://www.assemblyai.com/docs/models/speech-recognition#dual-channel-transcription) puede ser verdadera o falsa.", "The URL to which we send webhook requests.\nWe sends two different types of webhook requests.\nOne request when a transcript is completed or failed, and one request when the redacted audio is ready if redact_pii_audio is enabled.\n": "The URL to which we send webhook requests.\nWe sends two different types of webhook requests.\nOne request when a transcript is completed or failed, and one request when the redacted audio is ready if redact_pii_audio is enabled.\n", "The header name to be sent with the transcript completed or failed webhook requests": "El nombre de la cabecera a ser enviado con la transcripción completada o fallida de solicitudes de webhook", "The header value to send back with the transcript completed or failed webhook requests for added security": "El valor de la cabecera a enviar con la transcripción completada o fallida de peticiones de seguridad añadida", "Enable Key Phrases, either true or false": "Habilitar frases de clave, sean verdaderas o falsas", "The point in time, in milliseconds, to begin transcribing in your media file": "El punto en el tiempo, en milisegundos, para comenzar a transcribir en su archivo multimedia", "The point in time, in milliseconds, to stop transcribing in your media file": "El punto en el tiempo, en milisegundos, para dejar de transcribir en tu archivo multimedia", "The list of custom vocabulary to boost transcription probability for": "La lista de vocabulario personalizado para aumentar la probabilidad de transcripción de", "How much to boost specified words": "Cuánto optimizar las palabras especificadas", "Filter profanity from the transcribed text, can be true or false": "Filtrar la profanidad del texto transcrito, puede ser verdadero o falso", "Redact PII from the transcribed text using the Redact PII model, can be true or false": "Redactar PII desde el texto transcrito usando el modelo Redact PII, puede ser verdadero o falso", "Generate a copy of the original media file with spoken PII \"beeped\" out, can be true or false. See [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) for more details.": "Generar una copia del archivo multimedia original con PII hablado \"pitido\", puede ser verdadero o falso. Ver [PII redacción] (https://www.assemblyai.com/docs/models/pii-redaction) para más detalles.", "Controls the filetype of the audio created by redact_pii_audio. Currently supports mp3 (default) and wav. See [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) for more details.": "Controla el tipo de archivo del audio creado por redact_pii_audio. Actualmente soporta mp3 (por defecto) y onda. Vea [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) para más detalles.", "The list of PII Redaction policies to enable. See [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) for more details.": "La lista de políticas de Redacción PII a habilitar. Ver [PII redacción](https://www.assemblyai.com/docs/models/pii-redaction) para más detalles.", "The replacement logic for detected PII, can be \"entity_type\" or \"hash\". See [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) for more details.": "La lógica de reemplazo para PII detectado, puede ser \"entity_type\" o \"hash\". Vea [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) para más detalles.", "Enable [Speaker diarization](https://www.assemblyai.com/docs/models/speaker-diarization), can be true or false": "Habilitar [Diarización del altavoz](https://www.assemblyai.com/docs/models/speaker-diarization), puede ser verdadero o falso", "Tells the speaker label model how many speakers it should attempt to identify, up to 10. See [Speaker diarization](https://www.assemblyai.com/docs/models/speaker-diarization) for more details.": "Indica al modelo de etiqueta del altavoz cuántos altavoces debe intentar identificar, hasta 10. Vea [Diarización del altavoz](https://www.assemblyai.com/docs/models/speaker-diarization) para más detalles.", "Enable [Content Moderation](https://www.assemblyai.com/docs/models/content-moderation), can be true or false": "Habilitar [Moderación de contenido](https://www.assemblyai.com/docs/models/content-moderation), puede ser verdadero o falso", "The confidence threshold for the Content Moderation model. Values must be between 25 and 100.": "El umbral de confianza para el modelo de Moderación de Contenidos. Los valores deben estar entre 25 y 100.", "Enable [Topic Detection](https://www.assemblyai.com/docs/models/topic-detection), can be true or false": "Habilitar [<PERSON>ec<PERSON> de temas](https://www.assemblyai.com/docs/models/topic-detection), puede ser verdadero o falso", "Customize how words are spelled and formatted using to and from values.\nUse a JSON array of objects of the following format:\n```\n[\n  {\n    \"from\": [\"original\", \"spelling\"],\n    \"to\": \"corrected\"\n  }\n]\n```\n": "Personalizar cómo se escriben las palabras y se formatean usando valores a y a partir de ellas.\nUsar un array JSON de objetos del siguiente formato:\n```\n[\n  {\n    \"from\": [\"original\", \"orto\"],\n    \"a\": \"corrigido\"\n  }\n]\n```\n", "Enable [Sentiment Analysis](https://www.assemblyai.com/docs/models/sentiment-analysis), can be true or false": "Habilitar [<PERSON><PERSON><PERSON><PERSON>timentos](https://www.assemblyai.com/docs/models/sentiment-analysis), puede ser verdadero o falso", "Enable [Auto Chapters](https://www.assemblyai.com/docs/models/auto-chapters), can be true or false": "Habilitar [Auto capítulos](https://www.assemblyai.com/docs/models/auto-chapters), puede ser verdadero o falso", "Enable [Entity Detection](https://www.assemblyai.com/docs/models/entity-detection), can be true or false": "Habilitar [Detección de Entidad](https://www.assemblyai.com/docs/models/entity-detection), puede ser verdadero o falso", "Reject audio files that contain less than this fraction of speech.\nValid values are in the range [0, 1] inclusive.\n": "Rechazar archivos de audio que contengan menos de esta fracción de voz.\nValores válidos están en el rango [0, 1] inclusive.\n", "Enable [Summarization](https://www.assemblyai.com/docs/models/summarization), can be true or false": "Habilitar [Summarization](https://www.assemblyai.com/docs/models/summarization), puede ser verdadero o falso", "The model to summarize the transcript": "El modelo para resumir la transcripción", "The type of summary": "El tipo de resumen", "Enable custom topics, either true or false": "Habilitar temas personalizados, ya sean verdaderos o falsos", "The list of custom topics": "La lista de temas personalizados", "Wait until the transcript status is \"completed\" or \"error\" before moving on to the next step.": "Espere hasta que el estado de la transcripción sea \"completado\" o \"error\" antes de pasar al siguiente paso.", "If the transcript status is \"error\", throw an error.": "Si el estado de la transcripción es \"error\", arroje un error.", "The maximum number of characters per caption": "El número máximo de caracteres por título", "The desired file name for storing in ActivePieces. Make sure the file extension is correct.": "El nombre de archivo deseado para almacenar en ActivePieces. Asegúrese de que la extensión del archivo es correcta.", "Keywords to search for": "Palabras clave a buscar", "Maximum amount of transcripts to retrieve": "Cantidad máxima de transcripciones a recuperar", "Filter by transcript status": "Filtrar por estado de transcripción", "Only get transcripts created on this date": "Sólo obtener transcripciones creadas en esta fecha", "Get transcripts that were created before this transcript ID": "Obtener transcripciones que fueron creadas antes de este ID de transcripción", "Get transcripts that were created after this transcript ID": "Obtener transcripciones que fueron creadas después de este ID de transcripción", "Only get throttled transcripts, overrides the status filter": "Sólo obtener transcripciones de aceleración, anula el filtro de estado", "Your text to prompt the model to produce a desired output, including any context you want to pass into the model.": "Su texto para pedir al modelo que produzca una salida deseada, incluyendo cualquier contexto que desee pasar al modelo.", "A list of completed transcripts with text. Up to a maximum of 100 files or 100 hours, whichever is lower.\nUse either transcript_ids or input_text as input into LeMUR.\n": "A list of completed transcripts with text. Up to a maximum of 100 files or 100 hours, whichever is lower.\nUse either transcript_ids or input_text as input into LeMUR.\n", "Custom formatted transcript data. Maximum size is the context limit of the selected model, which defaults to 100000.\nUse either transcript_ids or input_text as input into LeMUR.\n": "Custom formatted transcript data. Maximum size is the context limit of the selected model, which defaults to 100000.\nUse either transcript_ids or input_text as input into LeMUR.\n", "Context to provide the model. This can be a string or a free-form JSON value.": "Contexto para proporcionar el modelo. Esto puede ser una cadena o un valor JSON de forma libre.", "The model that is used for the final prompt after compression is performed.\n": "El modelo que se utiliza para el indicador final después de que se realiza la compresión.\n", "Max output size in tokens, up to 4000": "Tamaño máximo de salida en tokens, hasta 4000", "The temperature to use for the model.\nHigher values result in answers that are more creative, lower values are more conservative.\nCan be any value between 0.0 and 1.0 inclusive.\n": "La temperatura a usar para el modelo.\nValores más altos dan como resultado respuestas más creativas, valores más bajos son más conservadores.\nPuede ser cualquier valor entre 0.0 y 1.0 inclusive.\n", "The ID of the LeMUR request whose data you want to delete. This would be found in the response of the original request.": "El ID de la solicitud de LeMUR cuyos datos desea eliminar. Esto se encuentra en la respuesta de la solicitud original.", "Authorization headers are injected automatically from your connection.": "Las cabeceras de autorización se inyectan automáticamente desde tu conexión.", "English (Global)": "<PERSON><PERSON><PERSON><PERSON> (Global)", "English (Australian)": "Ing<PERSON><PERSON> (juliano)", "English (British)": "Inglés (británico)", "English (US)": "<PERSON><PERSON><PERSON><PERSON> (US)", "Spanish": "Español", "French": "<PERSON><PERSON><PERSON><PERSON>", "German": "Alemán", "Italian": "Italiano", "Portuguese": "Portugués", "Dutch": "Ho<PERSON><PERSON>", "Afrikaans": "Afrikaans", "Albanian": "Albanés", "Amharic": "Amharic", "Arabic": "<PERSON><PERSON><PERSON>", "Armenian": "Armenio", "Assamese": "<PERSON><PERSON><PERSON>", "Azerbaijani": "Бессия", "Bashkir": "Bashkir", "Basque": "Vasco", "Belarusian": "Bielorruso", "Bengali": "Bengalí", "Bosnian": "Bo<PERSON><PERSON>", "Breton": "Breton", "Bulgarian": "Búlgaro", "Burmese": "Burmese", "Catalan": "Catalán", "Chinese": "Chino", "Croatian": "Croata", "Czech": "Checo", "Danish": "<PERSON><PERSON>", "Estonian": "Estonio", "Faroese": "Faroese", "Finnish": "Finlandés", "Galician": "Galiciano", "Georgian": "<PERSON><PERSON>", "Greek": "Griego", "Gujarati": "Gujarati", "Haitian": "Haitian", "Hausa": "Hausa", "Hawaiian": "<PERSON><PERSON><PERSON>", "Hebrew": "<PERSON><PERSON><PERSON>", "Hindi": "<PERSON><PERSON><PERSON>", "Hungarian": "<PERSON><PERSON><PERSON><PERSON>", "Icelandic": "Icelandic", "Indonesian": "Indonesio/a", "Japanese": "Japonés", "Javanese": "Javanese", "Kannada": "Kannada", "Kazakh": "Kazakh", "Khmer": "Khmer", "Korean": "<PERSON><PERSON>", "Lao": "Lao", "Latin": "Latín", "Latvian": "Letón", "Lingala": "Lingala", "Lithuanian": "Lituano", "Luxembourgish": "luxemburgués", "Macedonian": "Macedonio", "Malagasy": "Malagasy", "Malay": "Malayo", "Malayalam": "Malayalam", "Maltese": "Maltese", "Maori": "<PERSON><PERSON>", "Marathi": "Maratí", "Mongolian": "Mongol", "Nepali": "Nepali", "Norwegian": "Noruego", "Norwegian Nynorsk": "Norwegian Nynorsk", "Occitan": "Occitan", "Panjabi": "Panjabi", "Pashto": "Pashto", "Persian": "<PERSON><PERSON>", "Polish": "Polaco", "Romanian": "<PERSON><PERSON><PERSON>", "Russian": "<PERSON><PERSON><PERSON>", "Sanskrit": "Sanskrit", "Serbian": "<PERSON><PERSON>", "Shona": "<PERSON><PERSON><PERSON>", "Sindhi": "Sindhi", "Sinhala": "Sinhala", "Slovak": "Eslovaco", "Slovenian": "Slovenian", "Somali": "Somali", "Sundanese": "Sundanese", "Swahili": "Swahili", "Swedish": "Sueco", "Tagalog": "Tagalog", "Tajik": "Tayiko", "Tamil": "Tamil", "Tatar": "Tatar", "Telugu": "Telugu", "Thai": "Tailandés", "Tibetan": "Tibetano", "Turkish": "<PERSON><PERSON><PERSON>", "Turkmen": "Turkmen", "Ukrainian": "Ucraniano", "Urdu": "Urdu", "Uzbek": "Uzbek", "Vietnamese": "Vietnamese", "Welsh": "Galés", "Yiddish": "Yídica", "Yoruba": "Yoruba", "Best": "Mejor", "Nano": "<PERSON><PERSON>", "Low": "Baja", "Default": "Por defecto", "High": "Alta", "MP3": "MP3", "WAV": "WAV", "Account Number": "Número de cuenta", "Banking Information": "Información bancaria", "Blood Type": "<PERSON><PERSON><PERSON> de <PERSON>re", "Credit Card CVV": "Tarjeta de crédito CVV", "Credit Card Expiration": "Caducidad de la tarjeta de crédito", "Credit Card Number": "Número de tarjeta de crédito", "Date": "<PERSON><PERSON>", "Date Interval": "<PERSON><PERSON>", "Date of Birth": "Fecha de nacimiento", "Driver's License": "Licencia de conducir", "Drug": "Droga", "Duration": "Duración", "Email Address": "Dirección de email", "Event": "Evento", "Filename": "Nombre de archivo", "Gender Sexuality": "Sexualidad de género", "Healthcare Number": "Número de atención médica", "Injury": "Lesión", "IP Address": "Dirección IP", "Language": "Idioma", "Location": "Ubicación", "Marital Status": "Estado civil", "Medical Condition": "Condición médica", "Medical Process": "Proceso Médico", "Money Amount": "Cantidad de dinero", "Nationality": "Nacionalidad", "Number Sequence": "Secuencia de número", "Occupation": "Ocupación", "Organization": "Organización", "Passport Number": "Número de pasaporte", "Password": "Contraseña", "Person Age": "<PERSON><PERSON> de la <PERSON>", "Person Name": "Nombre de la persona", "Phone Number": "Número de teléfono", "Physical Attribute": "Atributo físico", "Political Affiliation": "Afiliación política", "Religion": "Religión", "Statistics": "Estadísticas", "Time": "<PERSON><PERSON>", "URL": "URL", "US Social Security Number": "Número de Seguro Social de Estados Unidos", "Username": "Usuario", "Vehicle ID": "ID del vehículo", "Zodiac Sign": "Señal de Zodíaco", "Entity Name": "Nombre de la entidad", "Hash": "Hash", "Informative": "Informativo", "Conversational": "Conversacional", "Catchy": "Atrapado", "Bullets": "<PERSON><PERSON>", "Bullets Verbose": "<PERSON><PERSON>", "Gist": "Gist", "Headline": "<PERSON><PERSON><PERSON><PERSON>", "Paragraph": "<PERSON><PERSON><PERSON><PERSON>", "SRT": "SRT", "VTT": "VTT", "Queued": "En cola", "Processing": "Procesando", "Completed": "Completado", "Error": "Error", "Claude 3.5 Sonnet (on Anthropic)": "Claude 3.5 Sonnet (en Antrópico)", "Claude 3 Opus (on Anthropic)": "Opus Claude 3 (en Antrópico)", "Claude 3 Haiku (on Anthropic)": "Claude 3 Haiku (en Antrópico)", "Claude 3 Sonnet (on Anthropic)": "Claude 3 Sonnet (en Antrópico)", "Claude 2.1 (on Anthropic)": "Claude 2.1 (en Antrópico)", "Claude 2 (on Anthropic)": "Claude 2 (en Antrópico)", "Claude Instant 1.2 (on Anthropic)": "<PERSON> 1.2 (en <PERSON><PERSON><PERSON>pic<PERSON>)", "Basic": "Básico", "Mistral 7B (Hosted by AssemblyAI)": "Mistral 7B (Organizado por AssemblblyAI)", "GET": "RECOGER", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "BORRAR", "HEAD": "LIMPIO"}
{"AssemblyAI": "AssemblyAI", "Transcribe and extract data from audio using AssemblyAI's Speech AI.": "Транслируйте и извлекайте данные из аудио с помощью Speech AI AssemblyAI.", "You can retrieve your AssemblyAI API key within your AssemblyAI [Account Settings](https://www.assemblyai.com/app/account?utm_source=activepieces).": "Вы можете получить свой API ключ AssemblyAI в разделе AssemblyAI [Настройки аккаунта](https://www.assemblyai.com/app/account?utm_source=activepieces).", "Upload File": "Загрузить файл", "Transcribe": "Переписать", "Get Transcript": "Get Transcript", "Get Transcript Sentences": "Получать сообщения субтитров", "Get Transcript Paragraphs": "Получить абзацы субтитров", "Get Transcript Subtitles": "Получить субтитры субтитров", "Get Transcript Redacted Audio": "Скачать редактируемый аудио субтитры", "Search words in transcript": "Поиск слов в субтитрах", "List transcripts": "Список субтитров", "Delete transcript": "Удалить субтитры", "Run a Task using LeMUR": "Запуск задачи с помощью LeMUR", "Retrieve LeMUR response": "Получить LeMUR ответ", "Purge LeMUR request data": "Очистить LeMUR запрос данных", "Custom API Call": "Пользовательский вызов API", "Upload a media file to AssemblyAI's servers.": "Загрузите медиа файл на серверы AssemblyAI.", "Transcribe an audio or video file using AssemblyAI.": "Преобразовать аудио или видео файл с помощью AssemblyAI.", "Retrieves a transcript by its ID.": "Получает субтитры по идентификатору.", "Retrieve the sentences of the transcript by its ID.": "Получить предложения субтитров по идентификатору.", "Retrieve the paragraphs of the transcript by its ID.": "Получить абзацы субтитров по его ID.", "Export the transcript as SRT or VTT subtitles.": "Экспортировать субтитры в SRT или VTT.", "Get the result of the redacted audio model.": "Получить результат отредактированной аудио модели.", "Search through the transcript for keywords. You can search for individual words, numbers, or phrases containing up to five words or numbers.": "Поиск по субтитрам ключевых слов. Можно искать отдельные слова, цифры или фразы, содержащие до пяти слов или цифр.", "Retrieve a list of transcripts you created.\nTranscripts are sorted from newest to oldest. The previous URL always points to a page with older transcripts.": "Retrieve a list of transcripts you created.\nTranscripts are sorted from newest to oldest. The previous URL always points to a page with older transcripts.", "Remove the data from the transcript and mark it as deleted.": "Удалите данные из субтитров и пометьте их как удаленные.", "Use the LeMUR task endpoint to input your own LLM prompt.": "Используйте конечную точку задания LeMUR для ввода собственного запроса LLM.", "Retrieve a LeMUR response that was previously generated.": "Получить LeMUR ответ, который был ранее создан.", "Delete the data for a previously submitted LeMUR request.\nThe LLM response data, as well as any context provided in the original request will be removed.": "Удалить данные для ранее представленного запроса LeMUR.\nДанные ответа LLM, а также любой контекст, указанный в первоначальном запросе, будут удалены.", "Make a custom API call to a specific endpoint": "Сделать пользовательский API вызов к определенной конечной точке", "Audio File": "Аудио файл", "Audio URL": "URL аудио", "Language Code": "Код языка", "Language Detection": "Обнаружение языка", "Language Confidence Threshold": "Порог доверия языка", "Speech Model": "Модель речи", "Punctuate": "Punctuate", "Format Text": "Форматировать текст", "Disfluencies": "Различия", "Dual Channel": "Двойной канал", "Webhook URL": "URL вебхука", "Webhook Auth Header Name": "Имя заголовка аутентификации вебхука", "Webhook Auth Header Value": "Значение заголовка авторизации Webhook", "Key Phrases": "Ключевые фразы", "Audio Start From": "Начало звука с", "Audio End At": "Окончание аудио в", "Word Boost": "Усиление слов", "Word Boost Level": "Уровень усиления слов", "Filter Profanity": "Фильтровать нецензуру", "Redact PII": "Redact PII", "Redact PII Audio": "Redact PII Audio", "Redact PII Audio Quality": "Качество Redact PII аудио", "Redact PII Policies": "Redact PII Policies", "Redact PII Substitution": "Исправить замену PII", "Speaker Labels": "Метки динамиков", "Speakers Expected": "Ожида<PERSON>ись спикеры", "Content Moderation": "Модерация контента", "Content Moderation Confidence": "Уверенность в модерацию контента", "Topic Detection": "Обнаружение темы", "Custom Spellings": "Пользовательские правописания", "Sentiment Analysis": "Ана<PERSON>из сущностей", "Auto Chapters": "Авто главы", "Entity Detection": "Обнаружение сущностей", "Speech Threshold": "Порог речи", "Enable Summarization": "Включить сводку", "Summary Model": "Обобщающая модель", "Summary Type": "Тип сводки", "Enable Custom Topics": "Включить пользовательские темы", "Custom Topics": "Пользовательские темы", "Wait until transcript is ready": "Подождите, пока субтитры будут готовы", "Throw if transcript status is error": "Бросить при ошибке субтитров", "Transcript ID": "ID субтитров", "Subtitles Format": "Фор<PERSON>а<PERSON> суб<PERSON><PERSON><PERSON><PERSON><PERSON>", "Number of Characters per Caption": "Количество символов в одной подписи", "Download file?": "Загрузить файл?", "Download File Name": "Имя файла", "Words": "Слова", "Limit": "<PERSON>и<PERSON><PERSON><PERSON>", "Status": "Status", "Created On": "Создано", "Before ID": "Перед ID", "After ID": "После ID", "Throttled Only": "Только обмороженные", "Prompt": "Prompt", "Transcript IDs": "ID субтитров", "Input Text": "Input Text", "Context": "Контекст", "Final Model": "Окончательная модель", "Maximum Output Size": "Максимальный размер вывода", "Temperature": "Температура", "LeMUR request ID": "ID запроса LeMUR", "Method": "Метод", "Headers": "Заголовки", "Query Parameters": "Параметры запроса", "Body": "Тело", "No Error on Failure": "Нет ошибок при ошибке", "Timeout (in seconds)": "Таймаут (в секундах)", "The File or URL of the audio or video file.": "Файл или URL аудио или видео файла.", "The URL of the audio or video file to transcribe.": "URL передаваемого аудио или видео файла.", "The language of your audio file. Possible values are found in [Supported Languages](https://www.assemblyai.com/docs/concepts/supported-languages).\nThe default value is 'en_us'.\n": "Язык аудио файла. Возможные значения находятся в [Поддерживаемые языки](https://www.assemblyai.com/docs/concepts/supported-languages).\nзначение по умолчанию 'en_us'.\n", "Enable [Automatic language detection](https://www.assemblyai.com/docs/models/speech-recognition#automatic-language-detection), either true or false.": "Включите [Автоматическое определение языка](https://www.assemblyai.com/docs/models/speech-recognition#automatic-language-detection), либо true или false.", "The confidence threshold for the automatically detected language.\nAn error will be returned if the language confidence is below this threshold.\nDefaults to 0.\n": "Порог доверия для автоматически обнаруженного языка.\nОшибка будет возвращена, если доверие языка ниже этого порога.\nПо умолчанию 0.\n", "The speech model to use for the transcription. When `null`, the \"best\" model is used.": "Модель речи, используемая для транскрипции. Когда 'null' используется лучшая модель.", "Enable Automatic Punctuation, can be true or false": "Включить автоматическую пунктуацию, может быть true или false", "Enable Text Formatting, can be true or false": "Включить форматирование текста, может быть true или false", "Transcribe Filler Words, like \"umm\", in your media file; can be true or false": "Transcribe Filler Words, such as \"umm\", in your media file; can be true or false", "Enable [Dual Channel](https://www.assemblyai.com/docs/models/speech-recognition#dual-channel-transcription) transcription, can be true or false.": "Включить транскрипцию [Dual Channel](https://www.assemblyai.com/docs/models/speech-recognition#dual-channel-transcription), может быть true или false.", "The URL to which we send webhook requests.\nWe sends two different types of webhook requests.\nOne request when a transcript is completed or failed, and one request when the redacted audio is ready if redact_pii_audio is enabled.\n": "The URL to which we send webhook requests.\nWe sends two different types of webhook requests.\nOne request when a transcript is completed or failed, and one request when the redacted audio is ready if redact_pii_audio is enabled.\n", "The header name to be sent with the transcript completed or failed webhook requests": "Имя заголовка для отправки с субтитрами завершено или не выполнено с помощью webhook запросов", "The header value to send back with the transcript completed or failed webhook requests for added security": "Значение заголовка для отправки с субтитрами завершено или неудавшиеся запросы webhook для дополнительной безопасности", "Enable Key Phrases, either true or false": "Включить ключевые фразы, true или false", "The point in time, in milliseconds, to begin transcribing in your media file": "Точка времени, в миллисекундах, начать трансляцию в вашем медиа-файле", "The point in time, in milliseconds, to stop transcribing in your media file": "Точка времени, в миллисекундах, остановить трансляцию в вашем медиа-файле", "The list of custom vocabulary to boost transcription probability for": "Список пользовательского словаря для увеличения вероятности транскрипции для", "How much to boost specified words": "Сколько увеличивать указанные слова", "Filter profanity from the transcribed text, can be true or false": "Фильтровать ненормативную лексику из переписанного текста может быть истинной или ложной", "Redact PII from the transcribed text using the Redact PII model, can be true or false": "Редкий PII из переписанного текста с использованием модели Redact PII, может быть истинным или ложным", "Generate a copy of the original media file with spoken PII \"beeped\" out, can be true or false. See [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) for more details.": "Создать копию оригинального медиа-файла с говорящим PII \"обезглавленным\", может быть правдой или ложным. См. [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) для получения более подробной информации.", "Controls the filetype of the audio created by redact_pii_audio. Currently supports mp3 (default) and wav. See [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) for more details.": "Контролирует тип файлов, созданный redact_pii_audio. В настоящее время поддерживает mp3 (по умолчанию) и wav. Смотрите [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) для получения более подробной информации.", "The list of PII Redaction policies to enable. See [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) for more details.": "Список политик исправления PII для включения. Смотрите [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) для получения более подробной информации.", "The replacement logic for detected PII, can be \"entity_type\" or \"hash\". See [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) for more details.": "Обнаружена логика замены PII, может быть \"entity_type\" или \"hash\". Подробнее смотрите в [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction).", "Enable [Speaker diarization](https://www.assemblyai.com/docs/models/speaker-diarization), can be true or false": "Включить [Диаризацию динамика](https://www.assemblyai.com/docs/models/speaker-diarization), может быть true или ложным", "Tells the speaker label model how many speakers it should attempt to identify, up to 10. See [Speaker diarization](https://www.assemblyai.com/docs/models/speaker-diarization) for more details.": "Сообщает модель, в которой динамик должен быть определен до 10. Подробнее см. в разделе [Диаризация динамика](https://www.assemblyai.com/docs/models/speaker-diarization).", "Enable [Content Moderation](https://www.assemblyai.com/docs/models/content-moderation), can be true or false": "Включить [Контент модера](https://www.assemblyai.com/docs/models/content-moderation), может быть true или false", "The confidence threshold for the Content Moderation model. Values must be between 25 and 100.": "Порог доверия модели моделирования контента. Значения должны быть от 25 до 100.", "Enable [Topic Detection](https://www.assemblyai.com/docs/models/topic-detection), can be true or false": "Включите [Topic Detection](https://www.assemblyai.com/docs/models/topic-detection), может быть true или false", "Customize how words are spelled and formatted using to and from values.\nUse a JSON array of objects of the following format:\n```\n[\n  {\n    \"from\": [\"original\", \"spelling\"],\n    \"to\": \"corrected\"\n  }\n]\n```\n": "Настройте порядок написания слов и их форматирования с использованием значений и значений.\nИспользуйте JSON массив объектов следующего формата:\n```\n[\n  {\n    \"from\": [\"original\", \"spell\"],\n    \"до\": \"corrected\"\n  }\n]\n```\n", "Enable [Sentiment Analysis](https://www.assemblyai.com/docs/models/sentiment-analysis), can be true or false": "Включите [Sentiment Analysis](https://www.assemblyai.com/docs/models/sentiment-analysis), может быть true или false", "Enable [Auto Chapters](https://www.assemblyai.com/docs/models/auto-chapters), can be true or false": "Включить [Auto Chapters](https://www.assemblyai.com/docs/models/auto-chapters), может быть true или false", "Enable [Entity Detection](https://www.assemblyai.com/docs/models/entity-detection), can be true or false": "Включить [Обнаружение сущностей](https://www.assemblyai.com/docs/models/entity-detection), может быть true или false", "Reject audio files that contain less than this fraction of speech.\nValid values are in the range [0, 1] inclusive.\n": "Отклонить аудио файлы, содержащие меньше этой доли речи.\nДопустимые значения находятся в диапазоне [0, 1] включительно.\n", "Enable [Summarization](https://www.assemblyai.com/docs/models/summarization), can be true or false": "Включить [Summarization](https://www.assemblyai.com/docs/models/summarization), может быть true или false", "The model to summarize the transcript": "Модель суммирования субтитров", "The type of summary": "Тип сводки", "Enable custom topics, either true or false": "Включить пользовательские темы, true или false", "The list of custom topics": "Список пользовательских тем", "Wait until the transcript status is \"completed\" or \"error\" before moving on to the next step.": "Подождите, пока субтитры не будут \"завершены\" или \"ошибка\" перед переходом на следующий шаг.", "If the transcript status is \"error\", throw an error.": "Если транскрипт имеет статус \"ошибка\", то попробуйте продолжить.", "The maximum number of characters per caption": "Максимальное количество символов в подписи", "The desired file name for storing in ActivePieces. Make sure the file extension is correct.": "Желаемое имя файла для хранения в ActivePieces. Убедитесь, что расширение файла правильное.", "Keywords to search for": "Ключевые слова для поиска", "Maximum amount of transcripts to retrieve": "Максимальное количество субтитров для получения", "Filter by transcript status": "Фильтровать по статусу субтитров", "Only get transcripts created on this date": "Получать только субтитры созданные на эту дату", "Get transcripts that were created before this transcript ID": "Получить субтитры, которые были созданы до этого идентификатора субтитров", "Get transcripts that were created after this transcript ID": "Получить субтитры, которые были созданы после этого идентификатора субтитров", "Only get throttled transcripts, overrides the status filter": "Получать только отлаженные субтитры, перезаписывает фильтр состояния", "Your text to prompt the model to produce a desired output, including any context you want to pass into the model.": "Ваш текст, чтобы побудить модель произвести желаемый выход, включая любой контекст, который вы хотите передать в модель.", "A list of completed transcripts with text. Up to a maximum of 100 files or 100 hours, whichever is lower.\nUse either transcript_ids or input_text as input into LeMUR.\n": "Список завершенных субтитров с текстом. Максимальное количество файлов - 100 часов, в зависимости от того, что меньше.\nИспользуйте транскрипты или input_text в качестве ввода в LeMUR.\n", "Custom formatted transcript data. Maximum size is the context limit of the selected model, which defaults to 100000.\nUse either transcript_ids or input_text as input into LeMUR.\n": "Пользовательские форматированные данные субтитров. Максимальный размер - ограничение контекста выбранной модели, по умолчанию 100000.\nИспользуйте транскрипты или input_text в качестве ввода в LeMUR.\n", "Context to provide the model. This can be a string or a free-form JSON value.": "Контекст для представления модели. Это может быть строка или произвольное значение JSON.", "The model that is used for the final prompt after compression is performed.\n": "Модель, используемая для финального подсказки после сжатия.\n", "Max output size in tokens, up to 4000": "Максимальный размер выхода в токенах, до 4000", "The temperature to use for the model.\nHigher values result in answers that are more creative, lower values are more conservative.\nCan be any value between 0.0 and 1.0 inclusive.\n": "The temperature to use for the model.\nHigher values result in answers that are more creative, lower values are more conservative.\nCan be any value between 0.0 and 1.0 inclusive.\n", "The ID of the LeMUR request whose data you want to delete. This would be found in the response of the original request.": "Идентификатор запроса LeMUR, данные которого вы хотите удалить. Он будет найден в ответ на первоначальный запрос.", "Authorization headers are injected automatically from your connection.": "Заголовки авторизации включаются автоматически из вашего соединения.", "English (Global)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Мир)", "English (Australian)": "<PERSON>н<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Австралия)", "English (British)": "Английский (английский)", "English (US)": "Английский", "Spanish": "Испанский", "French": "Французский", "German": "Немецкий", "Italian": "Итальянский", "Portuguese": "Португальский", "Dutch": "Голландский", "Afrikaans": "Аф<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Albanian": "Албанский", "Amharic": "Amharic", "Arabic": "Арабский", "Armenian": "Армянский", "Assamese": "<unk> ский", "Azerbaijani": "Азерб<PERSON>йджан", "Bashkir": "Bashkir", "Basque": "Баскский", "Belarusian": "Белорусский", "Bengali": "Бенгальский", "Bosnian": "Боснийский", "Breton": "Breton", "Bulgarian": "Болгарский", "Burmese": "Burmese", "Catalan": "Каталанский", "Chinese": "Китайский", "Croatian": "Хорватский", "Czech": "Чешский", "Danish": "Датский", "Estonian": "Эстонский", "Faroese": "Faroese", "Finnish": "Финский", "Galician": "Галисийский", "Georgian": "Грузинский", "Greek": "Греческий", "Gujarati": "Gujarati", "Haitian": "Haitian", "Hausa": "Hausa", "Hawaiian": "Гавайский", "Hebrew": "<PERSON>в<PERSON><PERSON>т", "Hindi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Hungarian": "Венгерский", "Icelandic": "Icelandic", "Indonesian": "Индонезийский", "Japanese": "Японский", "Javanese": "Javanese", "Kannada": "Kannada", "Kazakh": "Kazakh", "Khmer": "Khmer", "Korean": "Корейский", "Lao": "Lao", "Latin": "Латынь", "Latvian": "Латышский", "Lingala": "Lingala", "Lithuanian": "Литовский", "Luxembourgish": "Люксембургский", "Macedonian": "Македонский", "Malagasy": "Malagasy", "Malay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Malayalam": "Мал<PERSON><PERSON><PERSON>ам", "Maltese": "Maltese", "Maori": "<PERSON><PERSON>", "Marathi": "Мара<PERSON>ти", "Mongolian": "Монгольский", "Nepali": "Nepali", "Norwegian": "Норвежский", "Norwegian Nynorsk": "Norwegian Nynorsk", "Occitan": "Occitan", "Panjabi": "Panjabi", "Pashto": "Пашто", "Persian": "Персидский", "Polish": "Польский", "Romanian": "Румынский", "Russian": "Русский", "Sanskrit": "Sanskrit", "Serbian": "Сербский", "Shona": "<PERSON><PERSON><PERSON>", "Sindhi": "Sindhi", "Sinhala": "Сингала", "Slovak": "Словацкий", "Slovenian": "Slovenian", "Somali": "Somali", "Sundanese": "Sundanese", "Swahili": "Суахили", "Swedish": "Шведский", "Tagalog": "Tagalog", "Tajik": "Т<PERSON><PERSON><PERSON><PERSON><PERSON>", "Tamil": "Tamil", "Tatar": "Tatar", "Telugu": "Telugu", "Thai": "Тайский", "Tibetan": "Тибетский", "Turkish": "Турецкий", "Turkmen": "Turkmen", "Ukrainian": "Украинский", "Urdu": "Урду", "Uzbek": "Uzbek", "Vietnamese": "Vietnamese", "Welsh": "Уэльш", "Yiddish": "Иди<PERSON>", "Yoruba": "Yoruba", "Best": "Лучшее", "Nano": "Нано", "Low": "Низкий", "Default": "По умолчанию", "High": "Высокий", "MP3": "MP3", "WAV": "WAV", "Account Number": "Номер счета", "Banking Information": "Банковская информация", "Blood Type": "<PERSON>и<PERSON> крови", "Credit Card CVV": "Кредитная карта CVV", "Credit Card Expiration": "Срок действия кредитной карты", "Credit Card Number": "Номер кредитной карты", "Date": "Дата", "Date Interval": "Дата интервала", "Date of Birth": "Дата рождения", "Driver's License": "Водительское удостоверение", "Drug": "Наркотики", "Duration": "Duration", "Email Address": "Email Address", "Event": "Событие", "Filename": "Имя файла", "Gender Sexuality": "Сексуальность по признаку пола", "Healthcare Number": "Номер здоровья", "Injury": "Травма", "IP Address": "IP-адрес", "Language": "Язык", "Location": "Местоположение", "Marital Status": "Семейное положение", "Medical Condition": "Состояние здоровья", "Medical Process": "Медицинский процесс", "Money Amount": "Сумма денег", "Nationality": "Гражданство", "Number Sequence": "Числовая последовательность", "Occupation": "Занятость", "Organization": "Организация", "Passport Number": "Номер паспорта", "Password": "Пароль", "Person Age": "Возраст человека", "Person Name": "Имя пользователя", "Phone Number": "Номер телефона", "Physical Attribute": "Физический атрибут", "Political Affiliation": "Политическое партнерство", "Religion": "Религия", "Statistics": "Статистика", "Time": "Время", "URL": "URL", "US Social Security Number": "Номер социального страхования США", "Username": "Имя пользователя", "Vehicle ID": "ID транспортного средства", "Zodiac Sign": "Знак Zodiac", "Entity Name": "Название сущности", "Hash": "Хэш", "Informative": "Информационный", "Conversational": "Разговор", "Catchy": "К<PERSON>ш<PERSON><PERSON><PERSON>", "Bullets": "Пули", "Bullets Verbose": "Подробности о пулях", "Gist": "Жизнь", "Headline": "Заголовок", "Paragraph": "Пункт 2", "SRT": "SRT", "VTT": "VTT", "Queued": "В очереди", "Processing": "Обработка", "Completed": "Выполнено", "Error": "Ошибка", "Claude 3.5 Sonnet (on Anthropic)": "Claude 3.5 Sonnet (на антропке)", "Claude 3 Opus (on Anthropic)": "Claude 3 Opus (на антропском)", "Claude 3 Haiku (on Anthropic)": "Клод 3 Хайку (на антропии)", "Claude 3 Sonnet (on Anthropic)": "Клод 3 Соннет (на антропии)", "Claude 2.1 (on Anthropic)": "Клод 2.1 (по антропскому)", "Claude 2 (on Anthropic)": "Клод 2 (по антропии)", "Claude Instant 1.2 (on Anthropic)": "Клод Мгновенный 1.2 (на антропии)", "Basic": "Базовый", "Mistral 7B (Hosted by AssemblyAI)": "Mistral 7B (Hosted by AssemblyAI)", "GET": "ПОЛУЧИТЬ", "POST": "ПОСТ", "PATCH": "ПАТЧ", "PUT": "ПОКУПИТЬ", "DELETE": "УДАЛИТЬ", "HEAD": "HEAD"}
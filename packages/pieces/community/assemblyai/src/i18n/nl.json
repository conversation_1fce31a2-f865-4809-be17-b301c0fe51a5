{"AssemblyAI": "AssemblyAI", "Transcribe and extract data from audio using AssemblyAI's Speech AI.": "G<PERSON>vens uit audio schrijven en extraheren met <PERSON><PERSON><PERSON> van de AssemblyAI's spraak <PERSON>.", "You can retrieve your AssemblyAI API key within your AssemblyAI [Account Settings](https://www.assemblyai.com/app/account?utm_source=activepieces).": "Je kan je AssemblyAI API-sleutel ophalen in je Buurderij [Accountinstellingen](https://www.assemblyai.com/app/account?utm_source=activepieces).", "Upload File": "Bestand uploaden", "Transcribe": "Transcribe", "Get Transcript": "Get Transcript", "Get Transcript Sentences": "Transcript strings ophalen", "Get Transcript Paragraphs": "Transcriptond<PERSON><PERSON><PERSON>", "Get Transcript Subtitles": "Transcriptondertitels ophalen", "Get Transcript Redacted Audio": "Transcript audio Redacted krijgen", "Search words in transcript": "<PERSON><PERSON> woorden in transcript", "List transcripts": "Lijst transcripten", "Delete transcript": "<PERSON><PERSON><PERSON><PERSON><PERSON> transcript", "Run a Task using LeMUR": "<PERSON><PERSON><PERSON> een taak uit met LeMUR", "Retrieve LeMUR response": "Haal LeMUR reactie op", "Purge LeMUR request data": "LeMUR aanvraaggegevens verwijderen", "Custom API Call": "Custom API Call", "Upload a media file to AssemblyAI's servers.": "Upload een mediabestand naar de servers van AssemblyAI.", "Transcribe an audio or video file using AssemblyAI.": "Een audio- of videobestand vertalen via AssemblyAI.", "Retrieves a transcript by its ID.": "<PERSON><PERSON>t een transcript op via zijn ID.", "Retrieve the sentences of the transcript by its ID.": "<PERSON><PERSON> <PERSON> zinnen van het transcript op met zijn <PERSON>.", "Retrieve the paragraphs of the transcript by its ID.": "<PERSON><PERSON> <PERSON> al<PERSON>a's van het transcript op via zijn ID.", "Export the transcript as SRT or VTT subtitles.": "Exporteer het afschrift als SRT of VTT ondertitels.", "Get the result of the redacted audio model.": "K<PERSON><PERSON><PERSON> het resultaat van het roodgedefineerde audiomodel.", "Search through the transcript for keywords. You can search for individual words, numbers, or phrases containing up to five words or numbers.": "<PERSON><PERSON> in de transcript naar trefwoorden. Je kunt zoeken naar individuele woorden, cijfers of zinnen met maximaal vijf woorden of cijfers.", "Retrieve a list of transcripts you created.\nTranscripts are sorted from newest to oldest. The previous URL always points to a page with older transcripts.": "Haal een lijst op met transcripten die u hebt gemaakt.\nTransscripts worden gesorteerd van nieuwste naar oudste. De vorige URL verwijst altijd naar een pagina met oudere transcripts.", "Remove the data from the transcript and mark it as deleted.": "<PERSON>er<PERSON><PERSON><PERSON> de gegevens uit de transcript en markeer als verwijderd.", "Use the LeMUR task endpoint to input your own LLM prompt.": "Gebruik het LeMUR taakeindpunt om uw eigen LLM prompte in te voeren.", "Retrieve a LeMUR response that was previously generated.": "Krijg een LeMUR reactie die eerder werd gegenereerd.", "Delete the data for a previously submitted LeMUR request.\nThe LLM response data, as well as any context provided in the original request will be removed.": "Verwijder de gegevens van een eerder ingediend LeMUR verzoek.\nDe LLM reactiegegevens en elke context die in het oorspronkelijke verzoek wordt gegeven, worden verwijderd.", "Make a custom API call to a specific endpoint": "Maak een aangepaste API call naar een specifiek eindpunt", "Audio File": "Audio bestand", "Audio URL": "Audio URL", "Language Code": "Taal Code", "Language Detection": "Taal detectie", "Language Confidence Threshold": "Taal vertrouwen drempel", "Speech Model": "<PERSON><PERSON><PERSON> model", "Punctuate": "Punctuate", "Format Text": "Formateer tekst", "Disfluencies": "Disfluencies", "Dual Channel": "<PERSON><PERSON>", "Webhook URL": "Webhook URL", "Webhook Auth Header Name": "Webhook Auth Header naam", "Webhook Auth Header Value": "Webhook Auth Header waarde", "Key Phrases": "S<PERSON><PERSON><PERSON>zinnen", "Audio Start From": "Audio start vanaf", "Audio End At": "Audio eindigt op", "Word Boost": "Woord Boost", "Word Boost Level": "Woord Boost Niveau", "Filter Profanity": "<PERSON><PERSON> groot<PERSON>id", "Redact PII": "Redact PII", "Redact PII Audio": "Redact PII Audio", "Redact PII Audio Quality": "Redacte PII audiokwaliteit", "Redact PII Policies": "Redact PII Policies", "Redact PII Substitution": "Redact PII Plaatsvervanger", "Speaker Labels": "Luidspreker labels", "Speakers Expected": "Sprek<PERSON> verwacht", "Content Moderation": "Content Moderatie", "Content Moderation Confidence": "Content <PERSON><PERSON><PERSON>", "Topic Detection": "Onderwerp detectie", "Custom Spellings": "Aangepast<PERSON>", "Sentiment Analysis": "Sentiment Analyse", "Auto Chapters": "Auto Hoofdstukken", "Entity Detection": "Entiteit Detectie", "Speech Threshold": "<PERSON><PERSON><PERSON> drempel", "Enable Summarization": "Samenvatting inschakelen", "Summary Model": "Samenvatting Model", "Summary Type": "Samenvatting Type", "Enable Custom Topics": "Aangepaste onderwerpen inschakelen", "Custom Topics": "Aangepaste onderwerpen", "Wait until transcript is ready": "Wacht tot transcript klaar is", "Throw if transcript status is error": "Gooi als transcript status is fout", "Transcript ID": "Transcriptie ID", "Subtitles Format": "Ondertiteling formaat", "Number of Characters per Caption": "Aantal tekens per bijschrift", "Download file?": "Bestand downloaden?", "Download File Name": "Bestandsnaam voor downloaden", "Words": "Woorden", "Limit": "<PERSON><PERSON>", "Status": "status", "Created On": "Aangemaakt op", "Before ID": "Voor het ID", "After ID": "Na het ID", "Throttled Only": "<PERSON><PERSON>", "Prompt": "Prompt", "Transcript IDs": "Transcript-ID's", "Input Text": "Input Text", "Context": "Context", "Final Model": "Laatste model", "Maximum Output Size": "Maximale uitvoergrootte", "Temperature": "Temperatuur", "LeMUR request ID": "LeMUR verzoek ID", "Method": "<PERSON>e", "Headers": "Kopteksten", "Query Parameters": "Query parameters", "Body": "<PERSON><PERSON><PERSON>", "No Error on Failure": "<PERSON><PERSON> fout bij fout", "Timeout (in seconds)": "Time-out (in seconden)", "The File or URL of the audio or video file.": "<PERSON>t bestand of URL van het audio of video bestand.", "The URL of the audio or video file to transcribe.": "De URL van het audio of video bestand om te overschrijven.", "The language of your audio file. Possible values are found in [Supported Languages](https://www.assemblyai.com/docs/concepts/supported-languages).\nThe default value is 'en_us'.\n": "De taal van uw audiobestand. Mogelijke waarden vindt u in [ondersteunde talen](https://www.assemblyai.com/docs/concepts/supported-languages).\nDe standaardwaarde is 'en_us'.\n", "Enable [Automatic language detection](https://www.assemblyai.com/docs/models/speech-recognition#automatic-language-detection), either true or false.": "In<PERSON><PERSON><PERSON> van [Automatische taaldetectie](https://www.assemblyai.com/docs/models/speech-recognition#automatic-language-detection), waar of onwaar.", "The confidence threshold for the automatically detected language.\nAn error will be returned if the language confidence is below this threshold.\nDefaults to 0.\n": "De vertrouwensdrempel voor de automatisch gedetecteerde taal.\nEr wordt een fout geretourneerd als de taalcontrole onder deze drempel is.\nStandaard ingesteld op 0.\n", "The speech model to use for the transcription. When `null`, the \"best\" model is used.": "Het te gebruiken spraakmodel voor de transcriptie. <PERSON><PERSON> `null`, het \"beste\" model wordt gebruikt.", "Enable Automatic Punctuation, can be true or false": "Automatische leestekens inschakelen, kan waar of onwaar zijn", "Enable Text Formatting, can be true or false": "Teks<PERSON><PERSON><PERSON> inschakelen, kan waar of onwaar zijn", "Transcribe Filler Words, like \"umm\", in your media file; can be true or false": "Transcribe Filler Words, zoals \"umm\", in je mediabestand; kan waar of onwaar zijn", "Enable [Dual Channel](https://www.assemblyai.com/docs/models/speech-recognition#dual-channel-transcription) transcription, can be true or false.": "Insch<PERSON><PERSON> [Dual Channel](https://www.assemblyai.com/docs/models/speech-recognition#dual-channel-transcription) transcriptie, kan waar of niet waar zijn.", "The URL to which we send webhook requests.\nWe sends two different types of webhook requests.\nOne request when a transcript is completed or failed, and one request when the redacted audio is ready if redact_pii_audio is enabled.\n": "De URL waarnaar we webhook verzoeken sturen.\nWe sturen twee verschillende soorten webhook verzoeken.\nEen verzoek wanneer een transcript is voltooid of mislukt, en één verzoek wanneer redact_pii_audio klaar is als redact_audio is ingeschakeld.\n", "The header name to be sent with the transcript completed or failed webhook requests": "De naam van de header die wordt verzonden met de ingevulde of mislukte webhook verzoeken", "The header value to send back with the transcript completed or failed webhook requests for added security": "De header waarde om terug te verzenden met de ingevulde of mislukte webhook verzoeken voor extra beveiliging", "Enable Key Phrases, either true or false": "<PERSON><PERSON><PERSON><PERSON>, waar of niet waar", "The point in time, in milliseconds, to begin transcribing in your media file": "Het ti<PERSON><PERSON><PERSON> in milliseconden om te beginnen met om<PERSON><PERSON> in uw mediabestand", "The point in time, in milliseconds, to stop transcribing in your media file": "Het tij<PERSON><PERSON> in milliseconden om te stoppen met het om<PERSON><PERSON> in uw mediabestand", "The list of custom vocabulary to boost transcription probability for": "<PERSON>i<PERSON> met aangepaste woordenschat om de kans op transcriptie te verhogen voor", "How much to boost specified words": "Ho<PERSON><PERSON> je de opgegeven woorden wilt boosten", "Filter profanity from the transcribed text, can be true or false": "Filter profaniteit op de getranscrimeerde tekst, kan waar of onwaar zijn", "Redact PII from the transcribed text using the Redact PII model, can be true or false": "Redact PII uit de getranscrimeer<PERSON> te<PERSON> met be<PERSON><PERSON> van het Redact PII-model, kan waar of onwaar zijn", "Generate a copy of the original media file with spoken PII \"beeped\" out, can be true or false. See [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) for more details.": "Genereer een kopie van het originele mediabestand met gesproken PII \"beeped\" uit, kan waar of onwaar zijn. Zie [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) voor meer details.", "Controls the filetype of the audio created by redact_pii_audio. Currently supports mp3 (default) and wav. See [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) for more details.": "Bepaalt het bestandstype van het audio gemaakt door redact_pii_audio. Momenteel ondersteunt mp3 (standaard) en wav. Zie [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) voor meer details.", "The list of PII Redaction policies to enable. See [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) for more details.": "De lijst van PII Redactie beleid om in te schakelen. Zie [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) voor meer details.", "The replacement logic for detected PII, can be \"entity_type\" or \"hash\". See [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) for more details.": "De vervanging van PII kan \"entity_type\" of \"hash\" zijn. Z<PERSON> [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) voor meer informatie.", "Enable [Speaker diarization](https://www.assemblyai.com/docs/models/speaker-diarization), can be true or false": "In<PERSON><PERSON><PERSON> [<PERSON><PERSON><PERSON><PERSON> diarizatie](https://www.assemblyai.com/docs/models/speaker-diarization), kan waar of onwaar zijn", "Tells the speaker label model how many speakers it should attempt to identify, up to 10. See [Speaker diarization](https://www.assemblyai.com/docs/models/speaker-diarization) for more details.": "Vertelt het luidsprekerlabel hoeveel sprekers het moet proberen te identificeren, tot 10. Zie [Spreker diarizatie] (https://www.assemblyai.com/docs/models/speaker-diarization) voor meer informatie.", "Enable [Content Moderation](https://www.assemblyai.com/docs/models/content-moderation), can be true or false": "<PERSON><PERSON><PERSON><PERSON> van [Content Moderation](https://www.assemblyai.com/docs/models/content-moderation), kan waar of onwaar zijn", "The confidence threshold for the Content Moderation model. Values must be between 25 and 100.": "De vertrouwensdrempel voor het Content Moderation model. Waarden moeten tussen 25 en 100 liggen.", "Enable [Topic Detection](https://www.assemblyai.com/docs/models/topic-detection), can be true or false": "<PERSON><PERSON><PERSON><PERSON> van [R<PERSON><PERSON><PERSON> detectie](https://www.assemblyai.com/docs/models/topic-detection), kan waar of onwaar zijn", "Customize how words are spelled and formatted using to and from values.\nUse a JSON array of objects of the following format:\n```\n[\n  {\n    \"from\": [\"original\", \"spelling\"],\n    \"to\": \"corrected\"\n  }\n]\n```\n": "Pas aan hoe woorden worden gespeld en opgemaakt met be<PERSON><PERSON> van en van waarden.\nGebruik een JSON array van objecten van het volgende formaat:\n```\n[\n  {\n    \"van: [\"origineel\", \"spelling\"],\n    \"to\": \"gecorrigeerd\"\n  }\n]\n```\n", "Enable [Sentiment Analysis](https://www.assemblyai.com/docs/models/sentiment-analysis), can be true or false": "<PERSON><PERSON><PERSON><PERSON> van [Sentiment Analysis](https://www.assemblyai.com/docs/models/sentiment-analysis), kan waar of on<PERSON>ar zijn", "Enable [Auto Chapters](https://www.assemblyai.com/docs/models/auto-chapters), can be true or false": "<PERSON><PERSON><PERSON><PERSON> van [Auto Chapters](https://www.assemblyai.com/docs/models/auto-chapters), kan waar of onwaar zijn", "Enable [Entity Detection](https://www.assemblyai.com/docs/models/entity-detection), can be true or false": "<PERSON><PERSON><PERSON><PERSON> van [Entiteit Detection](https://www.assemblyai.com/docs/models/entity-detection), kan waar of onwaar zijn", "Reject audio files that contain less than this fraction of speech.\nValid values are in the range [0, 1] inclusive.\n": "Audiobestanden afwijzen die minder dan deze fractie van spraak bevatten.\nGeldige waarden liggen binnen het bereik [0, 1] inclusief.\n", "Enable [Summarization](https://www.assemblyai.com/docs/models/summarization), can be true or false": "[Summarization](https://www.assemblyai.com/docs/models/summarization) inschakelen, kan waar of onwaar zijn", "The model to summarize the transcript": "Het model om de transcript samen te vatten", "The type of summary": "Het type samenvatting", "Enable custom topics, either true or false": "Aangepaste topics inschakelen waar of onwaar", "The list of custom topics": "<PERSON>i<PERSON> met aangepaste onderwerpen", "Wait until the transcript status is \"completed\" or \"error\" before moving on to the next step.": "Wacht tot de transcript status \"voltooid\" of \"fout\" is voordat u verder gaat met de volgende stap.", "If the transcript status is \"error\", throw an error.": "Als de transcript status \"fout\" is, gooi dan een fout.", "The maximum number of characters per caption": "Het maximum aantal tekens per onderschrift", "The desired file name for storing in ActivePieces. Make sure the file extension is correct.": "De gewenste bestandsnaam voor het opslaan van ActivePieces. Zorg ervoor dat de bestandsextensie juist is.", "Keywords to search for": "Trefwoorden om naar te zoeken", "Maximum amount of transcripts to retrieve": "Maximale hoeveelheid van op te halen transcripten", "Filter by transcript status": "Filter op transcript status", "Only get transcripts created on this date": "Alleen transcripten ophalen die op deze datum zijn gemaakt", "Get transcripts that were created before this transcript ID": "Verkrijg transcripten die zijn aangemaakt voor deze transcript-ID", "Get transcripts that were created after this transcript ID": "Verkrijg transcripten die zijn aangemaakt na deze transcript-ID", "Only get throttled transcripts, overrides the status filter": "<PERSON>een geb<PERSON>en transcripten krijgen, overschrijft het status filter", "Your text to prompt the model to produce a desired output, including any context you want to pass into the model.": "<PERSON><PERSON><PERSON> tekst om het model aan te sporen een gewenste uitvoer te produceren, inclusief de context die je wilt doorgeven aan het model.", "A list of completed transcripts with text. Up to a maximum of 100 files or 100 hours, whichever is lower.\nUse either transcript_ids or input_text as input into LeMUR.\n": "<PERSON><PERSON> lij<PERSON> van voltooide transcripten met tekst. Maximaal 100 bestanden of 100 uur, wat ook lager is.\nGebruik transcript_ids of input_text als invoer in LeMUR.\n", "Custom formatted transcript data. Maximum size is the context limit of the selected model, which defaults to 100000.\nUse either transcript_ids or input_text as input into LeMUR.\n": "Aangepaste geformatteerde transcript gegevens. Maximale grootte is de context limiet van het geselecteerde model, standaard op 100000.\nGebruik transcript_ids of input_text als invoer in LeMUR.\n", "Context to provide the model. This can be a string or a free-form JSON value.": "Context om het model op te geven. Dit kan een tekenreeks of een vrije JSON-waarde zijn.", "The model that is used for the final prompt after compression is performed.\n": "Het model dat wordt gebruikt voor de laatste prompt na compressie is uitgevoerd.\n", "Max output size in tokens, up to 4000": "Maximale uitvoergrootte in tokens, tot 4000", "The temperature to use for the model.\nHigher values result in answers that are more creative, lower values are more conservative.\nCan be any value between 0.0 and 1.0 inclusive.\n": "De temperatuur die gebruikt moet worden voor het model.\nHogere waarden resulteren in antwoorden die creatiever en lagere waarden conservatiever zijn.\nKan elke waarde tussen 0,0 en 1.0 inclusief zijn.\n", "The ID of the LeMUR request whose data you want to delete. This would be found in the response of the original request.": "Het ID van het LeMUR verzoek wiens gegevens je wilt verwijderen. Dit wordt gevonden in het antwoord van het oorspronkelijke verzoek.", "Authorization headers are injected automatically from your connection.": "Autorisatie headers worden automatisch geïnjecteerd vanuit uw verbinding.", "English (Global)": "Engels (globaal)", "English (Australian)": "<PERSON><PERSON><PERSON> (Australian)", "English (British)": "<PERSON><PERSON><PERSON> (Engels)", "English (US)": "<PERSON><PERSON><PERSON> (VS)", "Spanish": "Spaans", "French": "<PERSON><PERSON>", "German": "<PERSON><PERSON>", "Italian": "Italiaans", "Portuguese": "Portugees", "Dutch": "Nederlands", "Afrikaans": "Afrikaanse", "Albanian": "Albanees", "Amharic": "Amharic", "Arabic": "Arabisch", "Armenian": "Armeens", "Assamese": "Assamees", "Azerbaijani": "Azerbeidzjaans", "Bashkir": "Bashkir", "Basque": "Baskisch", "Belarusian": "Wit-Russisch", "Bengali": "Bengaalse", "Bosnian": "Bosnisch", "Breton": "Breton", "Bulgarian": "Bulgaars", "Burmese": "Burmese", "Catalan": "Catala<PERSON>", "Chinese": "Chin<PERSON>", "Croatian": "<PERSON><PERSON><PERSON><PERSON>", "Czech": "T<PERSON>je<PERSON>sch", "Danish": "<PERSON><PERSON>", "Estonian": "Estlands", "Faroese": "Faroese", "Finnish": "Fins", "Galician": "<PERSON><PERSON><PERSON><PERSON>", "Georgian": "Georg<PERSON>", "Greek": "Grieks", "Gujarati": "Gujarati", "Haitian": "Haitian", "Hausa": "Hausa", "Hawaiian": "Hawaïaans", "Hebrew": "<PERSON><PERSON>eu<PERSON>", "Hindi": "<PERSON><PERSON><PERSON><PERSON>", "Hungarian": "Hongaars", "Icelandic": "Icelandic", "Indonesian": "Indonesisch", "Japanese": "Afrikaans", "Javanese": "Javanese", "Kannada": "Kannada", "Kazakh": "Kazakh", "Khmer": "Khmer", "Korean": "Koreaans", "Lao": "Lao", "Latin": "<PERSON><PERSON><PERSON><PERSON>", "Latvian": "Lets", "Lingala": "Lingala", "Lithuanian": "Litouws", "Luxembourgish": "Luxemburgs", "Macedonian": "<PERSON><PERSON>isch", "Malagasy": "Malagasy", "Malay": "<PERSON><PERSON><PERSON>", "Malayalam": "Maleisië", "Maltese": "Maltese", "Maori": "<PERSON><PERSON>", "Marathi": "Marathi", "Mongolian": "Mongools", "Nepali": "Nepali", "Norwegian": "Noors", "Norwegian Nynorsk": "Norwegian Nynorsk", "Occitan": "Occitan", "Panjabi": "Panjabi", "Pashto": "Pashto", "Persian": "<PERSON><PERSON><PERSON>", "Polish": "Pools", "Romanian": "<PERSON><PERSON><PERSON>", "Russian": "<PERSON><PERSON>", "Sanskrit": "Sanskrit", "Serbian": "<PERSON><PERSON><PERSON>", "Shona": "Telefoon", "Sindhi": "Sindhi", "Sinhala": "Singalees", "Slovak": "<PERSON><PERSON><PERSON>", "Slovenian": "Slovenian", "Somali": "Somali", "Sundanese": "Sundanese", "Swahili": "<PERSON><PERSON><PERSON>", "Swedish": "Zweeds", "Tagalog": "Tagalog", "Tajik": "Tadz<PERSON><PERSON><PERSON>", "Tamil": "Tamil", "Tatar": "Tatar", "Telugu": "Telugu", "Thai": "<PERSON><PERSON>", "Tibetan": "Tibetaans", "Turkish": "Turks", "Turkmen": "Turkmen", "Ukrainian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Urdu": "Urdu", "Uzbek": "Uzbek", "Vietnamese": "Vietnamese", "Welsh": "Welsh", "Yiddish": "<PERSON><PERSON><PERSON>", "Yoruba": "Yoruba", "Best": "Beste", "Nano": "<PERSON><PERSON>", "Low": "laag", "Default": "Standaard", "High": "hoog", "MP3": "MP3", "WAV": "WV", "Account Number": "<PERSON><PERSON>", "Banking Information": "Bankinformatie", "Blood Type": "Bloed type", "Credit Card CVV": "Creditcard CVV", "Credit Card Expiration": "Vervaldatum creditcard", "Credit Card Number": "Creditcard nummer", "Date": "Datum:", "Date Interval": "<PERSON><PERSON>", "Date of Birth": "Geboortedatum", "Driver's License": "<PERSON><PERSON>ie chauffeur", "Drug": "<PERSON><PERSON>", "Duration": "Tijdsduur", "Email Address": "Uw e-mailadres", "Event": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Filename": "Bestandsnaam", "Gender Sexuality": "Geslacht Seksualiteit", "Healthcare Number": "<PERSON><PERSON><PERSON> nummer", "Injury": "Verwondingen", "IP Address": "IP adres", "Language": "Taal", "Location": "Locatie", "Marital Status": "Basisstatus per huwelijk", "Medical Condition": "Medische Voorwaarde", "Medical Process": "Medisch proces", "Money Amount": "<PERSON>eld Bedrag", "Nationality": "Nationaliteit", "Number Sequence": "<PERSON><PERSON><PERSON> reeks", "Occupation": "Bezetting", "Organization": "Rekening", "Passport Number": "Paspoort Nummer", "Password": "Wachtwoord", "Person Age": "<PERSON><PERSON><PERSON>", "Person Name": "<PERSON><PERSON> persoon", "Phone Number": "Telefoon nummer", "Physical Attribute": "Fysieke kenmerk", "Political Affiliation": "Politieke Partner", "Religion": "Religie", "Statistics": "Statistieken", "Time": "Tijd", "URL": "URL", "US Social Security Number": "US Social Security Number", "Username": "Gebruikersnaam", "Vehicle ID": "Voertuig ID", "Zodiac Sign": "Zod<PERSON>", "Entity Name": "<PERSON><PERSON>", "Hash": "Toegangssleutel", "Informative": "Informatief", "Conversational": "Conversationeel", "Catchy": "<PERSON><PERSON>", "Bullets": "Opsommingstekens", "Bullets Verbose": "Uitgebreide kogels", "Gist": "Lijm", "Headline": "<PERSON><PERSON>", "Paragraph": "Paragraaf", "SRT": "SORT", "VTT": "VTT", "Queued": "<PERSON><PERSON><PERSON><PERSON>", "Processing": "Verwerken", "Completed": "Voltooid", "Error": "Foutmelding", "Claude 3.5 Sonnet (on Anthropic)": "Claude 3.5 <PERSON><PERSON> (op Anthropic)", "Claude 3 Opus (on Anthropic)": "Claude 3 Opus (over Anthropic)", "Claude 3 Haiku (on Anthropic)": "Claude 3 <PERSON><PERSON> (over Anthropic)", "Claude 3 Sonnet (on Anthropic)": "Claude 3 Sonnet (over Anthropic)", "Claude 2.1 (on Anthropic)": "<PERSON> 2.1 (over <PERSON><PERSON><PERSON>)", "Claude 2 (on Anthropic)": "<PERSON> 2 (over <PERSON><PERSON><PERSON>)", "Claude Instant 1.2 (on Anthropic)": "<PERSON> 1.2 (op Anthropic)", "Basic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Mistral 7B (Hosted by AssemblyAI)": "Mistral 7B (Gehost door AssemblyAI)", "GET": "KRIJG", "POST": "POSTE", "PATCH": "BEKIJK", "PUT": "PUT", "DELETE": "VERWIJDEREN", "HEAD": "HOOFD"}
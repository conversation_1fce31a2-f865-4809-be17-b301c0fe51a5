{"AssemblyAI": "AssemblyAI", "Transcribe and extract data from audio using AssemblyAI's Speech AI.": "AssemblyAIのSpeech AIを使用してオーディオからデータを書き換えて抽出します。", "You can retrieve your AssemblyAI API key within your AssemblyAI [Account Settings](https://www.assemblyai.com/app/account?utm_source=activepieces).": "AssemblyAI [Account Settings](https://www.assemblyai.com/app/account?utm_source=activepieces)からAssemblyAI APIキーを取得できます。", "Upload File": "ファイルをアップロード", "Transcribe": "変換する", "Get Transcript": "Get Transcript", "Get Transcript Sentences": "トランスクリプトの文章を取得", "Get Transcript Paragraphs": "トランスクリプトの段落の取得", "Get Transcript Subtitles": "トランスクリプトの字幕を取得", "Get Transcript Redacted Audio": "トランスクリプトで編集されたオーディオを取得", "Search words in transcript": "トランスクリプトで単語を検索", "List transcripts": "トランスクリプトの一覧", "Delete transcript": "トランスクリプトを削除", "Run a Task using LeMUR": "LeMURを使用してタスクを実行", "Retrieve LeMUR response": "LeMUR応答を取得", "Purge LeMUR request data": "LeMURリクエストデータを削除", "Custom API Call": "カスタムAPI通話", "Upload a media file to AssemblyAI's servers.": "AssemblyAIのサーバーにメディアファイルをアップロードします。", "Transcribe an audio or video file using AssemblyAI.": "AssemblyAI を使用してオーディオファイルまたはビデオファイルを転記します。", "Retrieves a transcript by its ID.": "IDからトランスクリプトを取得します。", "Retrieve the sentences of the transcript by its ID.": "IDで記録の文章を取得します。", "Retrieve the paragraphs of the transcript by its ID.": "記録の段落を ID で取得します。", "Export the transcript as SRT or VTT subtitles.": "トランスクリプトを SRT または VTT 字幕としてエクスポートします。", "Get the result of the redacted audio model.": "編集したオーディオモデルの結果を取得します。", "Search through the transcript for keywords. You can search for individual words, numbers, or phrases containing up to five words or numbers.": "キーワードを検索します。最大5つの単語または数字を含む個々の単語、数字、またはフレーズを検索できます。", "Retrieve a list of transcripts you created.\nTranscripts are sorted from newest to oldest. The previous URL always points to a page with older transcripts.": "作成したトランスクリプトのリストを取得します。\nトランスクリプトは最新から古いものにソートされます。 以前の URL は常に古いトランスクリプトのあるページを指しています。", "Remove the data from the transcript and mark it as deleted.": "トランスクリプトからデータを削除し、削除としてマークします。", "Use the LeMUR task endpoint to input your own LLM prompt.": "LeMUR タスク エンドポイントを使用して、独自の LLM プロンプトを入力します。", "Retrieve a LeMUR response that was previously generated.": "以前に生成された LeMUR 応答を取得します。", "Delete the data for a previously submitted LeMUR request.\nThe LLM response data, as well as any context provided in the original request will be removed.": "以前に送信された LeMUR 要求のデータを削除します。\nLLMレスポンスデータと、元のリクエストで提供されたコンテキストは削除されます。", "Make a custom API call to a specific endpoint": "特定のエンドポイントへのカスタム API コールを実行します。", "Audio File": "オーディオ ファイル", "Audio URL": "オーディオ URL", "Language Code": "言語コード", "Language Detection": "言語検出", "Language Confidence Threshold": "言語信頼度のしきい値", "Speech Model": "音声モデル", "Punctuate": "Punctuate", "Format Text": "テキストの書式設定", "Disfluencies": "<unk>", "Dual Channel": "デュアルチャンネル", "Webhook URL": "Webhook URL", "Webhook Auth Header Name": "Webhook認証ヘッダー名", "Webhook Auth Header Value": "Webhook認証ヘッダーの値", "Key Phrases": "キーフレーズ", "Audio Start From": "オーディオの開始元", "Audio End At": "オーディオ終了時刻", "Word Boost": "ワードブースト", "Word Boost Level": "単語ブーストレベル", "Filter Profanity": "不適切なフィルター", "Redact PII": "Redact PII", "Redact PII Audio": "Redact PII Audio", "Redact PII Audio Quality": "Redact PII オーディオ品質", "Redact PII Policies": "Redact PII Policies", "Redact PII Substitution": "Redact PII 置換", "Speaker Labels": "スピーカーラベル", "Speakers Expected": "予想されるスピーカー", "Content Moderation": "コンテンツモデレーション", "Content Moderation Confidence": "コンテンツモデレーションの信頼性", "Topic Detection": "トピックの検出", "Custom Spellings": "カスタムスペルチェック", "Sentiment Analysis": "センチメンタム分析", "Auto Chapters": "自動章", "Entity Detection": "エンティティの検出", "Speech Threshold": "音声のしきい値", "Enable Summarization": "概要を有効にする", "Summary Model": "概要モデル", "Summary Type": "サマリータイプ", "Enable Custom Topics": "カスタムトピックを有効にする", "Custom Topics": "カスタムトピック", "Wait until transcript is ready": "トランスクリプトの準備ができるまで待つ", "Throw if transcript status is error": "トランスクリプトの状態がエラーの場合はスローする", "Transcript ID": "トランスクリプトID", "Subtitles Format": "字幕フォーマット", "Number of Characters per Caption": "図表番号ごとの文字数", "Download file?": "ファイルのダウンロード?", "Download File Name": "ファイル名をダウンロード", "Words": "単語", "Limit": "制限", "Status": "ステータス", "Created On": "作成日時", "Before ID": "IDの前", "After ID": "IDの後", "Throttled Only": "スロットルのみ", "Prompt": "Prompt", "Transcript IDs": "トランスクリプトID", "Input Text": "Input Text", "Context": "コンテキスト", "Final Model": "最終モデル", "Maximum Output Size": "最大出力サイズ", "Temperature": "温度", "LeMUR request ID": "LeMURリクエストID", "Method": "方法", "Headers": "ヘッダー", "Query Parameters": "クエリパラメータ", "Body": "本文", "No Error on Failure": "失敗時にエラーはありません", "Timeout (in seconds)": "タイムアウト（秒）", "The File or URL of the audio or video file.": "オーディオまたはビデオファイルのファイルまたはURL。", "The URL of the audio or video file to transcribe.": "転記するオーディオファイルまたはビデオファイルのURL。", "The language of your audio file. Possible values are found in [Supported Languages](https://www.assemblyai.com/docs/concepts/supported-languages).\nThe default value is 'en_us'.\n": "オーディオファイルの言語。使用可能な値は [サポート言語](https://www.assemblyai.com/docs/concepts/supported-languages) にあります。\nデフォルト値は 'en_us' です。\n", "Enable [Automatic language detection](https://www.assemblyai.com/docs/models/speech-recognition#automatic-language-detection), either true or false.": "[自動言語検出](https://www.assemblyai.com/docs/models/speech-recognition#automatic-language-detection)をtrueまたはfalseにします。", "The confidence threshold for the automatically detected language.\nAn error will be returned if the language confidence is below this threshold.\nDefaults to 0.\n": "自動検出された言語の自信閾値。\n言語の自信がしきい値を下回った場合、エラーが返されます。\nデフォルトは0です。\n", "The speech model to use for the transcription. When `null`, the \"best\" model is used.": "転写に使用する音声モデル。`null`の場合、\"ベスト\"モデルが使用されます。", "Enable Automatic Punctuation, can be true or false": "自動句読点を有効にします。trueまたはfalseにすることができます", "Enable Text Formatting, can be true or false": "テキスト書式を有効にします。trueまたはfalseにすることができます。", "Transcribe Filler Words, like \"umm\", in your media file; can be true or false": "メディアファイルに「umm」のようなフィラーを転記します。trueまたはfalseにすることができます。", "Enable [Dual Channel](https://www.assemblyai.com/docs/models/speech-recognition#dual-channel-transcription) transcription, can be true or false.": "[Dual Channel](https://www.assemblyai.com/docs/models/speech-recognition#dual-channel-transcription) を有効にすると、true または false にすることができます。", "The URL to which we send webhook requests.\nWe sends two different types of webhook requests.\nOne request when a transcript is completed or failed, and one request when the redacted audio is ready if redact_pii_audio is enabled.\n": "The URL to which we send webhook requests.\nWe sends two different types of webhook requests.\nOne request when a transcript is completed or failed, and one request when the redacted audio is ready if redact_pii_audio is enabled.\n", "The header name to be sent with the transcript completed or failed webhook requests": "Webhookリクエストが完了または失敗した場合に送信されるヘッダー名", "The header value to send back with the transcript completed or failed webhook requests for added security": "セキュリティ追加のWebフックリクエストが完了または失敗した場合に送信するヘッダーの値", "Enable Key Phrases, either true or false": "キーフレーズを有効にする", "The point in time, in milliseconds, to begin transcribing in your media file": "メディアファイルの転記を開始する時間 (ミリ秒単位)", "The point in time, in milliseconds, to stop transcribing in your media file": "メディアファイルの転記を停止する時間 (ミリ秒単位)", "The list of custom vocabulary to boost transcription probability for": "転写確率を向上させるためのカスタム語彙リスト", "How much to boost specified words": "指定した単語をブーストする金額", "Filter profanity from the transcribed text, can be true or false": "書き換えられたテキストからの俗語をフィルタリングします。trueまたはfalseにすることができます。", "Redact PII from the transcribed text using the Redact PII model, can be true or false": "Redact PIIモデルを使用して書き換えられたテキストからのRedact PIIはtrueまたはfalseにすることができます。", "Generate a copy of the original media file with spoken PII \"beeped\" out, can be true or false. See [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) for more details.": "\"beeped\"と話されたPIIを持つ元のメディアファイルのコピーを生成し、trueまたはfalseにすることができます。 詳細は [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) を参照してください。", "Controls the filetype of the audio created by redact_pii_audio. Currently supports mp3 (default) and wav. See [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) for more details.": "redact_pii_audioによって作成されたオーディオのファイルタイプを制御します。現在 mp3 (デフォルト) と wave をサポートしています。詳細は [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) を参照してください。", "The list of PII Redaction policies to enable. See [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) for more details.": "有効にする PII Redaction ポリシーの一覧です。詳細は [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) を参照してください。", "The replacement logic for detected PII, can be \"entity_type\" or \"hash\". See [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) for more details.": "検出された PII の置換ロジックは \"entity_type\" か \"hash\" になります。詳細は [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) を参照してください。", "Enable [Speaker diarization](https://www.assemblyai.com/docs/models/speaker-diarization), can be true or false": "[Speaker diarization](https://www.assemblyai.com/docs/models/speaker-diarization)を有効にします。trueまたはfalseにすることができます", "Tells the speaker label model how many speakers it should attempt to identify, up to 10. See [Speaker diarization](https://www.assemblyai.com/docs/models/speaker-diarization) for more details.": "Tells the speaker label model how many speakers it should attempt to identify, up to 10. See [Speaker diarization](https://www.assemblyai.com/docs/models/speaker-diarization) for more details.", "Enable [Content Moderation](https://www.assemblyai.com/docs/models/content-moderation), can be true or false": "[Content Moderation](https://www.assemblyai.com/docs/models/content-moderation)を有効にします。trueまたはfalseにすることができます", "The confidence threshold for the Content Moderation model. Values must be between 25 and 100.": "コンテンツモデレーションモデルの信頼度しきい値。値は25から100の間でなければなりません。", "Enable [Topic Detection](https://www.assemblyai.com/docs/models/topic-detection), can be true or false": "[トピック検出](https://www.assemblyai.com/docs/models/topic-detection)を有効にします。trueまたはfalseにできます", "Customize how words are spelled and formatted using to and from values.\nUse a JSON array of objects of the following format:\n```\n[\n  {\n    \"from\": [\"original\", \"spelling\"],\n    \"to\": \"corrected\"\n  }\n]\n```\n": "Customize how words are spelled and formatted using to and from values.\nUse a JSON array of objects of the following format:\n```\n[\n  {\n    \"from\": [\"original\", \"spelling\"],\n    \"to\": \"corrected\"\n  }\n]\n```\n", "Enable [Sentiment Analysis](https://www.assemblyai.com/docs/models/sentiment-analysis), can be true or false": "[センチメンタム分析]（https://www.assemblyai.com/docs/models/sentiment-analysis）を有効にします。trueまたはfalseにすることができます", "Enable [Auto Chapters](https://www.assemblyai.com/docs/models/auto-chapters), can be true or false": "[Auto Chapters](https://www.assemblyai.com/docs/models/auto-chapters) を有効にします。true または false にすることができます。", "Enable [Entity Detection](https://www.assemblyai.com/docs/models/entity-detection), can be true or false": "[Entity Detection](https://www.assemblyai.com/docs/models/entity-detection)を有効にします。trueまたはfalseにすることができます", "Reject audio files that contain less than this fraction of speech.\nValid values are in the range [0, 1] inclusive.\n": "スピーチの分数未満のオーディオファイルを拒否します。\n有効な値は範囲[0, 1]を含みます。\n", "Enable [Summarization](https://www.assemblyai.com/docs/models/summarization), can be true or false": "[Summarization]を有効にする (https://www.assemblyai.com/docs/models/summarization), true または false にすることができます", "The model to summarize the transcript": "写本をまとめたモデル", "The type of summary": "概要の種類", "Enable custom topics, either true or false": "カスタムトピックを有効にする", "The list of custom topics": "カスタムトピックのリスト", "Wait until the transcript status is \"completed\" or \"error\" before moving on to the next step.": "次のステップに進む前に、トランスクリプトの状態が「完了」または「エラー」になるまで待ちます。", "If the transcript status is \"error\", throw an error.": "トランスクリプトの状態が \"error\" の場合は、エラーをスローします。", "The maximum number of characters per caption": "キャプションあたりの最大文字数", "The desired file name for storing in ActivePieces. Make sure the file extension is correct.": "ActivePiecesに保存するためのファイル名です。ファイル拡張子が正しいことを確認してください。", "Keywords to search for": "検索するキーワード", "Maximum amount of transcripts to retrieve": "取得するトランスクリプトの最大量", "Filter by transcript status": "トランスクリプトの状態でフィルター", "Only get transcripts created on this date": "この日付に作成されたトランスクリプトのみ取得", "Get transcripts that were created before this transcript ID": "このトランスクリプトIDの前に作成されたトランスクリプトを取得", "Get transcripts that were created after this transcript ID": "このトランスクリプトIDの後に作成されたトランスクリプトを取得", "Only get throttled transcripts, overrides the status filter": "ステータスフィルタをオーバーライドし、スロットル化されたトランスクリプトのみ取得", "Your text to prompt the model to produce a desired output, including any context you want to pass into the model.": "モデルに渡したいコンテキストを含め、望ましい出力を生成するように求めるテキストを入力します。", "A list of completed transcripts with text. Up to a maximum of 100 files or 100 hours, whichever is lower.\nUse either transcript_ids or input_text as input into LeMUR.\n": "テキスト付きの完了したトランスクリプトのリスト。最大100ファイルまたは100時間のいずれか低い方まで。\nLeMURの入力としてtranscript_idsまたはinput_textのいずれかを使用します。\n", "Custom formatted transcript data. Maximum size is the context limit of the selected model, which defaults to 100000.\nUse either transcript_ids or input_text as input into LeMUR.\n": "カスタムフォーマットされたトランスクリプトデータ。最大サイズは選択したモデルのコンテキスト制限です。デフォルトは100000です。\nLeMURの入力としてtranscript_idsまたはinput_textのいずれかを使用します。\n", "Context to provide the model. This can be a string or a free-form JSON value.": "モデルを提供するコンテキスト。これは文字列または自由形式の JSON 値にすることができます。", "The model that is used for the final prompt after compression is performed.\n": "圧縮後の最終プロンプトに使用されるモデル。\n", "Max output size in tokens, up to 4000": "トークンの最大出力サイズ（最大4000）", "The temperature to use for the model.\nHigher values result in answers that are more creative, lower values are more conservative.\nCan be any value between 0.0 and 1.0 inclusive.\n": "The temperature to use for the model.\nHigher values result in answers that are more creative, lower values are more conservative.\nCan be any value between 0.0 and 1.0 inclusive.\n", "The ID of the LeMUR request whose data you want to delete. This would be found in the response of the original request.": "データを削除したいLeMURリクエストのID。元のリクエストの応答で確認できます。", "Authorization headers are injected automatically from your connection.": "認証ヘッダは接続から自動的に注入されます。", "English (Global)": "英語（世界）", "English (Australian)": "英語 (オーストラリア語)", "English (British)": "英語 (イギリス)", "English (US)": "英語 (米国)", "Spanish": "スペイン語", "French": "フランス語", "German": "ドイツ語", "Italian": "イタリア語", "Portuguese": "ポルトガル語", "Dutch": "オランダ語", "Afrikaans": "アフリカーンス語", "Albanian": "アルバニア語", "Amharic": "Amharic", "Arabic": "アラビア文字", "Armenian": "アルメニア語", "Assamese": "アッサム語", "Azerbaijani": "アゼルバイジャン語", "Bashkir": "Bashkir", "Basque": "バスク語", "Belarusian": "ベラルーシ語", "Bengali": "ベンガル語", "Bosnian": "ボスニア語", "Breton": "Breton", "Bulgarian": "ブルガリア語", "Burmese": "Burmese", "Catalan": "カタロニア語", "Chinese": "中国語", "Croatian": "クロアチア語", "Czech": "チェコ語", "Danish": "デンマーク語", "Estonian": "エストニア語", "Faroese": "Faroese", "Finnish": "フィンランド語", "Galician": "ガリシア語", "Georgian": "グルジア語", "Greek": "ギリシア語", "Gujarati": "Gujarati", "Haitian": "Haitian", "Hausa": "Hausa", "Hawaiian": "ハワイ語", "Hebrew": "ヘブライ文字", "Hindi": "ヒンディー語", "Hungarian": "ハンガリー語", "Icelandic": "Icelandic", "Indonesian": "インドネシア語", "Japanese": "日本語", "Javanese": "Javanese", "Kannada": "Kannada", "Kazakh": "Kazakh", "Khmer": "Khmer", "Korean": "Korean", "Lao": "Lao", "Latin": "Latin", "Latvian": "ラトビア語", "Lingala": "Lingala", "Lithuanian": "リトアニア語", "Luxembourgish": "ルクセンブルク語", "Macedonian": "マケドニア語", "Malagasy": "Malagasy", "Malay": "マレー語", "Malayalam": "マラヤラム語", "Maltese": "Maltese", "Maori": "<PERSON><PERSON>", "Marathi": "Marathi", "Mongolian": "モンゴル語", "Nepali": "Nepali", "Norwegian": "ノルウェー語", "Norwegian Nynorsk": "Norwegian Nynorsk", "Occitan": "Occitan", "Panjabi": "Panjabi", "Pashto": "Pashto", "Persian": "ペルシャ語", "Polish": "ポーランド語", "Romanian": "ルーマニア語", "Russian": "ロシア語", "Sanskrit": "Sanskrit", "Serbian": "セルビア語", "Shona": "庄名（しょうな）", "Sindhi": "Sindhi", "Sinhala": "シンハラ語", "Slovak": "スロバキア語", "Slovenian": "Slovenian", "Somali": "Somali", "Sundanese": "Sundanese", "Swahili": "スワヒリ語", "Swedish": "スウェーデン語", "Tagalog": "Tagalog", "Tajik": "タジク語", "Tamil": "Tamil", "Tatar": "Tatar", "Telugu": "Telugu", "Thai": "タイ語", "Tibetan": "チベット語", "Turkish": "トルコ語", "Turkmen": "Turkmen", "Ukrainian": "ウクライナ語", "Urdu": "ウルドゥー語", "Uzbek": "Uzbek", "Vietnamese": "Vietnamese", "Welsh": "ウェールズ語", "Yiddish": "イディッシュ", "Yoruba": "Yoruba", "Best": "ベスト", "Nano": "<PERSON><PERSON>", "Low": "低い", "Default": "デフォルト", "High": "高い", "MP3": "MP3", "WAV": "WAV", "Account Number": "口座番号", "Banking Information": "銀行口座情報", "Blood Type": "血液タイプ", "Credit Card CVV": "クレジットカードCVV", "Credit Card Expiration": "クレジットカードの有効期限", "Credit Card Number": "クレジットカード番号", "Date": "日付", "Date Interval": "日付間隔", "Date of Birth": "誕生日", "Driver's License": "ドライバーライセンス", "Drug": "薬物format@@0", "Duration": "期間", "Email Address": "メールアドレス", "Event": "イベント", "Filename": "ファイル名", "Gender Sexuality": "性別性", "Healthcare Number": "医療番号", "Injury": "傷害者", "IP Address": "IP アドレス", "Language": "言語", "Location": "場所", "Marital Status": "婚姻状況", "Medical Condition": "<unk>", "Medical Process": "医療プロセス", "Money Amount": "金額", "Nationality": "国籍：", "Number Sequence": "シーケンス番号", "Occupation": "Ocupação", "Organization": "組織", "Passport Number": "パスポート番号", "Password": "パスワード", "Person Age": "年齢:", "Person Name": "担当者名", "Phone Number": "電話番号", "Physical Attribute": "物理属性", "Political Affiliation": "政治的関連", "Religion": "宗教", "Statistics": "統計情報", "Time": "時刻", "URL": "URL", "US Social Security Number": "米国の社会保障番号", "Username": "ユーザー名", "Vehicle ID": "車両ID", "Zodiac Sign": "星座記号", "Entity Name": "エンティティ名", "Hash": "ハッシュ", "Informative": "参考情報", "Conversational": "会話", "Catchy": "キャッチーな", "Bullets": "箇条書き:", "Bullets Verbose": "箇条書きの詳細設定", "Gist": "Gist", "Headline": "見出し", "Paragraph": "段落", "SRT": "SRT", "VTT": "VTT", "Queued": "キューに入りました", "Processing": "処理中", "Completed": "完了", "Error": "エラー", "Claude 3.5 Sonnet (on Anthropic)": "<PERSON> 3.5 <PERSON><PERSON> (Anthropic)", "Claude 3 Opus (on Anthropic)": "クロード3 Opus (アンスロピック上)", "Claude 3 Haiku (on Anthropic)": "クロード3俳句（アンスロピック）", "Claude 3 Sonnet (on Anthropic)": "クロード3ソネット（アンスロピック）", "Claude 2.1 (on Anthropic)": "クロード2.1（アンスロピック上）", "Claude 2 (on Anthropic)": "クロード2（アンスロピック上）", "Claude Instant 1.2 (on Anthropic)": "<PERSON> 1.2 (<PERSON><PERSON><PERSON>)", "Basic": "基本", "Mistral 7B (Hosted by AssemblyAI)": "ミストラル7B（AssemblyAI主催）", "GET": "取得", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "削除", "HEAD": "頭"}
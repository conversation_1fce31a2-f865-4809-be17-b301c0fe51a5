{"AssemblyAI": "AssemblyAI", "Transcribe and extract data from audio using AssemblyAI's Speech AI.": "Transcrire et extraire des données audio à l'aide de l'IO vocal d'AssemblyAI.", "You can retrieve your AssemblyAI API key within your AssemblyAI [Account Settings](https://www.assemblyai.com/app/account?utm_source=activepieces).": "Vous pouvez récupérer votre clé API AssemblyAI dans votre AssemblyAI [Paramètres du compte](https://www.assemblyai.com/app/account?utm_source=activepieces).", "Upload File": "Charger un fichier", "Transcribe": "Transcrire", "Get Transcript": "Get Transcript", "Get Transcript Sentences": "Récupérer les phrases de transcription", "Get Transcript Paragraphs": "Récupérer les paragraphes de transcription", "Get Transcript Subtitles": "Obt<PERSON>r les sous-titres de transcription", "Get Transcript Redacted Audio": "Récupérer l'audio de la transcription", "Search words in transcript": "Rechercher des mots dans la transcription", "List transcripts": "Liste des transcriptions", "Delete transcript": "Supprimer la transcription", "Run a Task using LeMUR": "Exécuter une tâche en utilisant LeMUR", "Retrieve LeMUR response": "Récupérer la réponse LeMUR", "Purge LeMUR request data": "Purger les données de requête LeMUR", "Custom API Call": "Appel API personnalisé", "Upload a media file to AssemblyAI's servers.": "Télécharger un fichier média sur les serveurs d'AssemblyAI.", "Transcribe an audio or video file using AssemblyAI.": "Transcrivez un fichier audio ou vidéo en utilisant AssemblyAI.", "Retrieves a transcript by its ID.": "Récupère une transcription par son ID.", "Retrieve the sentences of the transcript by its ID.": "Récupère les phrases de la transcription par son ID.", "Retrieve the paragraphs of the transcript by its ID.": "Récupère les paragraphes de la transcription par son ID.", "Export the transcript as SRT or VTT subtitles.": "Exporter la transcription sous forme de sous-titres SRT ou VTT.", "Get the result of the redacted audio model.": "Obtenir le résultat du modèle audio redistribué.", "Search through the transcript for keywords. You can search for individual words, numbers, or phrases containing up to five words or numbers.": "Recherchez dans la transcription des mots clés. V<PERSON> pouvez rechercher des mots, des chiffres ou des phrases contenant jusqu'à cinq mots ou chiffres.", "Retrieve a list of transcripts you created.\nTranscripts are sorted from newest to oldest. The previous URL always points to a page with older transcripts.": "Récupérer une liste de transcriptions que vous avez créées.\nLes transcriptions sont triées du plus récent au plus ancien. L'URL précédente pointe toujours vers une page avec des transcriptions plus anciennes.", "Remove the data from the transcript and mark it as deleted.": "Supprimer les données de la transcription et marquer comme supprimées.", "Use the LeMUR task endpoint to input your own LLM prompt.": "Utilisez le point de terminaison LeMUR pour entrer votre propre invite LLM.", "Retrieve a LeMUR response that was previously generated.": "Récupère une réponse LeMUR qui a été générée précédemment.", "Delete the data for a previously submitted LeMUR request.\nThe LLM response data, as well as any context provided in the original request will be removed.": "Delete the data for a previously submitted LeMUR request.\nThe LLM response data, as well as any context provided in the original request will be removed.", "Make a custom API call to a specific endpoint": "Passez un appel API personnalisé à un point de terminaison spécifique", "Audio File": "Fichier audio", "Audio URL": "URL audio", "Language Code": "Code de la langue", "Language Detection": "Détection de langue", "Language Confidence Threshold": "Seuil de confiance dans la langue", "Speech Model": "<PERSON><PERSON><PERSON><PERSON> vocal", "Punctuate": "Punctuate", "Format Text": "Format du texte", "Disfluencies": "Influences", "Dual Channel": "Double canal", "Webhook URL": "URL du Webhook", "Webhook Auth Header Name": "Nom de l'en-tête d'authentification Webhook", "Webhook Auth Header Value": "Valeur de l'en-tête d'authentification Webhook", "Key Phrases": "Phrases clés", "Audio Start From": "Démarrage audio à partir de", "Audio End At": "Fin de l'audio à", "Word Boost": "Boost de mot", "Word Boost Level": "Niveau de Boost de mot", "Filter Profanity": "Profanation du filtre", "Redact PII": "Redact PII", "Redact PII Audio": "Redact PII Audio", "Redact PII Audio Quality": "Redéfinir la qualité audio PII", "Redact PII Policies": "Redact PII Policies", "Redact PII Substitution": "Substitution PII Redact", "Speaker Labels": "Étiquettes de haut-parleurs", "Speakers Expected": "Haut-parleurs attendus", "Content Moderation": "Modération du contenu", "Content Moderation Confidence": "Confiance de la modération de contenu", "Topic Detection": "Détection du sujet", "Custom Spellings": "Orthographes personnalisés", "Sentiment Analysis": "<PERSON><PERSON><PERSON> de la sensibilité", "Auto Chapters": "Chapitres automatiques", "Entity Detection": "Détection d'Entité", "Speech Threshold": "<PERSON><PERSON>", "Enable Summarization": "<PERSON><PERSON> le rés<PERSON>", "Summary Model": "<PERSON><PERSON><PERSON><PERSON>", "Summary Type": "Type de résumé", "Enable Custom Topics": "Activer les sujets personnalisés", "Custom Topics": "Sujets personnalisés", "Wait until transcript is ready": "Attendre que la transcription soit prête", "Throw if transcript status is error": "Lancer si l'état de la transcription est une erreur", "Transcript ID": "ID de la transcription", "Subtitles Format": "Format des sous-titres", "Number of Characters per Caption": "Nombre de caractères par légende", "Download file?": "Télécharger le fichier ?", "Download File Name": "Télécharger le nom du fichier", "Words": "<PERSON><PERSON>", "Limit": "Limite", "Status": "Statut", "Created On": "<PERSON><PERSON><PERSON>", "Before ID": "Avant l'ID", "After ID": "Après l'ID", "Throttled Only": "Combustible uniquement", "Prompt": "Prompt", "Transcript IDs": "ID de transcription", "Input Text": "Input Text", "Context": "Contexte", "Final Model": "Modèle final", "Maximum Output Size": "<PERSON>lle de sortie maximale", "Temperature": "Température", "LeMUR request ID": "Identifiant de requête LeMUR", "Method": "Méthode", "Headers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Query Parameters": "Paramètres de requête", "Body": "Corps", "No Error on Failure": "Aucune erreur en cas d'échec", "Timeout (in seconds)": "<PERSON><PERSON><PERSON>atten<PERSON> (en secondes)", "The File or URL of the audio or video file.": "Le fichier ou l'URL du fichier audio ou vidéo.", "The URL of the audio or video file to transcribe.": "L'URL du fichier audio ou vidéo à transcrire.", "The language of your audio file. Possible values are found in [Supported Languages](https://www.assemblyai.com/docs/concepts/supported-languages).\nThe default value is 'en_us'.\n": "La langue de votre fichier audio. Les valeurs possibles sont trouvées dans [Langues Supportées](https://www.assemblyai.com/docs/concepts/supported-languages).\nLa valeur par défaut est 'en_us'.\n", "Enable [Automatic language detection](https://www.assemblyai.com/docs/models/speech-recognition#automatic-language-detection), either true or false.": "Activer [Détection automatique de la langue](https://www.assemblyai.com/docs/models/speech-recognition#automatic-language-detection), vrai ou faux.", "The confidence threshold for the automatically detected language.\nAn error will be returned if the language confidence is below this threshold.\nDefaults to 0.\n": "Le seuil de confiance pour la langue automatiquement détectée.\nUne erreur sera retournée si la confiance du langage est inférieure à ce seuil.\nPar défaut à 0.\n", "The speech model to use for the transcription. When `null`, the \"best\" model is used.": "Le modèle de parole à utiliser pour la transcription. Lorsque `null`, le modèle \"meilleur\" est utilisé.", "Enable Automatic Punctuation, can be true or false": "Activer la ponctuation automatique, peut être vrai ou faux", "Enable Text Formatting, can be true or false": "Activer le formatage du texte, peut être vrai ou faux", "Transcribe Filler Words, like \"umm\", in your media file; can be true or false": "Transcrire des mots de remplissage, comme « umm», dans votre fichier multimédia; peut être vrai ou faux", "Enable [Dual Channel](https://www.assemblyai.com/docs/models/speech-recognition#dual-channel-transcription) transcription, can be true or false.": "Activer la transcription [Dual Channel](https://www.assemblyai.com/docs/models/speech-recognition#dual-channel-transcription), peut être vrai ou faux.", "The URL to which we send webhook requests.\nWe sends two different types of webhook requests.\nOne request when a transcript is completed or failed, and one request when the redacted audio is ready if redact_pii_audio is enabled.\n": "L'URL vers laquelle nous envoyons des demandes de webhook.\nNous envoyons deux types différents de requêtes de webhook.\nUne requête lorsqu'une transcription est terminée ou échouée, et une requête lorsque l'audio reproduit est prête si redact_pii_audio est activé.\n", "The header name to be sent with the transcript completed or failed webhook requests": "Le nom de l'en-tête à envoyer avec la transcription complétée ou les requêtes échouées de webhook", "The header value to send back with the transcript completed or failed webhook requests for added security": "La valeur de l'en-tête à renvoyer avec la transcription complétée ou les requêtes échouées de webhook pour plus de sécurité", "Enable Key Phrases, either true or false": "Activer les phrases clés, vrai ou faux", "The point in time, in milliseconds, to begin transcribing in your media file": "Le point dans le temps, en millisecondes, pour commencer à transcrire dans votre fichier média", "The point in time, in milliseconds, to stop transcribing in your media file": "Le point dans le temps, en millisecondes, d'arrêter de transcrire dans votre fichier média", "The list of custom vocabulary to boost transcription probability for": "La liste du vocabulaire personnalisé pour augmenter la probabilité de transcription pour", "How much to boost specified words": "Combien pour booster les mots spécifiés", "Filter profanity from the transcribed text, can be true or false": "Filtrer la profanité du texte transcrit, peut être vrai ou faux", "Redact PII from the transcribed text using the Redact PII model, can be true or false": "Redact PII à partir du texte transcrit en utilisant le modèle PII Redact, peut être vrai ou faux", "Generate a copy of the original media file with spoken PII \"beeped\" out, can be true or false. See [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) for more details.": "Générer une copie du fichier multimédia original avec des IPI parlés, peut être vrai ou faux. Voir [Redaction PII](https://www.assemblyai.com/docs/models/pii-redaction) pour plus de détails.", "Controls the filetype of the audio created by redact_pii_audio. Currently supports mp3 (default) and wav. See [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) for more details.": "Contrôle le type de fichier de l'audio créé par redact_pii_audio. Actuellement supporte les mp3 (par défaut) et les ondes. Voir [Redaction PII](https://www.assemblyai.com/docs/models/pii-redaction) pour plus de détails.", "The list of PII Redaction policies to enable. See [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) for more details.": "La liste des politiques de Redaction PII à activer. Voir [Redaction PII](https://www.assemblyai.com/docs/models/pii-redaction) pour plus de détails.", "The replacement logic for detected PII, can be \"entity_type\" or \"hash\". See [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) for more details.": "La logique de remplacement pour PII détecté peut être \"entity_type\" ou \"hash\". Voir [Redaction PII](https://www.assemblyai.com/docs/models/pii-redaction) pour plus de détails.", "Enable [Speaker diarization](https://www.assemblyai.com/docs/models/speaker-diarization), can be true or false": "Activer la [diarification du haut-parleur] (https://www.assemblyai.com/docs/models/speaker-diarization), peut être vrai ou faux", "Tells the speaker label model how many speakers it should attempt to identify, up to 10. See [Speaker diarization](https://www.assemblyai.com/docs/models/speaker-diarization) for more details.": "Indique au modèle d'étiquette du haut-parleur combien d'enceintes il doit essayer d'identifier, jusqu'à 10. Voir [Diarization du haut-parleur](https://www.assemblyai.com/docs/models/speaker-diarization) pour plus de détails.", "Enable [Content Moderation](https://www.assemblyai.com/docs/models/content-moderation), can be true or false": "Activer [Modération de contenu](https://www.assemblyai.com/docs/models/content-moderation), peut être vrai ou faux", "The confidence threshold for the Content Moderation model. Values must be between 25 and 100.": "Le seuil de confiance du modèle de modération de contenu doit être compris entre 25 et 100.", "Enable [Topic Detection](https://www.assemblyai.com/docs/models/topic-detection), can be true or false": "Activer la [Détection du sujet](https://www.assemblyai.com/docs/models/topic-detection), peut être vrai ou faux", "Customize how words are spelled and formatted using to and from values.\nUse a JSON array of objects of the following format:\n```\n[\n  {\n    \"from\": [\"original\", \"spelling\"],\n    \"to\": \"corrected\"\n  }\n]\n```\n": "Personnaliser la façon dont les mots sont orthographiés et formatés en utilisant et à partir des valeurs.\nUtiliser une table JSON d'objets au format suivant :\n```\n[\n  {\n    \"from\": [\"original\", \"orthographe\"],\n    \"à\": \"corrigé\"\n  }\n]\n```\n", "Enable [Sentiment Analysis](https://www.assemblyai.com/docs/models/sentiment-analysis), can be true or false": "Activer [Analy<PERSON> de sens] (https://www.assemblyai.com/docs/models/sentiment-analysis), peut être vrai ou faux", "Enable [Auto Chapters](https://www.assemblyai.com/docs/models/auto-chapters), can be true or false": "Activer [Chapitres Automatiques](https://www.assemblyai.com/docs/models/auto-chapters), peut être vrai ou faux", "Enable [Entity Detection](https://www.assemblyai.com/docs/models/entity-detection), can be true or false": "Activer [Détection d'Entité](https://www.assemblyai.com/docs/models/entity-detection), peut être vrai ou faux", "Reject audio files that contain less than this fraction of speech.\nValid values are in the range [0, 1] inclusive.\n": "Rejeter les fichiers audio qui contiennent moins de cette fraction de discours.\nLes valeurs valides sont dans l'intervalle [0, 1] inclusivement.\n", "Enable [Summarization](https://www.assemblyai.com/docs/models/summarization), can be true or false": "Activer [Summarization](https://www.assemblyai.com/docs/models/summarization), peut être vrai ou faux", "The model to summarize the transcript": "Le modèle pour résumer la transcription", "The type of summary": "Le type de résumé", "Enable custom topics, either true or false": "Activer les sujets personnalisés, que ce soit vrai ou faux", "The list of custom topics": "La liste des sujets personnalisés", "Wait until the transcript status is \"completed\" or \"error\" before moving on to the next step.": "Attendez que le statut de la transcription soit \"completed\" ou \"error\" avant de passer à l'étape suivante.", "If the transcript status is \"error\", throw an error.": "Si l'état de la transcription est \"error\", lancer une erreur.", "The maximum number of characters per caption": "Le nombre maximum de caractères par légende", "The desired file name for storing in ActivePieces. Make sure the file extension is correct.": "Le nom de fichier souhaité pour le stockage dans ActivePieces. Assurez-vous que l'extension de fichier est correcte.", "Keywords to search for": "Mots-clés à rechercher", "Maximum amount of transcripts to retrieve": "Nombre maximum de transcriptions à récupérer", "Filter by transcript status": "Filtrer par statut de transcription", "Only get transcripts created on this date": "N'obtenir que les transcriptions créées à cette date", "Get transcripts that were created before this transcript ID": "Récupérer les transcriptions qui ont été créées avant cet ID de transcription", "Get transcripts that were created after this transcript ID": "Récupérer les transcriptions qui ont été créées après cet ID de transcription", "Only get throttled transcripts, overrides the status filter": "N'obtenir que des transcriptions limitées, remplace le filtre de statut", "Your text to prompt the model to produce a desired output, including any context you want to pass into the model.": "Votre texte pour inviter le modèle à produire une sortie souhaitée, y compris tout contexte que vous voulez passer dans le modèle.", "A list of completed transcripts with text. Up to a maximum of 100 files or 100 hours, whichever is lower.\nUse either transcript_ids or input_text as input into LeMUR.\n": "Une liste de transcriptions remplies avec du texte. Jusqu'à un maximum de 100 fichiers ou 100 heures, selon la moindre des cas.\nUtilisez soit transcript_ids soit input_text comme entrée dans LeMUR.\n", "Custom formatted transcript data. Maximum size is the context limit of the selected model, which defaults to 100000.\nUse either transcript_ids or input_text as input into LeMUR.\n": "Données de transcription personnalisées formatées. La taille maximale est la limite de contexte du modèle sélectionné, qui est par défaut 100000.\nUtilisez soit transcript_ids soit input_text comme entrée dans LeMUR.\n", "Context to provide the model. This can be a string or a free-form JSON value.": "Contexte pour fournir le modèle. <PERSON>la peut être une chaîne de caractères ou une valeur JSON libre.", "The model that is used for the final prompt after compression is performed.\n": "Le modèle qui est utilisé pour l'invite finale après la compression est effectuée.\n", "Max output size in tokens, up to 4000": "Taille maximale de sortie en jetons, jusqu'à 4000", "The temperature to use for the model.\nHigher values result in answers that are more creative, lower values are more conservative.\nCan be any value between 0.0 and 1.0 inclusive.\n": "La température à utiliser pour le modèle.\nDes valeurs plus élevées donnent des réponses plus créatives, des valeurs plus faibles sont plus conservatrices.\nPeut être n'importe quelle valeur comprise entre 0.0 et 1.0 inclusivement.\n", "The ID of the LeMUR request whose data you want to delete. This would be found in the response of the original request.": "L'ID de la requête LeMUR dont vous voulez supprimer les données. Cela se trouve dans la réponse de la requête originale.", "Authorization headers are injected automatically from your connection.": "Les en-têtes d'autorisation sont injectés automatiquement à partir de votre connexion.", "English (Global)": "<PERSON><PERSON><PERSON> (Global)", "English (Australian)": "<PERSON><PERSON><PERSON> (Australien)", "English (British)": "<PERSON><PERSON><PERSON> (britannique)", "English (US)": "<PERSON><PERSON><PERSON> (US)", "Spanish": "Espagnol", "French": "Français", "German": "Allemand", "Italian": "Italien", "Portuguese": "Portugais", "Dutch": "Néerlandais", "Afrikaans": "afrikaans", "Albanian": "Albanais", "Amharic": "Amharic", "Arabic": "<PERSON><PERSON>", "Armenian": "<PERSON><PERSON><PERSON>", "Assamese": "Assamais", "Azerbaijani": "Azerbaïdjan", "Bashkir": "Bashkir", "Basque": "Basque", "Belarusian": "Biélorusse", "Bengali": "Bengalais", "Bosnian": "Bosniaque", "Breton": "Breton", "Bulgarian": "Bulgare", "Burmese": "Burmese", "Catalan": "Catalan", "Chinese": "<PERSON><PERSON>", "Croatian": "Croate", "Czech": "Tchèque", "Danish": "<PERSON><PERSON>", "Estonian": "estonien", "Faroese": "Faroese", "Finnish": "Finlandais", "Galician": "Galicien", "Georgian": "Géorgien", "Greek": "Grecque", "Gujarati": "Gujarati", "Haitian": "Haitian", "Hausa": "Hausa", "Hawaiian": "Hawaï", "Hebrew": "<PERSON><PERSON><PERSON><PERSON>", "Hindi": "Hindi", "Hungarian": "Hongrois", "Icelandic": "Icelandic", "Indonesian": "Indonésien", "Japanese": "Japonais", "Javanese": "Javanese", "Kannada": "Kannada", "Kazakh": "Kazakh", "Khmer": "Khmer", "Korean": "<PERSON><PERSON><PERSON>", "Lao": "Lao", "Latin": "Latins", "Latvian": "<PERSON><PERSON><PERSON>", "Lingala": "Lingala", "Lithuanian": "lituanien", "Luxembourgish": "luxembourgeois", "Macedonian": "<PERSON><PERSON><PERSON><PERSON>", "Malagasy": "Malagasy", "Malay": "<PERSON><PERSON>", "Malayalam": "Malaisien", "Maltese": "Maltese", "Maori": "<PERSON><PERSON>", "Marathi": "Marathi", "Mongolian": "Mongol", "Nepali": "Nepali", "Norwegian": "Norvégien", "Norwegian Nynorsk": "Norwegian Nynorsk", "Occitan": "Occitan", "Panjabi": "Panjabi", "Pashto": "Pachto", "Persian": "Perse", "Polish": "Polonais", "Romanian": "<PERSON><PERSON><PERSON><PERSON>", "Russian": "<PERSON><PERSON>", "Sanskrit": "Sanskrit", "Serbian": "Serb<PERSON>", "Shona": "<PERSON><PERSON><PERSON>", "Sindhi": "Sindhi", "Sinhala": "Cinghala", "Slovak": "Slovaque", "Slovenian": "Slovenian", "Somali": "Somali", "Sundanese": "Sundanese", "Swahili": "Swahili", "Swedish": "<PERSON><PERSON><PERSON><PERSON>", "Tagalog": "Tagalog", "Tajik": "Tadjik", "Tamil": "Tamil", "Tatar": "Tatar", "Telugu": "Telugu", "Thai": "<PERSON><PERSON><PERSON>", "Tibetan": "<PERSON><PERSON><PERSON><PERSON>", "Turkish": "<PERSON><PERSON>", "Turkmen": "Turkmen", "Ukrainian": "<PERSON><PERSON><PERSON><PERSON>", "Urdu": "<PERSON><PERSON><PERSON>", "Uzbek": "Uzbek", "Vietnamese": "Vietnamese", "Welsh": "<PERSON><PERSON><PERSON>", "Yiddish": "Yiddish", "Yoruba": "Yoruba", "Best": "<PERSON><PERSON><PERSON>", "Nano": "<PERSON><PERSON>", "Low": "Bas", "Default": "<PERSON><PERSON> <PERSON><PERSON>", "High": "<PERSON><PERSON><PERSON>", "MP3": "Mp3", "WAV": "WAV", "Account Number": "Numéro de compte", "Banking Information": "Informations bancaires", "Blood Type": "<PERSON> de sang", "Credit Card CVV": "CVV Carte de Crédit", "Credit Card Expiration": "Expiration de la carte de crédit", "Credit Card Number": "Numéro de carte de crédit", "Date": "Date", "Date Interval": "Intervalle de date", "Date of Birth": "Date de naissance", "Driver's License": "Permis de conduire", "Drug": "Médicament", "Duration": "<PERSON><PERSON><PERSON>", "Email Address": "Adresse e-mail", "Event": "Evénement", "Filename": "Nom du fichier", "Gender Sexuality": "Sexualité sexuelle", "Healthcare Number": "Nombre de soins de santé", "Injury": "<PERSON><PERSON><PERSON>", "IP Address": "Adresse IP", "Language": "<PERSON><PERSON>", "Location": "Localisation", "Marital Status": "Statut matrimonial", "Medical Condition": "Condition médicale", "Medical Process": "Processus médical", "Money Amount": "Montant de l'argent", "Nationality": "Nationalité", "Number Sequence": "<PERSON><PERSON>quence de nombre", "Occupation": "Occupation", "Organization": "Organisation", "Passport Number": "<PERSON>um<PERSON><PERSON>", "Password": "Password", "Person Age": "<PERSON>ge de <PERSON> personne", "Person Name": "Nom de la personne", "Phone Number": "Numéro de téléphone", "Physical Attribute": "Attribut physique", "Political Affiliation": "Affiliation politique", "Religion": "Religion", "Statistics": "Statistiques", "Time": "Date et heure", "URL": "URL", "US Social Security Number": "Numéro de sécurité sociale des États-Unis", "Username": "Nom d'utilisateur", "Vehicle ID": "ID du véhicule", "Zodiac Sign": "Panneau du zodiaque", "Entity Name": "Nom de l'entité", "Hash": "Hachage", "Informative": "Informatif", "Conversational": "Conversation", "Catchy": "Attraper", "Bullets": "<PERSON>es", "Bullets Verbose": "Balles à balles", "Gist": "Gist", "Headline": "Titre", "Paragraph": "Paragraphe", "SRT": "SRT", "VTT": "VTT", "Queued": "En file d'attente", "Processing": "Traitement en cours", "Completed": "<PERSON><PERSON><PERSON><PERSON>", "Error": "Error", "Claude 3.5 Sonnet (on Anthropic)": "Claude 3.5 Sonnet (sur Anthropique)", "Claude 3 Opus (on Anthropic)": "Claude 3 Opus (sur Anthropique)", "Claude 3 Haiku (on Anthropic)": "Claude 3 Haiku (sur Anthropique)", "Claude 3 Sonnet (on Anthropic)": "Claude 3 Sonnet (sur Anthropique)", "Claude 2.1 (on Anthropic)": "Claude 2.1 (sur Anthropique)", "Claude 2 (on Anthropic)": "Claude 2 (sur Anthropique)", "Claude Instant 1.2 (on Anthropic)": "<PERSON> 1.2 (sur Anthropique)", "Basic": "Basique", "Mistral 7B (Hosted by AssemblyAI)": "Mistral 7B (hébergé par AssemblyAI)", "GET": "OBTENIR", "POST": "POSTER", "PATCH": "PATCH", "PUT": "EFFACER", "DELETE": "SUPPRIMER", "HEAD": "TÊTE"}
{"AssemblyAI": "AssemblyAI", "Transcribe and extract data from audio using AssemblyAI's Speech AI.": "Transcreva e extraia dados do áudio usando a IA da fala da AssemblyAI.", "You can retrieve your AssemblyAI API key within your AssemblyAI [Account Settings](https://www.assemblyai.com/app/account?utm_source=activepieces).": "Você pode recuperar sua chave de API do AssemblyAI dentro do seu AssemblyAI [Configurações da Conta](https://www.assemblyai.com/app/account?utm_source=activepieces).", "Upload File": "Enviar Arquivo", "Transcribe": "Transcrever", "Get Transcript": "Get Transcript", "Get Transcript Sentences": "Obter frases transcritas", "Get Transcript Paragraphs": "Obter Parágrafos de Transcrição", "Get Transcript Subtitles": "Obter legendas de transcrição", "Get Transcript Redacted Audio": "Obter Transcrição de Áudio Redagido", "Search words in transcript": "Pesquisar palavras em transcrição", "List transcripts": "Listar transcrições", "Delete transcript": "Excluir transcrição", "Run a Task using LeMUR": "Executar uma tarefa usando o LeMUR", "Retrieve LeMUR response": "Recuperar resposta ao LeMUR", "Purge LeMUR request data": "Limpar dados do LeMUR request", "Custom API Call": "Chamada de API personalizada", "Upload a media file to AssemblyAI's servers.": "Enviar um arquivo de mídia para os servidores do AssemblyAI.", "Transcribe an audio or video file using AssemblyAI.": "Transcreva um arquivo de áudio ou vídeo usando o AssemblyAI.", "Retrieves a transcript by its ID.": "Recupera uma transcrição por seu ID.", "Retrieve the sentences of the transcript by its ID.": "Recupere as frases da transcrição por seu documento de identidade.", "Retrieve the paragraphs of the transcript by its ID.": "Recuperar os parágrafos da transcrição por seu ID.", "Export the transcript as SRT or VTT subtitles.": "Exportar a transcrição como legendas SRT ou VTT.", "Get the result of the redacted audio model.": "Obter o resultado do modelo de áudio aninhado.", "Search through the transcript for keywords. You can search for individual words, numbers, or phrases containing up to five words or numbers.": "Pesquisar através da transcrição por palavras-chave. Você pode procurar por palavras, números ou frases contendo até cinco palavras ou números.", "Retrieve a list of transcripts you created.\nTranscripts are sorted from newest to oldest. The previous URL always points to a page with older transcripts.": "Retrieve a list of transcripts you created.\nTranscripts are sorted from newest to oldest. The previous URL always points to a page with older transcripts.", "Remove the data from the transcript and mark it as deleted.": "Remova os dados da transcrição e marque-os como excluídos.", "Use the LeMUR task endpoint to input your own LLM prompt.": "Use o LeMUR task endpoint para inserir seu próprio prompt LLM.", "Retrieve a LeMUR response that was previously generated.": "Recuperar uma resposta LeMUR que foi gerada anteriormente.", "Delete the data for a previously submitted LeMUR request.\nThe LLM response data, as well as any context provided in the original request will be removed.": "Excluir os dados para uma solicitação LeMUR enviada anteriormente.\nOs dados de resposta de LLM, bem como qualquer contexto fornecido na solicitação original serão removidos.", "Make a custom API call to a specific endpoint": "Faça uma chamada de API personalizada para um ponto de extremidade específico", "Audio File": "Arquivo de Áudio", "Audio URL": "URL do Áudio", "Language Code": "Código do Idioma", "Language Detection": "Detecção de Idioma", "Language Confidence Threshold": "Limite de confiança da língua", "Speech Model": "<PERSON><PERSON>", "Punctuate": "Punctuate", "Format Text": "Formatar Texto", "Disfluencies": "Disfluências", "Dual Channel": "Canal duplo", "Webhook URL": "URL do webhook", "Webhook Auth Header Name": "Nome de Cabeçalho de Autenticação Webhook", "Webhook Auth Header Value": "Valor do cabeçalho de autenticação Webhook", "Key Phrases": "Frases principais", "Audio Start From": "Início do Áudio De", "Audio End At": "Fim do áudio em", "Word Boost": "Boost de Palavra", "Word Boost Level": "Nível de Boost de Palavra", "Filter Profanity": "Filtrar Profanidade", "Redact PII": "Redact PII", "Redact PII Audio": "Redact PII Audio", "Redact PII Audio Quality": "Redact qualidade de áudio PII", "Redact PII Policies": "Redact PII Policies", "Redact PII Substitution": "Substituição PII de Redacto", "Speaker Labels": "Ró<PERSON>los dos alto-falantes", "Speakers Expected": "Alto-falantes esperados", "Content Moderation": "Moderação de conteúdo", "Content Moderation Confidence": "Confiança de moderação de conteúdo", "Topic Detection": "Detecção de tópico", "Custom Spellings": "Ortografias personalizadas", "Sentiment Analysis": "Análise do Sentimento", "Auto Chapters": "<PERSON><PERSON><PERSON><PERSON>", "Entity Detection": "Detecção de Entidade", "Speech Threshold": "<PERSON><PERSON>", "Enable Summarization": "Habilitar resumo", "Summary Model": "<PERSON><PERSON>", "Summary Type": "Tipo de Resumo", "Enable Custom Topics": "Ativar tópicos personalizados", "Custom Topics": "Tópicos Personalizados", "Wait until transcript is ready": "Aguarde até que a transcrição esteja pronta", "Throw if transcript status is error": "Jogar se o status de transcrição estiver errado", "Transcript ID": "ID de transcrição", "Subtitles Format": "Formato das Legendas", "Number of Characters per Caption": "Número de caracteres por legenda", "Download file?": "Baixar arquivo?", "Download File Name": "Baixar nome do arquivo", "Words": "Palavras", "Limit": "Limitar", "Status": "Estado", "Created On": "Criado Em", "Before ID": "Antes do <PERSON>", "After ID": "Depois da identificação", "Throttled Only": "Limitado <PERSON>", "Prompt": "Aviso", "Transcript IDs": "IDs de transcrição", "Input Text": "Input Text", "Context": "Contexto", "Final Model": "Modelo Final", "Maximum Output Size": "<PERSON><PERSON><PERSON>", "Temperature": "Temperatura", "LeMUR request ID": "ID de solicitação do LeMUR", "Method": "<PERSON><PERSON><PERSON><PERSON>", "Headers": "Cabeçalhos", "Query Parameters": "Parâ<PERSON><PERSON> da consult<PERSON>", "Body": "<PERSON><PERSON><PERSON><PERSON>", "No Error on Failure": "Nenhum erro no Failure", "Timeout (in seconds)": "Tempo limite (em segundos)", "The File or URL of the audio or video file.": "O arquivo ou URL do arquivo de áudio ou vídeo.", "The URL of the audio or video file to transcribe.": "URL do arquivo de áudio ou vídeo a ser transcrito.", "The language of your audio file. Possible values are found in [Supported Languages](https://www.assemblyai.com/docs/concepts/supported-languages).\nThe default value is 'en_us'.\n": "Idioma do seu arquivo de áudio. Possíveis valores são encontrados em [Idiomas Suportados](https://www.assemblyai.com/docs/concepts/supported-languages).\nO valor padrão é 'en_us'.\n", "Enable [Automatic language detection](https://www.assemblyai.com/docs/models/speech-recognition#automatic-language-detection), either true or false.": "Habilitar [Detecção de idioma automático](https://www.assemblyai.com/docs/models/speech-recognition#automatic-language-detection), verda<PERSON><PERSON> ou falso.", "The confidence threshold for the automatically detected language.\nAn error will be returned if the language confidence is below this threshold.\nDefaults to 0.\n": "O limite de confiança para o idioma detectado automaticamente.\nUm erro será retornado se a confiança do idioma estiver abaixo desse limite.\nPadrão é 0.\n", "The speech model to use for the transcription. When `null`, the \"best\" model is used.": "O modelo de fala a ser usado para a transcrição. Quando `null`, o modelo \"best\" é usado.", "Enable Automatic Punctuation, can be true or false": "Habilitar Pontuação Automática, pode ser verdadeiro ou falso", "Enable Text Formatting, can be true or false": "Ativar a formatação do texto, pode ser verdadeiro ou falso", "Transcribe Filler Words, like \"umm\", in your media file; can be true or false": "Transcreva Palavras de Filtro, como \"umm\", no seu arquivo de mídia; pode ser verdadeiro ou falso", "Enable [Dual Channel](https://www.assemblyai.com/docs/models/speech-recognition#dual-channel-transcription) transcription, can be true or false.": "Habilitar a transcrição [Canal dual](https://www.assemblyai.com/docs/models/speech-recognition#dual-channel-transcription), pode ser verdadeira ou falsa.", "The URL to which we send webhook requests.\nWe sends two different types of webhook requests.\nOne request when a transcript is completed or failed, and one request when the redacted audio is ready if redact_pii_audio is enabled.\n": "The URL to which we send webhook requests.\nWe sends two different types of webhook requests.\nOne request when a transcript is completed or failed, and one request when the redacted audio is ready if redact_pii_audio is enabled.\n", "The header name to be sent with the transcript completed or failed webhook requests": "O nome do cabeçalho a ser enviado com a transcrição concluída ou com solicitações de webhook que falharam", "The header value to send back with the transcript completed or failed webhook requests for added security": "O valor do cabeçalho para enviar de volta com a transcrição concluída ou com solicitações falhadas de webhook para aumentar a segurança", "Enable Key Phrases, either true or false": "Ativar frases chave, verdadeiro ou falso", "The point in time, in milliseconds, to begin transcribing in your media file": "O ponto no tempo, em milissegundos, para começar a transcrever o seu arquivo de mídia", "The point in time, in milliseconds, to stop transcribing in your media file": "O ponto no tempo, em milissegundos, para parar de transcrever o seu arquivo de mídia", "The list of custom vocabulary to boost transcription probability for": "Lista de vocabulário personalizado para aumentar a probabilidade de transcrição de", "How much to boost specified words": "Quanto para impulsionar as palavras especificadas", "Filter profanity from the transcribed text, can be true or false": "Filtrar palavrões do texto transcrito, pode ser verdadeiro ou falso", "Redact PII from the transcribed text using the Redact PII model, can be true or false": "Redact PII do texto transcrito usando o modelo de Redact PII pode ser verdadeiro ou falso", "Generate a copy of the original media file with spoken PII \"beeped\" out, can be true or false. See [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) for more details.": "Gerar uma cópia do arquivo de mídia original com PII \"beeed\" falado, pode ser verdadeiro ou falso. Veja [Redação PI](https://www.assemblyai.com/docs/models/pii-redaction) para mais detalhes.", "Controls the filetype of the audio created by redact_pii_audio. Currently supports mp3 (default) and wav. See [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) for more details.": "Controla o tipo de arquivo do áudio criado pelo redact_pii_audio. Atualmente suporta mp3 (padrão) e onda. Veja [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) para mais detalhes.", "The list of PII Redaction policies to enable. See [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) for more details.": "Lista das políticas de Redação PII para habilitar. Veja [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) para mais detalhes.", "The replacement logic for detected PII, can be \"entity_type\" or \"hash\". See [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) for more details.": "A lógica de substituição do PII detectado, pode ser \"entity_type\" ou \"hash\". Veja [PII redaction](https://www.assemblyai.com/docs/models/pii-redaction) para mais detalhes.", "Enable [Speaker diarization](https://www.assemblyai.com/docs/models/speaker-diarization), can be true or false": "Habilitar [Dialização do alto-falante](https://www.assemblyai.com/docs/models/speaker-diarization), pode ser verdadeira ou falsa", "Tells the speaker label model how many speakers it should attempt to identify, up to 10. See [Speaker diarization](https://www.assemblyai.com/docs/models/speaker-diarization) for more details.": "Diz ao pregador o modelo de rótulo de quantos pregadores ele deve tentar identificar, até 10. Veja [Dialização do alto-falante](https://www.assemblyai.com/docs/models/speaker-diarization) para mais detalhes.", "Enable [Content Moderation](https://www.assemblyai.com/docs/models/content-moderation), can be true or false": "Habilitar [Moderação de Conteúdo](https://www.assemblyai.com/docs/models/content-moderation), pode ser verdadeiro ou falso", "The confidence threshold for the Content Moderation model. Values must be between 25 and 100.": "O limite de confiança para o modelo de Moderação de Conteúdo. Os valores devem estar entre 25 e 100.", "Enable [Topic Detection](https://www.assemblyai.com/docs/models/topic-detection), can be true or false": "Habilitar [Detecção de Tópico](https://www.assemblyai.com/docs/models/topic-detection), pode ser verdadeiro ou falso", "Customize how words are spelled and formatted using to and from values.\nUse a JSON array of objects of the following format:\n```\n[\n  {\n    \"from\": [\"original\", \"spelling\"],\n    \"to\": \"corrected\"\n  }\n]\n```\n": "Personalize como as palavras são ortografadas e formatadas usando os valores de e para cima.\nUse uma matriz JSON de objetos do seguinte formato:\n```\n[\n  {\n    \"from\": [\"original\", \"ortografia\"],\n    \"para\": \"corrigida\"\n  }\n]\n```\n", "Enable [Sentiment Analysis](https://www.assemblyai.com/docs/models/sentiment-analysis), can be true or false": "Habilitar [<PERSON><PERSON><PERSON><PERSON>](https://www.assemblyai.com/docs/models/sentiment-analysis), pode ser verdadeiro ou falso", "Enable [Auto Chapters](https://www.assemblyai.com/docs/models/auto-chapters), can be true or false": "Ativar [<PERSON><PERSON><PERSON><PERSON>](https://www.assemblyai.com/docs/models/auto-chapters), pode ser verdadeiro ou falso", "Enable [Entity Detection](https://www.assemblyai.com/docs/models/entity-detection), can be true or false": "Ativar [Detecção da Entidade](https://www.assemblyai.com/docs/models/entity-detection), pode ser verdadeiro ou falso", "Reject audio files that contain less than this fraction of speech.\nValid values are in the range [0, 1] inclusive.\n": "Reject audio files that contain less than this fraction of speech.\nValid values are in the range [0, 1] inclusive.\n", "Enable [Summarization](https://www.assemblyai.com/docs/models/summarization), can be true or false": "Habilitar [Summarization](https://www.assemblyai.com/docs/models/summarization), pode ser verdadeiro ou falso", "The model to summarize the transcript": "O modelo para resumir a transcrição", "The type of summary": "O tipo de resumo", "Enable custom topics, either true or false": "Habilitar tópicos personalizados, verdadeiro ou falso", "The list of custom topics": "A lista de tópicos personalizados", "Wait until the transcript status is \"completed\" or \"error\" before moving on to the next step.": "Aguarde até que o status de transcrição seja \"completado\" ou \"erro\" antes de passar para o próximo passo.", "If the transcript status is \"error\", throw an error.": "Se o status de transcrição for \"erro\", lança um erro.", "The maximum number of characters per caption": "O número máximo de caracteres por legenda", "The desired file name for storing in ActivePieces. Make sure the file extension is correct.": "O nome do arquivo desejado para armazenar no ActivePieces. Certifique-se de que a extensão do arquivo está correta.", "Keywords to search for": "Palavras-chave para procurar", "Maximum amount of transcripts to retrieve": "Quantidade máxima de transcrições a recuperar", "Filter by transcript status": "Filtrar por status de transcrição", "Only get transcripts created on this date": "Apenas obtenha transcrições criadas nesta data", "Get transcripts that were created before this transcript ID": "Obter transcrições que foram criadas antes desta identificação de transcrição", "Get transcripts that were created after this transcript ID": "Obter transcrições que foram criadas após esta identificação de transcrição", "Only get throttled transcripts, overrides the status filter": "Só obter transcrições limitadas, substituir o filtro de status", "Your text to prompt the model to produce a desired output, including any context you want to pass into the model.": "Seu texto para prompt o modelo para produzir uma saída desejada, incluindo qualquer contexto que você deseja passar para o modelo.", "A list of completed transcripts with text. Up to a maximum of 100 files or 100 hours, whichever is lower.\nUse either transcript_ids or input_text as input into LeMUR.\n": "A list of completed transcripts with text. Up to a maximum of 100 files or 100 hours, whichever is lower.\nUse either transcript_ids or input_text as input into LeMUR.\n", "Custom formatted transcript data. Maximum size is the context limit of the selected model, which defaults to 100000.\nUse either transcript_ids or input_text as input into LeMUR.\n": "Custom formatted transcript data. Maximum size is the context limit of the selected model, which defaults to 100000.\nUse either transcript_ids or input_text as input into LeMUR.\n", "Context to provide the model. This can be a string or a free-form JSON value.": "Contexto para fornecer o modelo. <PERSON>to pode ser uma string ou uma forma JSON de forma livre.", "The model that is used for the final prompt after compression is performed.\n": "O modelo que é usado para a prompt final após a compressão é executada.\n", "Max output size in tokens, up to 4000": "<PERSON><PERSON><PERSON> m<PERSON>xi<PERSON> de saída em tokens, até 4000", "The temperature to use for the model.\nHigher values result in answers that are more creative, lower values are more conservative.\nCan be any value between 0.0 and 1.0 inclusive.\n": "A temperatura para usar para o modelo.\nValores maiores resultam em respostas que são mais criativas, valores mais baixos são mais conservadores.\nPode ser qualquer valor entre 0.0 e 1.0 inclusive.\n", "The ID of the LeMUR request whose data you want to delete. This would be found in the response of the original request.": "O ID da solicitação LeMUR cujos dados você deseja excluir. <PERSON>to pode ser encontrado na resposta da solicitação original.", "Authorization headers are injected automatically from your connection.": "Os cabeçalhos de autorização são inseridos automaticamente a partir da sua conexão.", "English (Global)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Global)", "English (Australian)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (australiano)", "English (British)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)", "English (US)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (pt-BR)", "Spanish": "espanhol", "French": "franc<PERSON>s", "German": "alemão", "Italian": "italiano", "Portuguese": "Português", "Dutch": "Neerlandês", "Afrikaans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Albanian": "albanês", "Amharic": "Amharic", "Arabic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Armenian": "<PERSON><PERSON><PERSON>", "Assamese": "assamês", "Azerbaijani": "azerbaijano", "Bashkir": "Bashkir", "Basque": "basco", "Belarusian": "Bielorrusso", "Bengali": "bengali", "Bosnian": "b<PERSON><PERSON>", "Breton": "Breton", "Bulgarian": "b<PERSON><PERSON><PERSON>", "Burmese": "Burmese", "Catalan": "catalão", "Chinese": "chinês", "Croatian": "croata", "Czech": "tcheco", "Danish": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Estonian": "Estônio", "Faroese": "Faroese", "Finnish": "<PERSON><PERSON><PERSON>", "Galician": "galego", "Georgian": "georgiano", "Greek": "<PERSON><PERSON>", "Gujarati": "Gujarati", "Haitian": "Haitian", "Hausa": "Hausa", "Hawaiian": "<PERSON><PERSON><PERSON>", "Hebrew": "Hebraico", "Hindi": "hindi", "Hungarian": "<PERSON><PERSON><PERSON><PERSON>", "Icelandic": "Icelandic", "Indonesian": "indonésio", "Japanese": "j<PERSON><PERSON><PERSON>", "Javanese": "Javanese", "Kannada": "Kannada", "Kazakh": "Kazakh", "Khmer": "Khmer", "Korean": "<PERSON><PERSON>", "Lao": "Lao", "Latin": "latim", "Latvian": "Letã", "Lingala": "Lingala", "Lithuanian": "lituano", "Luxembourgish": "luxemburguês", "Macedonian": "<PERSON><PERSON><PERSON><PERSON>", "Malagasy": "Malagasy", "Malay": "malaio", "Malayalam": "Malaialam", "Maltese": "Maltese", "Maori": "<PERSON><PERSON>", "Marathi": "marata", "Mongolian": "Mongol", "Nepali": "Nepali", "Norwegian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Norwegian Nynorsk": "Norwegian Nynorsk", "Occitan": "Occitan", "Panjabi": "Panjabi", "Pashto": "Pashto", "Persian": "persa", "Polish": "<PERSON><PERSON><PERSON><PERSON>", "Romanian": "romeno", "Russian": "<PERSON>", "Sanskrit": "Sanskrit", "Serbian": "<PERSON><PERSON><PERSON><PERSON>", "Shona": "<PERSON><PERSON>", "Sindhi": "Sindhi", "Sinhala": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Slovak": "Eslovaco", "Slovenian": "Slovenian", "Somali": "Somali", "Sundanese": "Sundanese", "Swahili": "<PERSON><PERSON><PERSON><PERSON>", "Swedish": "sueco", "Tagalog": "Tagalog", "Tajik": "Tadjique", "Tamil": "Tamil", "Tatar": "Tatar", "Telugu": "Telugu", "Thai": "Tailandês", "Tibetan": "tibetano", "Turkish": "<PERSON><PERSON><PERSON>", "Turkmen": "Turkmen", "Ukrainian": "ucraniano", "Urdu": "urdu", "Uzbek": "Uzbek", "Vietnamese": "Vietnamese", "Welsh": "galês", "Yiddish": "ií<PERSON><PERSON>", "Yoruba": "Yoruba", "Best": "Mel<PERSON>", "Nano": "nano", "Low": "baixa", "Default": "Padrão", "High": "alta", "MP3": "MP3", "WAV": "WAV", "Account Number": "Numero da Conta", "Banking Information": "Informações Bancárias", "Blood Type": "T<PERSON>o de <PERSON>ue", "Credit Card CVV": "Cartão de Crédito CVV", "Credit Card Expiration": "Expiração do Cartão de Crédito", "Credit Card Number": "Número do cartão de crédito", "Date": "Encontro", "Date Interval": "Intervalo de data", "Date of Birth": "Data de nascimento", "Driver's License": "Carteira de Motorista", "Drug": "<PERSON><PERSON><PERSON>", "Duration": "Duração", "Email Address": "Endereço de e-mail", "Event": "Evento", "Filename": "Nome", "Gender Sexuality": "Sexualidade Sexualidade", "Healthcare Number": "Número de <PERSON>", "Injury": "Les<PERSON><PERSON>", "IP Address": "Endereço IP", "Language": "IDIOMA", "Location": "Local:", "Marital Status": "Estado civil", "Medical Condition": "Condição Médica", "Medical Process": "Processo Médico", "Money Amount": "Valor do Dinheiro", "Nationality": "Nacionalidade", "Number Sequence": "Sequência de números", "Occupation": "Ocupação", "Organization": "Cliente", "Passport Number": "Número de Passaporte", "Password": "<PERSON><PERSON>", "Person Age": "<PERSON> das pessoas", "Person Name": "Nome da pessoa", "Phone Number": "Número de telefone", "Physical Attribute": "Atributo físico", "Political Affiliation": "Afiliação Política", "Religion": "Religião", "Statistics": "estatísticas", "Time": "<PERSON><PERSON><PERSON><PERSON>", "URL": "URL:", "US Social Security Number": "Número de Segurança Social dos EUA", "Username": "Usuário:", "Vehicle ID": "ID do veículo", "Zodiac Sign": "Signo do Zodíaco", "Entity Name": "Nome da entidade", "Hash": "Hash", "Informative": "Informativo", "Conversational": "Conversacional", "Catchy": "<PERSON><PERSON><PERSON><PERSON>", "Bullets": "<PERSON><PERSON>", "Bullets Verbose": "Verbose de <PERSON>las", "Gist": "Neblina", "Headline": "<PERSON><PERSON><PERSON><PERSON>", "Paragraph": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SRT": "SRT", "VTT": "VTT", "Queued": "Enfileirado", "Processing": "Processando", "Completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Error": "Erro", "Claude 3.5 Sonnet (on Anthropic)": "Claude 3.5 Sonnet (no Antrópico)", "Claude 3 Opus (on Anthropic)": "Claude 3 Opus (no Antrópico)", "Claude 3 Haiku (on Anthropic)": "Claude 3 <PERSON><PERSON> (no Anthropic)", "Claude 3 Sonnet (on Anthropic)": "Claude 3 Sonnet (no Antrópico)", "Claude 2.1 (on Anthropic)": "Claude 2.1 (sobre Anthrop<PERSON>)", "Claude 2 (on Anthropic)": "Claude 2 (sobre Anthrop<PERSON>)", "Claude Instant 1.2 (on Anthropic)": "<PERSON> 1.2 (em Anthropic)", "Basic": "Básico", "Mistral 7B (Hosted by AssemblyAI)": "Mistral 7B (Selvagem por AssemblyAI)", "GET": "OBTER", "POST": "POSTAR", "PATCH": "COMPRAR", "PUT": "COLOCAR", "DELETE": "EXCLUIR", "HEAD": "CABEÇA"}
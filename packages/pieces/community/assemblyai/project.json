{"name": "pieces-assemblyai", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/pieces/community/assemblyai/src", "projectType": "library", "release": {"version": {"generatorOptions": {"packageRoot": "dist/{projectRoot}", "currentVersionResolver": "git-tag"}}}, "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/pieces/community/assemblyai", "tsConfig": "packages/pieces/community/assemblyai/tsconfig.lib.json", "packageJson": "packages/pieces/community/assemblyai/package.json", "main": "packages/pieces/community/assemblyai/src/index.ts", "assets": ["packages/pieces/community/assemblyai/*.md", {"input": "packages/pieces/community/assemblyai/src/i18n", "output": "./src/i18n", "glob": "**/!(i18n.json)"}], "buildableProjectDepsInPackageJsonType": "dependencies", "updateBuildableProjectDepsInPackageJson": true}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "_generate-params": {"executor": "nx:run-commands", "options": {"command": "tsx ./scripts/generateFromSpec.ts", "cwd": "packages/pieces/community/assemblyai"}}, "format": {"executor": "nx:run-commands", "options": {"command": "nx format:write --files packages/pieces/community/assemblyai/**/*"}}, "generate-params": {"executor": "nx:run-commands", "options": {"commands": ["nx _generate-params pieces-assemblyai", "nx format pieces-assemblyai"]}}}}
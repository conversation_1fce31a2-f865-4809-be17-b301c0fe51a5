openapi: 3.1.0

info:
  version: 0.0.0

servers: []
security: []
paths: {}
components:
  schemas:
    TranscriptOptionalParams:
      properties:
        # use JSON as array with a nested array isn't supported for input in Active Pieces
        custom_spelling:
          description: |
            Customize how words are spelled and formatted using to and from values.
            Use a JSON array of objects of the following format:
            ```
            [
              {
                "from": ["original", "spelling"],
                "to": "corrected"
              }
            ]
            ```
          type: json

    LemurBaseParams:
      properties:
        context:
          x-ap-type: LongText

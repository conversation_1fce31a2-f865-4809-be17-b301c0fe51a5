{"Aircall": "Aircall", "API ID": "API ID", "API Token": "API Token", "You can create API key by naviagting to **Integrations & API** menu.": "You can create API key by naviagting to **Integrations & API** menu.", "Comment a Call": "Comment a Call", "Create a Contact": "Create a Contact", "Find Call": "Find Call", "Find Contact": "Find Contact", "Get Call": "Get Call", "Tag a Call": "Tag a Call", "Update Contact": "Update Contact", "Custom API Call": "Custom API Call", "Adds a comment (note) to a specific call.": "Adds a comment (note) to a specific call.", "Creates a new contact.": "Creates a new contact.", "Finds specific call based on provided filter.": "Finds specific call based on provided filter.", "Finds contact based on phone or email.": "Finds contact based on phone or email.", "Retrieves details about a specific call.": "Retrieves details about a specific call.", "Add tags to a specific call.": "Add tags to a specific call.", "Update an existing contact.": "Update an existing contact.", "Make a custom API call to a specific endpoint": "Make a custom API call to a specific endpoint", "Call": "Call", "Comment Content": "Comment Content", "Phone Numbers": "Phone Numbers", "First Name": "First Name", "Last Name": "Last Name", "Company Name": "Company Name", "Information": "Information", "Email Addresses": "Email Addresses", "Call Direction": "Call Direction", "Phone Number": "Phone Number", "Tags": "Tags", "Email": "Email", "Contact ID": "Contact ID", "Method": "Method", "Headers": "Headers", "Query Parameters": "Query Parameters", "Body": "Body", "No Error on Failure": "No Error on Failure", "Timeout (in seconds)": "Timeout (in seconds)", "Additional information about the contact.": "Additional information about the contact.", "Array of email addresses (optional, max 20)": "Array of email addresses (optional, max 20)", "Filter by call direction": "Filter by call direction", "The calling or receiving phone number of calls.": "The calling or receiving phone number of calls.", "Search by phone number (with country code, e.g., +1234567890).": "Search by phone number (with country code, e.g., +1234567890).", "Search by email address.": "Search by email address.", "Select the contact to update": "Select the contact to update", "Last name of the contact": "Last name of the contact", "Additional information about the contact": "Additional information about the contact", "Authorization headers are injected automatically from your connection.": "Authorization headers are injected automatically from your connection.", "Inbound": "Inbound", "Outbound": "Outbound", "GET": "GET", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "DELETE", "HEAD": "HEAD", "Call Ended": "Call Ended", "New Contact": "New Contact", "New Note": "New Note", "New Number Created": "New Number Created", "New SMS": "New SMS", "Triggers when a call ends.": "Triggers when a call ends.", "Triggers when a new contact is created.": "Triggers when a new contact is created.", "Triggers when a new note is added to a call.": "Triggers when a new note is added to a call.", "Triggers when a new number is created.": "Triggers when a new number is created.", "Triggers when a new SMS message is received.": "Triggers when a new SMS message is received."}
{"AI Answer": "AI Ответ", "\n      To obtain your AiAnswer API access token, follow these steps below:\n      1. Log in to your AiAnswer account at https://app.aianswer.us .\n      2. Navigate to Settings < API Key.\n      3. Click on Copy icon to copy your existing Key or click on New API Key to create a new one.\n      4. Copy the API Key and paste it below in \"AiAnswer API Key\".\n    ": "\n      Для получения токена доступа к AiAnswer API, выполните следующие шаги:\n      1. Войдите в свой аккаунт AiAnswer на https://app.aianswer.us .\n      2. Перейдите в Настройки < API Key.\n      3. Нажмите на значок \"Копировать\", чтобы скопировать существующий ключ или нажмите на Новый ключ API для создания нового.\n      4. Скопируйте ключ API и вставьте его ниже в \"AiAnswer API Key\".\n    ", "Gmail get list of Agents": "Gmail получает список агентов", "Create Phone Call": "Создать звонок", "Get Call Details": "Получить подробности звонка", "Schedule Call Agent": "Запланировать звонок Агента", "Get Call Transcript": "Получить субтитры вызова", "Custom API Call": "Пользовательский вызов API", "get the lists of agents with Gmail": "получить списки агентов с помощью Gmail", "Create a phone call to customer from Agent": "Создать телефонный звонок клиенту от Агента", "Fetch Call details by Call ID": "Получить детали звонка по коду", "Schedule a call with an agent": "Запланировать звонок агентом", "Fetch the transcript of a call by Call ID": "Получить субтитры звонка по номеру вызова", "Make a custom API call to a specific endpoint": "Сделать пользовательский API вызов к определенной конечной точке", "Agent ID": "ID Агента", "To Phone Number": "На номер телефона", "Details": "Детали", "Call ID": "ID звонка", "Phone Number": "Номер телефона", "Execution Time": "Время выполнения", "Timezone": "Timezone", "Prospect Details": "Детали аспекта", "Method": "Метод", "Headers": "Заголовки", "Query Parameters": "Параметры запроса", "Body": "Тело", "No Error on Failure": "Нет ошибок при ошибке", "Timeout (in seconds)": "Таймаут (в секундах)", "Enter the phone number, along with country code, in format (e.g., +919876543210)": "Введите номер телефона вместе с кодом страны в формате (например, +919876543210)", "Optional details with key-value pairs (e.g., customer_id, priority)": "Необязательные детали с парами ключевого значения (например, customer_id, приоритет)", "Time to schedule the call in YYYY-MM-DD HH:MM:SS format.": "Время запланировать вызов в формате YYYY-MM-DD HH:MM:SS.", "Timezone of the scheduled call (e.g., Asia/Calcutta)": "Часовой пояс запланированного вызова (например, Азия/Калькутта)", "Optional prospect details with key-value pairs (e.g., customer_id, priority)": "Необязательная проверка деталей с парами ключевого значения (например, customer_id, приоритет)", "Authorization headers are injected automatically from your connection.": "Заголовки авторизации включаются автоматически из вашего соединения.", "GET": "ПОЛУЧИТЬ", "POST": "ПОСТ", "PATCH": "ПАТЧ", "PUT": "ПОКУПИТЬ", "DELETE": "УДАЛИТЬ", "HEAD": "HEAD"}
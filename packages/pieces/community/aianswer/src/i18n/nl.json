{"AI Answer": "AI antwoord", "\n      To obtain your AiAnswer API access token, follow these steps below:\n      1. Log in to your AiAnswer account at https://app.aianswer.us .\n      2. Navigate to Settings < API Key.\n      3. Click on Copy icon to copy your existing Key or click on New API Key to create a new one.\n      4. Copy the API Key and paste it below in \"AiAnswer API Key\".\n    ": "\n      Om je AiAnswer API-toegangstoken te verkrijgen, volg deze stappen hieronder:\n      1. Log in op je AiAnswer account op https://app.aianswer.us .\n      2. Navigeer naar Instellingen < API Key.\n      3. Klik op het pictogram kopi<PERSON>ren van uw bestaande sleutel of klik op de Nieuwe API-sleutel om een nieuwe te maken.\n      4. <PERSON><PERSON><PERSON> de API-sleutel en plak deze hieronder in \"AiAnswer API-sleutel\".\n    ", "Gmail get list of Agents": "<PERSON><PERSON> kri<PERSON> lij<PERSON> van agenten", "Create Phone Call": "Telefoongesprek aanmaken", "Get Call Details": "Oproepgege<PERSON><PERSON>", "Schedule Call Agent": "Oproep agent inplannen", "Get Call Transcript": "<PERSON><PERSON><PERSON> ve<PERSON>", "Custom API Call": "Custom API Call", "get the lists of agents with Gmail": "<PERSON> <PERSON>i<PERSON><PERSON> van agenten ophalen met <PERSON><PERSON>", "Create a phone call to customer from Agent": "Maak een telefoongesprek naar klant van Agent", "Fetch Call details by Call ID": "Oproepgegevens ophalen door oproep-ID", "Schedule a call with an agent": "Plan een g<PERSON><PERSON><PERSON> met een medewerker", "Fetch the transcript of a call by Call ID": "<PERSON><PERSON><PERSON> van de transcript van een oproep met oproep-ID", "Make a custom API call to a specific endpoint": "Maak een aangepaste API call naar een specifiek eindpunt", "Agent ID": "Agent ID", "To Phone Number": "Naar telefoonnummer", "Details": "Beschrijving", "Call ID": "Oproep ID", "Phone Number": "Telefoon nummer", "Execution Time": "Execution Time", "Timezone": "Timezone", "Prospect Details": "Details prospect", "Method": "<PERSON>e", "Headers": "Kopteksten", "Query Parameters": "Query parameters", "Body": "<PERSON><PERSON><PERSON>", "No Error on Failure": "<PERSON><PERSON> fout bij fout", "Timeout (in seconds)": "Time-out (in seconden)", "Enter the phone number, along with country code, in format (e.g., +919876543210)": "<PERSON>oer het telefoonnummer in, samen met de landcode, in formaat (bijv. +919876543210)", "Optional details with key-value pairs (e.g., customer_id, priority)": "Optionele details met sleutelwaarde paren (bijv. customer_id, prioriteit)", "Time to schedule the call in YYYY-MM-DD HH:MM:SS format.": "Tijd om de oproep te plannen in YYYY-MM-DD HH:MM:SS formaat.", "Timezone of the scheduled call (e.g., Asia/Calcutta)": "Ti<PERSON><PERSON><PERSON> van de geplande opro<PERSON> (bijv. Azië/Calcutta)", "Optional prospect details with key-value pairs (e.g., customer_id, priority)": "Optionele prospect details met sleutelwaarde paren (bijv. klant_id, prioriteit)", "Authorization headers are injected automatically from your connection.": "Autorisatie headers worden automatisch geïnjecteerd vanuit uw verbinding.", "GET": "KRIJG", "POST": "POSTE", "PATCH": "BEKIJK", "PUT": "PUT", "DELETE": "VERWIJDEREN", "HEAD": "HOOFD"}
{"AI Answer": "Resposta IA", "\n      To obtain your AiAnswer API access token, follow these steps below:\n      1. Log in to your AiAnswer account at https://app.aianswer.us .\n      2. Navigate to Settings < API Key.\n      3. Click on Copy icon to copy your existing Key or click on New API Key to create a new one.\n      4. Copy the API Key and paste it below in \"AiAnswer API Key\".\n    ": "\n      To obtain your AiAnswer API access token, follow these steps below:\n      1. Log in to your AiAnswer account at https://app.aianswer.us .\n      2. Navigate to Settings < API Key.\n      3. Click on Copy icon to copy your existing Key or click on New API Key to create a new one.\n      4. Copy the API Key and paste it below in \"AiAnswer API Key\".\n    ", "Gmail get list of Agents": "Gmail obter lista de agentes", "Create Phone Call": "<PERSON><PERSON><PERSON> chamada telefônica", "Get Call Details": "Obter de<PERSON><PERSON> da chamada", "Schedule Call Agent": "Agendar <PERSON>", "Get Call Transcript": "Obter transcrição de chamada", "Custom API Call": "Chamada de API personalizada", "get the lists of agents with Gmail": "obter as listas de agentes com o Gmail", "Create a phone call to customer from Agent": "Crie uma chamada telefônica para o cliente a partir do Agente", "Fetch Call details by Call ID": "Obter de<PERSON><PERSON> da chamada pelo ID da chamada", "Schedule a call with an agent": "Agende uma chamada com um agente", "Fetch the transcript of a call by Call ID": "Obter a transcrição de uma chamada pela ID de Chamada", "Make a custom API call to a specific endpoint": "Faça uma chamada de API personalizada para um ponto de extremidade específico", "Agent ID": "ID representante", "To Phone Number": "Para número de telefone", "Details": "de<PERSON><PERSON>", "Call ID": "ID da chamada", "Phone Number": "Número de telefone", "Execution Time": "Tempo de execução", "Timezone": "Timezone", "Prospect Details": "Detalhes do Prospecto", "Method": "<PERSON><PERSON><PERSON><PERSON>", "Headers": "Cabeçalhos", "Query Parameters": "Parâ<PERSON><PERSON> da consult<PERSON>", "Body": "<PERSON><PERSON><PERSON><PERSON>", "No Error on Failure": "Nenhum erro no Failure", "Timeout (in seconds)": "Tempo limite (em segundos)", "Enter the phone number, along with country code, in format (e.g., +919876543210)": "Digite o número de telefone, juntamente com o código do país, no formato (por exemplo, +919876543210)", "Optional details with key-value pairs (e.g., customer_id, priority)": "Detalhes opcionais com pares de chave-valor (por exemplo, customer_id, prioridade)", "Time to schedule the call in YYYY-MM-DD HH:MM:SS format.": "Hora de agendar a chamada no formato YYYY-MM-DD HH:MM:SS.", "Timezone of the scheduled call (e.g., Asia/Calcutta)": "<PERSON><PERSON> hor<PERSON> da chamada agenda<PERSON> (por exemplo, Ásia/Calcutta)", "Optional prospect details with key-value pairs (e.g., customer_id, priority)": "Detalhes opcionais de perspectiva com pares de chave-valor (por exemplo, customer_id, prioridade)", "Authorization headers are injected automatically from your connection.": "Os cabeçalhos de autorização são inseridos automaticamente a partir da sua conexão.", "GET": "OBTER", "POST": "POSTAR", "PATCH": "COMPRAR", "PUT": "COLOCAR", "DELETE": "EXCLUIR", "HEAD": "CABEÇA"}
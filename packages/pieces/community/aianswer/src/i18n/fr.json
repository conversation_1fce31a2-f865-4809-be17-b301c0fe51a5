{"AI Answer": "Réponse AI", "\n      To obtain your AiAnswer API access token, follow these steps below:\n      1. Log in to your AiAnswer account at https://app.aianswer.us .\n      2. Navigate to Settings < API Key.\n      3. Click on Copy icon to copy your existing Key or click on New API Key to create a new one.\n      4. Copy the API Key and paste it below in \"AiAnswer API Key\".\n    ": "\n      To obtain your AiAnswer API access token, follow these steps below:\n      1. Log in to your AiAnswer account at https://app.aianswer.us .\n      2. Navigate to Settings < API Key.\n      3. Click on Copy icon to copy your existing Key or click on New API Key to create a new one.\n      4. Copy the API Key and paste it below in \"AiAnswer API Key\".\n    ", "Gmail get list of Agents": "Gmail récupère la liste des agents", "Create Phone Call": "C<PERSON>er un appel téléphonique", "Get Call Details": "O<PERSON><PERSON><PERSON> les détails de l’appel", "Schedule Call Agent": "Planifier un agent d'appel", "Get Call Transcript": "Obtenir la transcription d'appel", "Custom API Call": "Appel API personnalisé", "get the lists of agents with Gmail": "obtenir les listes d'agents avec Gmail", "Create a phone call to customer from Agent": "<PERSON><PERSON>er un appel téléphonique vers le client à partir de l'Agent", "Fetch Call details by Call ID": "Récupérer les détails de l'appel par ID d'appel", "Schedule a call with an agent": "Planifier un appel avec un agent", "Fetch the transcript of a call by Call ID": "Récupérer la transcription d'un appel par ID d'appel", "Make a custom API call to a specific endpoint": "Passez un appel API personnalisé à un point de terminaison spécifique", "Agent ID": "ID de l'agent", "To Phone Number": "Vers le numéro de téléphone", "Details": "Détails du produit", "Call ID": "ID de l'appel", "Phone Number": "Numéro de téléphone", "Execution Time": "Temps d'Exécution", "Timezone": "Timezone", "Prospect Details": "<PERSON><PERSON><PERSON> du prospect", "Method": "Méthode", "Headers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Query Parameters": "Paramètres de requête", "Body": "Corps", "No Error on Failure": "Aucune erreur en cas d'échec", "Timeout (in seconds)": "<PERSON><PERSON><PERSON>atten<PERSON> (en secondes)", "Enter the phone number, along with country code, in format (e.g., +919876543210)": "Entrez le numéro de téléphone, ainsi que l'indicatif du pays, au format (par exemple, +919876543210)", "Optional details with key-value pairs (e.g., customer_id, priority)": "Détails optionnels avec des paires clé-valeur (ex : customer_id, priorité)", "Time to schedule the call in YYYY-MM-DD HH:MM:SS format.": "Heure pour programmer l'appel au format AAAA-MM-JJ HH:MM:SS.", "Timezone of the scheduled call (e.g., Asia/Calcutta)": "<PERSON>seau horaire de l'appel programmé (par exemple, Asie/Calcutta)", "Optional prospect details with key-value pairs (e.g., customer_id, priority)": "Détails du prospect optionnels avec des paires clé-valeur (ex : customer_id, priorité)", "Authorization headers are injected automatically from your connection.": "Les en-têtes d'autorisation sont injectés automatiquement à partir de votre connexion.", "GET": "OBTENIR", "POST": "POSTER", "PATCH": "PATCH", "PUT": "EFFACER", "DELETE": "SUPPRIMER", "HEAD": "TÊTE"}
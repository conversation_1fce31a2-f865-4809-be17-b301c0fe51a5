{"AI Answer": "AI Answer", "\n      To obtain your AiAnswer API access token, follow these steps below:\n      1. Log in to your AiAnswer account at https://app.aianswer.us .\n      2. Navigate to Settings < API Key.\n      3. Click on Copy icon to copy your existing Key or click on New API Key to create a new one.\n      4. Copy the API Key and paste it below in \"AiAnswer API Key\".\n    ": "\n      To obtain your AiAnswer API access token, follow these steps below:\n      1. Log in to your AiAnswer account at https://app.aianswer.us .\n      2. Navigate to Settings < API Key.\n      3. Click on Copy icon to copy your existing Key or click on New API Key to create a new one.\n      4. Copy the API Key and paste it below in \"AiAnswer API Key\".\n    ", "Gmail get list of Agents": "Gmail get list of Agents", "Create Phone Call": "Create Phone Call", "Get Call Details": "Get Call Details", "Schedule Call Agent": "Schedule Call Agent", "Get Call Transcript": "Get Call Transcript", "Custom API Call": "Custom API Call", "get the lists of agents with Gmail": "get the lists of agents with Gmail", "Create a phone call to customer from Agent": "Create a phone call to customer from Agent", "Fetch Call details by Call ID": "Fetch Call details by Call ID", "Schedule a call with an agent": "Schedule a call with an agent", "Fetch the transcript of a call by Call ID": "Fetch the transcript of a call by Call ID", "Make a custom API call to a specific endpoint": "Make a custom API call to a specific endpoint", "Agent ID": "Agent ID", "To Phone Number": "To Phone Number", "Details": "Details", "Call ID": "Call ID", "Phone Number": "Phone Number", "Execution Time": "Execution Time", "Timezone": "Timezone", "Prospect Details": "Prospect Details", "Method": "Method", "Headers": "Headers", "Query Parameters": "Query Parameters", "Body": "Body", "No Error on Failure": "No Error on Failure", "Timeout (in seconds)": "Timeout (in seconds)", "Enter the phone number, along with country code, in format (e.g., +919876543210)": "Enter the phone number, along with country code, in format (e.g., +919876543210)", "Optional details with key-value pairs (e.g., customer_id, priority)": "Optional details with key-value pairs (e.g., customer_id, priority)", "Time to schedule the call in YYYY-MM-DD HH:MM:SS format.": "Time to schedule the call in YYYY-MM-DD HH:MM:SS format.", "Timezone of the scheduled call (e.g., Asia/Calcutta)": "Timezone of the scheduled call (e.g., Asia/Calcutta)", "Optional prospect details with key-value pairs (e.g., customer_id, priority)": "Optional prospect details with key-value pairs (e.g., customer_id, priority)", "Authorization headers are injected automatically from your connection.": "Authorization headers are injected automatically from your connection.", "GET": "GET", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "DELETE", "HEAD": "HEAD"}
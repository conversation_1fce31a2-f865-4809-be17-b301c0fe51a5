{"AI Answer": "KI Antwort", "\n      To obtain your AiAnswer API access token, follow these steps below:\n      1. Log in to your AiAnswer account at https://app.aianswer.us .\n      2. Navigate to Settings < API Key.\n      3. Click on Copy icon to copy your existing Key or click on New API Key to create a new one.\n      4. Copy the API Key and paste it below in \"AiAnswer API Key\".\n    ": "\n      Um dein AiAnswer API Zugangs-Token zu erhalten, folgen Sie den folgenden Schritten:\n      1. Melden Sie sich bei Ihrem AiAnswer-Ko<PERSON> unter https://app.aianswer.us an.\n      2. Navigieren Sie zu Einstellungen < API Key.\n      3. Klicken Sie auf das Symbol kopieren, um Ihren vorhandenen Schlüssel zu kopieren oder klicken Sie auf den neuen API-Schlüssel, um einen neuen zu erstellen.\n      Kopieren Sie den API-Schlüssel und fügen Sie ihn unten in \"AiAnswer API Key\" ein.\n    ", "Gmail get list of Agents": "Gmail get <PERSON><PERSON> der Agenten", "Create Phone Call": "<PERSON><PERSON><PERSON>", "Get Call Details": "Anrufdetails abrufen", "Schedule Call Agent": "Anruf-Agent planen", "Get Call Transcript": "Anrufprotokoll erhalten", "Custom API Call": "Eigener API-Aufruf", "get the lists of agents with Gmail": "erhalte die Listen von Agenten mit Google Mail", "Create a phone call to customer from Agent": "<PERSON><PERSON><PERSON> an Kunden vom Agenten erstellen", "Fetch Call details by Call ID": "Rufdetails per Anrufnummer abrufen", "Schedule a call with an agent": "<PERSON>en Anruf mit einem Agenten planen", "Fetch the transcript of a call by Call ID": "Abrufen des Transkripts eines Anruf-ID", "Make a custom API call to a specific endpoint": "Einen benutzerdefinierten API-Aufruf an einen bestimmten Endpunkt machen", "Agent ID": "Agenten-<PERSON>", "To Phone Number": "An Telefonnummer", "Details": "Details", "Call ID": "Anruf-ID", "Phone Number": "Telefonnummer", "Execution Time": "Ausführungszeit", "Timezone": "Timezone", "Prospect Details": "Details prüfen", "Method": "<PERSON>e", "Headers": "Kopfzeilen", "Query Parameters": "Abfrageparameter", "Body": "<PERSON><PERSON><PERSON><PERSON>", "No Error on Failure": "<PERSON><PERSON> bei <PERSON>hler", "Timeout (in seconds)": "Timeout (in Sekunden)", "Enter the phone number, along with country code, in format (e.g., +919876543210)": "Geben Sie die Telefonnummer zusammen mit Landesvorwahl im Format ein (z.B. +919876543210)", "Optional details with key-value pairs (e.g., customer_id, priority)": "Optionale Details mit Schlüssel-Wert-Paaren (z.B. customer_id, Priorität)", "Time to schedule the call in YYYY-MM-DD HH:MM:SS format.": "Z<PERSON>, den Aufruf im Format JJJJJ-MM-TT HH:MM:SS zu planen.", "Timezone of the scheduled call (e.g., Asia/Calcutta)": "Zeitzone des geplanten Anrufs (z. B. Asien/Calcutta)", "Optional prospect details with key-value pairs (e.g., customer_id, priority)": "Optionale Details mit Schlüsselwert-Paaren (z.B. customer_id, priority)", "Authorization headers are injected automatically from your connection.": "Autorisierungs-Header werden automatisch von Ihrer Verbindung injiziert.", "GET": "ERHALTEN", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "LÖSCHEN", "HEAD": "HEAD"}
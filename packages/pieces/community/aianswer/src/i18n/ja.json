{"AI Answer": "AIの回答", "\n      To obtain your AiAnswer API access token, follow these steps below:\n      1. Log in to your AiAnswer account at https://app.aianswer.us .\n      2. Navigate to Settings < API Key.\n      3. Click on Copy icon to copy your existing Key or click on New API Key to create a new one.\n      4. Copy the API Key and paste it below in \"AiAnswer API Key\".\n    ": "\n      To obtain your AiAnswer API access token, follow these steps below:\n      1. Log in to your AiAnswer account at https://app.aianswer.us .\n      2. Navigate to Settings < API Key.\n      3. Click on Copy icon to copy your existing Key or click on New API Key to create a new one.\n      4. Copy the API Key and paste it below in \"AiAnswer API Key\".\n    ", "Gmail get list of Agents": "Gmail のエージェント一覧", "Create Phone Call": "電話番号を作成", "Get Call Details": "通話の詳細を取得", "Schedule Call Agent": "通話エージェントをスケジュールする", "Get Call Transcript": "通話のトランスクリプトを取得", "Custom API Call": "カスタムAPI通話", "get the lists of agents with Gmail": "Gmail のエージェントリストを取得する", "Create a phone call to customer from Agent": "エージェントからお客様に電話をかけます", "Fetch Call details by Call ID": "通話IDで通話の詳細を取得", "Schedule a call with an agent": "エージェントとの通話のスケジュール", "Fetch the transcript of a call by Call ID": "Call IDによる通話記録を取得", "Make a custom API call to a specific endpoint": "特定のエンドポイントへのカスタム API コールを実行します。", "Agent ID": "エージェントID", "To Phone Number": "電話番号へ", "Details": "詳細", "Call ID": "通話ID", "Phone Number": "電話番号", "Execution Time": "実行時間", "Timezone": "Timezone", "Prospect Details": "見込み客の詳細", "Method": "方法", "Headers": "ヘッダー", "Query Parameters": "クエリパラメータ", "Body": "本文", "No Error on Failure": "失敗時にエラーはありません", "Timeout (in seconds)": "タイムアウト（秒）", "Enter the phone number, along with country code, in format (e.g., +919876543210)": "電話番号と国コードを入力してください（例：+919876543210）", "Optional details with key-value pairs (e.g., customer_id, priority)": "キーと値のペアを持つオプションの詳細 (例: customer_id, priority)", "Time to schedule the call in YYYY-MM-DD HH:MM:SS format.": "YYYY-MM-DD HH:MM:SS 形式で通話をスケジュールする時間です。", "Timezone of the scheduled call (e.g., Asia/Calcutta)": "スケジュールされた通話のタイムゾーン（例：アジア/カルカッタ）", "Optional prospect details with key-value pairs (e.g., customer_id, priority)": "キーと値のペアを持つオプションの見込み客の詳細 (例: customer_id, priority)", "Authorization headers are injected automatically from your connection.": "認証ヘッダは接続から自動的に注入されます。", "GET": "取得", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "削除", "HEAD": "頭"}
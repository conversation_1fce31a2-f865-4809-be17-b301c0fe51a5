{"AI Answer": "AI Answer", "\n      To obtain your AiAnswer API access token, follow these steps below:\n      1. Log in to your AiAnswer account at https://app.aianswer.us .\n      2. Navigate to Settings < API Key.\n      3. Click on Copy icon to copy your existing Key or click on New API Key to create a new one.\n      4. Copy the API Key and paste it below in \"AiAnswer API Key\".\n    ": "\n      To obtain your AiAnswer API access token, follow these steps below:\n      1. Log in to your AiAnswer account at https://app.aianswer.us .\n      2. Navigate to Settings < API Key.\n      3. Click on Copy icon to copy your existing Key or click on New API Key to create a new one.\n      4. Copy the API Key and paste it below in \"AiAnswer API Key\".\n    ", "Gmail get list of Agents": "Gmail get list of Agents", "Create Phone Call": "Create Phone Call", "Get Call Details": "Get Call Details", "Schedule Call Agent": "Schedule Call Agent", "Get Call Transcript": "Get Call Transcript", "Custom API Call": "自定义 API 呼叫", "get the lists of agents with Gmail": "get the lists of agents with Gmail", "Create a phone call to customer from Agent": "Create a phone call to customer from Agent", "Fetch Call details by Call ID": "Fetch Call details by Call ID", "Schedule a call with an agent": "Schedule a call with an agent", "Fetch the transcript of a call by Call ID": "Fetch the transcript of a call by Call ID", "Make a custom API call to a specific endpoint": "将一个自定义 API 调用到一个特定的终点", "Agent ID": "Agent ID", "To Phone Number": "To Phone Number", "Details": "详细信息", "Call ID": "Call ID", "Phone Number": "Phone Number", "Execution Time": "Execution Time", "Timezone": "Timezone", "Prospect Details": "Prospect Details", "Method": "方法", "Headers": "信头", "Query Parameters": "查询参数", "Body": "正文内容", "No Error on Failure": "失败时没有错误", "Timeout (in seconds)": "超时(秒)", "Enter the phone number, along with country code, in format (e.g., +919876543210)": "Enter the phone number, along with country code, in format (e.g., +919876543210)", "Optional details with key-value pairs (e.g., customer_id, priority)": "Optional details with key-value pairs (e.g., customer_id, priority)", "Time to schedule the call in YYYY-MM-DD HH:MM:SS format.": "Time to schedule the call in YYYY-MM-DD HH:MM:SS format.", "Timezone of the scheduled call (e.g., Asia/Calcutta)": "Timezone of the scheduled call (e.g., Asia/Calcutta)", "Optional prospect details with key-value pairs (e.g., customer_id, priority)": "Optional prospect details with key-value pairs (e.g., customer_id, priority)", "Authorization headers are injected automatically from your connection.": "授权头自动从您的连接中注入。", "GET": "获取", "POST": "帖子", "PATCH": "PATCH", "PUT": "弹出", "DELETE": "删除", "HEAD": "黑色"}
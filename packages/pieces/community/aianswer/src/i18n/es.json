{"AI Answer": "Respuesta IA", "\n      To obtain your AiAnswer API access token, follow these steps below:\n      1. Log in to your AiAnswer account at https://app.aianswer.us .\n      2. Navigate to Settings < API Key.\n      3. Click on Copy icon to copy your existing Key or click on New API Key to create a new one.\n      4. Copy the API Key and paste it below in \"AiAnswer API Key\".\n    ": "\n      To obtain your AiAnswer API access token, follow these steps below:\n      1. Log in to your AiAnswer account at https://app.aianswer.us .\n      2. Navigate to Settings < API Key.\n      3. Click on Copy icon to copy your existing Key or click on New API Key to create a new one.\n      4. Copy the API Key and paste it below in \"AiAnswer API Key\".\n    ", "Gmail get list of Agents": "Gmail obtener lista de agentes", "Create Phone Call": "<PERSON><PERSON><PERSON> llamada telefónica", "Get Call Details": "Detalles de llamada", "Schedule Call Agent": "Agente de llamada programada", "Get Call Transcript": "Obtener transcripción de llamada", "Custom API Call": "Llamada API personalizada", "get the lists of agents with Gmail": "obtener las listas de agentes con Gmail", "Create a phone call to customer from Agent": "<PERSON><PERSON>r una llamada telefónica al cliente desde el agente", "Fetch Call details by Call ID": "Obtener detalles de la llamada por ID", "Schedule a call with an agent": "Programar una llamada con un agente", "Fetch the transcript of a call by Call ID": "Obtener la transcripción de una llamada por ID de llamada", "Make a custom API call to a specific endpoint": "Hacer una llamada API personalizada a un extremo específico", "Agent ID": "ID del agente", "To Phone Number": "A número de teléfono", "Details": "Detalles", "Call ID": "ID de llamada", "Phone Number": "Número de teléfono", "Execution Time": "Tiempo de ejecución", "Timezone": "Timezone", "Prospect Details": "Detalles del candidato", "Method": "<PERSON><PERSON><PERSON><PERSON>", "Headers": "Encabezados", "Query Parameters": "Parámetros de consulta", "Body": "<PERSON><PERSON><PERSON>", "No Error on Failure": "No hay ningún error en fallo", "Timeout (in seconds)": "Tiempo de espera (en segundos)", "Enter the phone number, along with country code, in format (e.g., +919876543210)": "Introduzca el número de teléfono, junto con el código del país, en formato (por ejemplo, +919876543210)", "Optional details with key-value pairs (e.g., customer_id, priority)": "Detalles opcionales con pares clave-valor (por ejemplo, customer_id, prioridad)", "Time to schedule the call in YYYY-MM-DD HH:MM:SS format.": "Hora de programar la llamada en formato AAA-MM-DD HH:MM:SS.", "Timezone of the scheduled call (e.g., Asia/Calcutta)": "Zona horaria de la llamada programada (por ejemplo, Asia/Calcutta)", "Optional prospect details with key-value pairs (e.g., customer_id, priority)": "Detalles opcionales de perspectiva con pares clave-valor (por ejemplo, customer_id, prioridad)", "Authorization headers are injected automatically from your connection.": "Las cabeceras de autorización se inyectan automáticamente desde tu conexión.", "GET": "RECOGER", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "BORRAR", "HEAD": "LIMPIO"}
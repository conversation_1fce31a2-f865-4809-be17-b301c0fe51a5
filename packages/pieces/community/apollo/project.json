{"name": "pieces-a<PERSON>lo", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/pieces/community/apollo/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/pieces/community/apollo", "tsConfig": "packages/pieces/community/apollo/tsconfig.lib.json", "packageJson": "packages/pieces/community/apollo/package.json", "main": "packages/pieces/community/apollo/src/index.ts", "assets": ["packages/pieces/community/apollo/*.md", {"input": "packages/pieces/community/apollo/src/i18n", "output": "./src/i18n", "glob": "**/!(i18n.json)"}], "buildableProjectDepsInPackageJsonType": "dependencies", "updateBuildableProjectDepsInPackageJson": true}}, "publish": {"command": "node tools/scripts/publish.mjs pieces-apollo {args.ver} {args.tag}", "dependsOn": ["build"]}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}, "tags": []}
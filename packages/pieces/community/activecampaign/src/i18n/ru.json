{"ActiveCampaign": "Активная Кампания", "Email marketing, marketing automation, and CRM tools you need to create incredible customer experiences.": "Электронный маркетинг, маркетинговая автоматизация и CRM инструменты, необходимые для создания невероятных впечатлений от клиентов.", "API URL": "API URL", "API Key": "Ключ API", "\nTo obtain your ActiveCampaign API URL and Key, follow these steps:\n\n1. Log in to your ActiveCampaign account.\n2. Navigate to **Settings->Developer** section.\n3. Under **API Access** ,you'll find your API URL and Key.\n": "\nTo obtain your ActiveCampaign API URL and Key, follow these steps:\n\n1. Log in to your ActiveCampaign account.\n2. Navigate to **Settings->Developer** section.\n3. Under **API Access** ,you'll find your API URL and Key.\n", "Add Contact to Account": "Добавить контакт в аккаунт", "Add Tag to Contact": "Добавить тег в контакт", "Create Account": "Создать Аккаунт", "Create Contact": "Создать контакт", "Update Account": "Обновить аккаунт", "Update Contact": "Обновить контакт", "Subscribe or Unsubscribe Contact From List": "Подписаться или Отписаться от контакта из списка", "Adds a contact to an ActiveCampaign account.": "Добавля<PERSON>т контакт с учетной записью Activeкампании.", "Adds a tag to contact.": "Добавляет тег контакту.", "Creates a new account.": "Создать новую учетную запись.", "Creates a new contact.": "Создает новый контакт.", "Updates an account.": "Обновление учетной записи.", "Updates an existing contact.": "Обновляет существующий контакт.", "Subscribes a Contact to a List it is not currently associated with, or Unsubscribes a Contact from a list is currently associated with.": "Подписаться на контакт из списка, с которым он в настоящее время не связан или отписаться от контакта из списка в настоящее время связан с.", "Contact ID": "ID контакта", "Account ID": "ID клиента", "Job Title": "Заголовок", "Tag ID": "Tag ID", "Account Name": "Имя аккаунта", "Account URL": "URL аккаунта", "Account Custom Fields": "Пользовательские поля клиента", "Email": "Почта", "First Name": "First Name", "Last Name": "Last Name", "Phone": "Телефон", "Contact Custom Fields": "Пользовательские поля контакта", "List": "Список", "Action": "Действие", "Subscribe": "Подписаться", "Unsubscribe": "Отписаться", "Deal Task Completed": "Задача на сделку выполнена", "New Contact Note": "Новая контактная заметка", "New Contact Task": "Новая задача Контакта", "New Deal Added or Updated": "Новая сделка добавлена или обновлена", "New or Updated Account": "Новый или обновленный аккаунт", "New Deal Note": "Новая заметка о сделке", "New Deal Task": "Новая сделка", "Tag Added or Removed From Contact": "Тег добавлен или удален из контакта", "Updated Contact": "Контакт обновлен", "Triggers when a deal task has been completed.": "Триггеры по завершении сделки.", "Triggers when a new contact note is added.": "Включает при добавлении новой контактной заметки.", "Triggers when a new contact task is added.": "Триггеры при добавлении новой задачи контакта.", "Triggers when a new deal is created or existing deal is updated.": "Триггеры при создании новой сделки или обновлении существующей сделки.", "Triggers when a new account is added or an existing account’s details are updated": "Триггеры при добавлении новой учетной записи или обновлении данных существующего аккаунта", "Triggers when a new deal note is created.": "Триггеры при создании новой заметки о сделке.", "Triggers when a new deal task is created.": "Триггеры при создании новой сделки.", "Triggers when a a Tag is added or removed from a Contact": "Триггеры при добавлении или удалении тега из контакта", "Triggers when an existing contact details are updated.": "Включает при обновлении существующих контактных данных."}
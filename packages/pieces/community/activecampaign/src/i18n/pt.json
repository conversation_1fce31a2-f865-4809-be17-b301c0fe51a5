{"ActiveCampaign": "<PERSON>an<PERSON>", "Email marketing, marketing automation, and CRM tools you need to create incredible customer experiences.": "Marketing por e-mail, automação de marketing e ferramentas de CRM que você precisa para criar experiências incríveis para o cliente.", "API URL": "API URL", "API Key": "Chave de <PERSON>", "\nTo obtain your ActiveCampaign API URL and Key, follow these steps:\n\n1. Log in to your ActiveCampaign account.\n2. Navigate to **Settings->Developer** section.\n3. Under **API Access** ,you'll find your API URL and Key.\n": "\nTo obtain your ActiveCampaign API URL and Key, follow these steps:\n\n1. Log in to your ActiveCampaign account.\n2. Navigate to **Settings->Developer** section.\n3. Under **API Access** ,you'll find your API URL and Key.\n", "Add Contact to Account": "Adicionar contato a conta", "Add Tag to Contact": "Adicionar Tag ao Contato", "Create Account": "C<PERSON><PERSON> conta", "Create Contact": "<PERSON><PERSON><PERSON> contato", "Update Account": "<PERSON><PERSON><PERSON>r conta", "Update Contact": "At<PERSON><PERSON>r contato", "Subscribe or Unsubscribe Contact From List": "Inscrever-se ou Cancelar Contato na Lista de Contatos", "Adds a contact to an ActiveCampaign account.": "Adiciona um contato a uma conta ActiveCampaign", "Adds a tag to contact.": "Adiciona uma etiqueta para contato.", "Creates a new account.": "Cria uma nova conta.", "Creates a new contact.": "Cria um novo contato.", "Updates an account.": "Atualiza uma conta.", "Updates an existing contact.": "Atualiza um contato existente.", "Subscribes a Contact to a List it is not currently associated with, or Unsubscribes a Contact from a list is currently associated with.": "Assinar um Contato a uma Lista com quem ele não está atualmente associado, ou cancelar a assinatura de um Contato a partir de uma lista está atualmente associado.", "Contact ID": "ID do contato", "Account ID": "ID da Conta", "Job Title": "Título do Cargo", "Tag ID": "Tag ID", "Account Name": "<PERSON>me da Conta", "Account URL": "URL da Conta", "Account Custom Fields": "Campos personalizados de clientes", "Email": "e-mail", "First Name": "Nome", "Last Name": "Sobrenome", "Phone": "Smartphone", "Contact Custom Fields": "Campos personalizados de contato", "List": "Lista", "Action": "A<PERSON>ão", "Subscribe": "Inscrever-se", "Unsubscribe": "Desinscrever", "Deal Task Completed": "Tarefa de negócio concluída", "New Contact Note": "Nova Nota de Contato", "New Contact Task": "Nova tarefa de contato", "New Deal Added or Updated": "Nova Oferta adicionada ou atualizada", "New or Updated Account": "Conta nova ou atualizada", "New Deal Note": "Nova Nota de Oferta", "New Deal Task": "Nova tarefa de negócio", "Tag Added or Removed From Contact": "Tag adicionada ou removida do contato", "Updated Contact": "Contato atualizado", "Triggers when a deal task has been completed.": "Dispara quando uma tarefa de negócio for concluída.", "Triggers when a new contact note is added.": "Aciona quando uma nova nota de contato é adicionada.", "Triggers when a new contact task is added.": "Aciona quando uma nova tarefa de contato é adicionada.", "Triggers when a new deal is created or existing deal is updated.": "Aciona quando uma nova operação é criada ou uma operação existente é atualizada.", "Triggers when a new account is added or an existing account’s details are updated": "Dispara quando uma nova conta é adicionada ou os detalhes de uma conta existente são atualizados", "Triggers when a new deal note is created.": "Aciona quando uma nova nota de negócio é criada.", "Triggers when a new deal task is created.": "Aciona quando uma nova tarefa de negócio é criada.", "Triggers when a a Tag is added or removed from a Contact": "Aciona quando uma Tag é adicionada ou removida de um Contato", "Triggers when an existing contact details are updated.": "Aciona quando detalhes de contato existentes são atualizados."}
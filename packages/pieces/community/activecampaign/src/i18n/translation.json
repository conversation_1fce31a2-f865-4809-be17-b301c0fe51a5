{"ActiveCampaign": "ActiveCampaign", "Email marketing, marketing automation, and CRM tools you need to create incredible customer experiences.": "Email marketing, marketing automation, and CRM tools you need to create incredible customer experiences.", "API URL": "API URL", "API Key": "API Key", "\nTo obtain your ActiveCampaign API URL and Key, follow these steps:\n\n1. Log in to your ActiveCampaign account.\n2. Navigate to **Settings->Developer** section.\n3. Under **API Access** ,you'll find your API URL and Key.\n": "\nTo obtain your ActiveCampaign API URL and Key, follow these steps:\n\n1. Log in to your ActiveCampaign account.\n2. Navigate to **Settings->Developer** section.\n3. Under **API Access** ,you'll find your API URL and Key.\n", "Add Contact to Account": "Add Contact to Account", "Add Tag to Contact": "Add Tag to Contact", "Create Account": "Create Account", "Create Contact": "Create Contact", "Update Account": "Update Account", "Update Contact": "Update Contact", "Subscribe or Unsubscribe Contact From List": "Subscribe or Unsubscribe Contact From List", "Adds a contact to an ActiveCampaign account.": "Adds a contact to an ActiveCampaign account.", "Adds a tag to contact.": "Adds a tag to contact.", "Creates a new account.": "Creates a new account.", "Creates a new contact.": "Creates a new contact.", "Updates an account.": "Updates an account.", "Updates an existing contact.": "Updates an existing contact.", "Subscribes a Contact to a List it is not currently associated with, or Unsubscribes a Contact from a list is currently associated with.": "Subscribes a Contact to a List it is not currently associated with, or Unsubscribes a Contact from a list is currently associated with.", "Contact ID": "Contact ID", "Account ID": "Account ID", "Job Title": "Job Title", "Tag ID": "Tag ID", "Account Name": "Account Name", "Account URL": "Account URL", "Account Custom Fields": "Account Custom Fields", "Email": "Email", "First Name": "First Name", "Last Name": "Last Name", "Phone": "Phone", "Contact Custom Fields": "Contact Custom Fields", "List": "List", "Action": "Action", "Subscribe": "Subscribe", "Unsubscribe": "Unsubscribe", "Deal Task Completed": "Deal Task Completed", "New Contact Note": "New Contact Note", "New Contact Task": "New Contact Task", "New Deal Added or Updated": "New Deal Added or Updated", "New or Updated Account": "New or Updated Account", "New Deal Note": "New Deal Note", "New Deal Task": "New Deal Task", "Tag Added or Removed From Contact": "Tag Added or Removed From Contact", "Updated Contact": "Updated Contact", "Triggers when a deal task has been completed.": "Triggers when a deal task has been completed.", "Triggers when a new contact note is added.": "Triggers when a new contact note is added.", "Triggers when a new contact task is added.": "Triggers when a new contact task is added.", "Triggers when a new deal is created or existing deal is updated.": "Triggers when a new deal is created or existing deal is updated.", "Triggers when a new account is added or an existing account’s details are updated": "Triggers when a new account is added or an existing account’s details are updated", "Triggers when a new deal note is created.": "Triggers when a new deal note is created.", "Triggers when a new deal task is created.": "Triggers when a new deal task is created.", "Triggers when a a Tag is added or removed from a Contact": "Triggers when a a Tag is added or removed from a Contact", "Triggers when an existing contact details are updated.": "Triggers when an existing contact details are updated."}
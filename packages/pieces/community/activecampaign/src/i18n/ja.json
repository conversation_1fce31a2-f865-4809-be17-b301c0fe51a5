{"ActiveCampaign": "アクティブキャンペーン", "Email marketing, marketing automation, and CRM tools you need to create incredible customer experiences.": "メールマーケティング、マーケティングオートメーション、CRMツールを活用することで、驚くほどのカスタマーエクスペリエンスを実現できます。", "API URL": "API URL", "API Key": "API キー", "\nTo obtain your ActiveCampaign API URL and Key, follow these steps:\n\n1. Log in to your ActiveCampaign account.\n2. Navigate to **Settings->Developer** section.\n3. Under **API Access** ,you'll find your API URL and Key.\n": "\nTo obtain your ActiveCampaign API URL and Key, follow these steps:\n\n1. Log in to your ActiveCampaign account.\n2. Navigate to **Settings->Developer** section.\n3. Under **API Access** ,you'll find your API URL and Key.\n", "Add Contact to Account": "アカウントに連絡先を追加", "Add Tag to Contact": "連絡先にタグを追加", "Create Account": "アカウントを作成", "Create Contact": "連絡先を作成", "Update Account": "アカウントを更新", "Update Contact": "連絡先を更新", "Subscribe or Unsubscribe Contact From List": "一覧から連絡先を購読または購読解除する", "Adds a contact to an ActiveCampaign account.": "ActiveCampaignアカウントに連絡先を追加します。", "Adds a tag to contact.": "連絡先にタグを追加", "Creates a new account.": "新しいアカウントを作成します。", "Creates a new contact.": "新しい連絡先を作成します。", "Updates an account.": "アカウントを更新する。", "Updates an existing contact.": "既存のコンタクトを更新します。", "Subscribes a Contact to a List it is not currently associated with, or Unsubscribes a Contact from a list is currently associated with.": "現在関連付けられていないリストに連絡先を登録したり、リストから連絡先を登録解除したりします。", "Contact ID": "連絡先ID", "Account ID": "アカウントID", "Job Title": "役職名", "Tag ID": "Tag ID", "Account Name": "口座名", "Account URL": "アカウントのURL", "Account Custom Fields": "アカウントのカスタムフィールド", "Email": "Eメールアドレス", "First Name": "名", "Last Name": "Last Name", "Phone": "電話番号", "Contact Custom Fields": "連絡のカスタムフィールド", "List": "リスト", "Action": "アクション", "Subscribe": "購読する", "Unsubscribe": "購読解除", "Deal Task Completed": "タスクを処理しました", "New Contact Note": "新しい連絡先メモ", "New Contact Task": "新しい連絡先タスク", "New Deal Added or Updated": "新しい取引が追加または更新されました", "New or Updated Account": "新規または更新されたアカウント", "New Deal Note": "新しい取引メモ", "New Deal Task": "新しい取引タスク", "Tag Added or Removed From Contact": "連絡先からタグが追加または削除されました", "Updated Contact": "連絡先の更新", "Triggers when a deal task has been completed.": "ディールタスクが完了したときに発生します。", "Triggers when a new contact note is added.": "新しい連絡先ノートが追加されたときにトリガーされます。", "Triggers when a new contact task is added.": "新しい連絡先タスクが追加されたときにトリガーします。", "Triggers when a new deal is created or existing deal is updated.": "新しい取引が作成されたり、既存の取引が更新されたときに発生します。", "Triggers when a new account is added or an existing account’s details are updated": "新しいアカウントが追加された時、または既存のアカウントの詳細が更新されたときにトリガーされます。", "Triggers when a new deal note is created.": "新しいディールノートが作成されたときにトリガーされます。", "Triggers when a new deal task is created.": "新しい取引タスクが作成されたときにトリガーされます。", "Triggers when a a Tag is added or removed from a Contact": "連絡先からタグが追加または削除されたときにトリガーします", "Triggers when an existing contact details are updated.": "既存の連絡先詳細が更新されたときにトリガーされます。"}
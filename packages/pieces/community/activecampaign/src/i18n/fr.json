{"ActiveCampaign": "Campagne active", "Email marketing, marketing automation, and CRM tools you need to create incredible customer experiences.": "Le marketing par courriel, l'automatisation du marketing et les outils CRM dont vous avez besoin pour créer des expériences clients incroyables.", "API URL": "API URL", "API Key": "Clé API", "\nTo obtain your ActiveCampaign API URL and Key, follow these steps:\n\n1. Log in to your ActiveCampaign account.\n2. Navigate to **Settings->Developer** section.\n3. Under **API Access** ,you'll find your API URL and Key.\n": "\nTo obtain your ActiveCampaign API URL and Key, follow these steps:\n\n1. Log in to your ActiveCampaign account.\n2. Navigate to **Settings->Developer** section.\n3. Under **API Access** ,you'll find your API URL and Key.\n", "Add Contact to Account": "Ajouter un contact au compte", "Add Tag to Contact": "Ajouter un tag au contact", "Create Account": "<PERSON><PERSON><PERSON> un compte", "Create Contact": "<PERSON><PERSON><PERSON> un contact", "Update Account": "Mettre à jour le compte", "Update Contact": "Mettre à jour le contact", "Subscribe or Unsubscribe Contact From List": "<PERSON><PERSON><PERSON><PERSON>ner ou se désabonner de la liste des contacts", "Adds a contact to an ActiveCampaign account.": "Ajoute un contact à un compte ActiveCampagne.", "Adds a tag to contact.": "Ajoute un tag au contact.", "Creates a new account.": "Crée un nouveau compte.", "Creates a new contact.": "Crée un nouveau contact.", "Updates an account.": "Met à jour un compte.", "Updates an existing contact.": "Met à jour un contact existant.", "Subscribes a Contact to a List it is not currently associated with, or Unsubscribes a Contact from a list is currently associated with.": "Abonne un Contact à une Liste avec laquelle il n'est actuellement pas associé ou désabonne un Contact d'une liste est actuellement associé.", "Contact ID": "ID du contact", "Account ID": "ID du compte client", "Job Title": "Titre du poste", "Tag ID": "Tag ID", "Account Name": "Nom du compte", "Account URL": "URL du compte", "Account Custom Fields": "Champs personnalisés du cpte client", "Email": "<PERSON><PERSON><PERSON>", "First Name": "First Name", "Last Name": "Last Name", "Phone": "Téléphone", "Contact Custom Fields": "Champs personnalisés du contact", "List": "Liste", "Action": "Action", "Subscribe": "<PERSON>'abonner", "Unsubscribe": "<PERSON> d<PERSON>ab<PERSON>ner", "Deal Task Completed": "Tâche de transaction terminée", "New Contact Note": "Nouvelle note de contact", "New Contact Task": "Nouvelle tâche de contact", "New Deal Added or Updated": "Nouvelle offre ajoutée ou mise à jour", "New or Updated Account": "Nouveau ou mis à jour du compte", "New Deal Note": "Note sur la nouvelle offre", "New Deal Task": "Nouvelle tâche", "Tag Added or Removed From Contact": "Étiquette ajoutée ou retirée du contact", "Updated Contact": "Contact mis à jour", "Triggers when a deal task has been completed.": "Déclenche quand une tâche de transaction est terminée.", "Triggers when a new contact note is added.": "Déclenche quand une nouvelle note de contact est ajoutée.", "Triggers when a new contact task is added.": "Déclenche quand une nouvelle tâche de contact est ajoutée.", "Triggers when a new deal is created or existing deal is updated.": "Déclenche lorsqu'une nouvelle transaction est créée ou qu'une transaction existante est mise à jour.", "Triggers when a new account is added or an existing account’s details are updated": "Déclenche lorsqu'un nouveau compte est ajouté ou les coordonnées d'un compte existant sont mises à jour", "Triggers when a new deal note is created.": "<PERSON><PERSON><PERSON><PERSON><PERSON> quand une nouvelle note d'affaire est créée.", "Triggers when a new deal task is created.": "Déclenche quand une nouvelle tâche de transaction est créée.", "Triggers when a a Tag is added or removed from a Contact": "Déclenche lorsqu'un Tag est ajouté ou retiré d'un Contact", "Triggers when an existing contact details are updated.": "Déclenche lorsqu'un contact existant est mis à jour."}
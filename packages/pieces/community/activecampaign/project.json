{"name": "pieces-activecampaign", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/pieces/community/activecampaign/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/pieces/community/activecampaign", "tsConfig": "packages/pieces/community/activecampaign/tsconfig.lib.json", "packageJson": "packages/pieces/community/activecampaign/package.json", "main": "packages/pieces/community/activecampaign/src/index.ts", "assets": ["packages/pieces/community/activecampaign/*.md", {"input": "packages/pieces/community/activecampaign/src/i18n", "output": "./src/i18n", "glob": "**/!(i18n.json)"}], "buildableProjectDepsInPackageJsonType": "dependencies", "updateBuildableProjectDepsInPackageJson": true}}, "publish": {"command": "node tools/scripts/publish.mjs pieces-activecampaign {args.ver} {args.tag}", "dependsOn": ["build"]}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}, "tags": []}
{"Attio": "Аттио", "Modern, collaborative CRM platform built to be fully customizable and real-time.": "Современная, коллективная CRM платформа, созданная для полной настройки и реального времени.", "\nTo use Attio, you need to generate an Access Token:\n1. Login to your Attio account at https://app.attio.com.\n2. From the dropdown beside your workspace name, click Workspace settings.\n3. Click the Developers tab.\n4. Click on the \"New Access Token\" button.\n5. Set the appropriate Scopes for the integration.\n6. Copy the generated Access Token.\n": "\nДля использования Attio, вам нужно сгенерировать маркер доступа:\n1. Войдите в свою учетную запись Attio на https://app. ttio.com.\n2. Из выпадающего списка рядом с именем рабочей области нажмите кнопку настроек рабочей области.\n3. Нажмите на вкладку Разработчики.\n4. Нажмите на кнопку \"New Access Token\".\n5. Установите соответствующие области для интеграции.\n6. Скопируйте созданный маркер доступа.\n", "Create Record": "Создать запись", "Update Record": "Обновить запись", "Find Record": "Найти запись", "Create List Entry": "Создать запись в списке", "Update List Entry": "Обновить запись списка", "Find List Entry": "Найти запись из списка", "Custom API Call": "Пользовательский вызов API", "Creates a new record such as peron,company or deal.": "Создает новую запись, такую как перн, компания или сделка.", "Update an existing record with new attribute values.": "Обновить существующую запись с новыми значениями атрибута.", "Search for records in Attio using filters and return matching results.": "Поиск записей в Аттио с помощью фильтров и возврат результатов поиска.", "Add a record to a specified list.": "Добавить запись в указанный список.", "Update the attributes of an existing entry in a list.": "Обновить атрибуты существующей записи в списке.", "Search for entries in a specific list in Attio using filters and return matching results.": "Поиск записей в определенном списке в Аттио с использованием фильтров и результатов поиска.", "Make a custom API call to a specific endpoint": "Сделать пользовательский API вызов к определенной конечной точке", "Object": "Объект", "Object Attributes": "Атрибуты объекта", "Record ID": "ID записи", "List": "Список", "Parent Object": "Родительский объект", "Parent Record ID": "Родительская запись ID", "List Attributes": "Атрибуты списка", "Entry ID": "ID записи", "Method": "Метод", "Headers": "Заголовки", "Query Parameters": "Параметры запроса", "Body": "Тело", "No Error on Failure": "Нет ошибок при ошибке", "Timeout (in seconds)": "Таймаут (в секундах)", "The unique identifier of the record to update.": "Уникальный идентификатор для обновления.", "The unique identifier of the entry to update.": "Уникальный идентификатор записи для обновления.", "Authorization headers are injected automatically from your connection.": "Заголовки авторизации включаются автоматически из вашего соединения.", "GET": "ПОЛУЧИТЬ", "POST": "ПОСТ", "PATCH": "ПАТЧ", "PUT": "ПОКУПИТЬ", "DELETE": "УДАЛИТЬ", "HEAD": "HEAD", "Record Created": "Запись создана", "Record Updated": "Запись обновлена", "List Entry Created": "Запись создана", "List Entry Updated": "Запись рассылки обновлена", "Triggers when a new record such as person,company or deal is created.": "Триггеры при создании новой записи, такой как личность, компания или сделка.", "Triggers when an existing record is updated (people, companies, deals, etc.).": "Триггеры при обновлении существующей записи (люди, компании, сделки и т.д.).", "Triggers when a new entry is added.": "Триггеры при добавлении новой записи.", "Triggers when an existing entry is updated.": "Триггеры при обновлении существующей записи."}
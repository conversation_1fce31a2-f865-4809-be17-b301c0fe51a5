{"Attio": "<PERSON><PERSON><PERSON>", "Modern, collaborative CRM platform built to be fully customizable and real-time.": "Modern, gezamenlijk CRM-platform gebouwd om volledig aanpasbaar en real-time te zijn.", "\nTo use Attio, you need to generate an Access Token:\n1. Login to your Attio account at https://app.attio.com.\n2. From the dropdown beside your workspace name, click Workspace settings.\n3. Click the Developers tab.\n4. Click on the \"New Access Token\" button.\n5. Set the appropriate Scopes for the integration.\n6. Copy the generated Access Token.\n": "\nTo use Attio, you need to generate an Access Token:\n1. Login to your Attio account at https://app.attio.com.\n2. From the dropdown beside your workspace name, click Workspace settings.\n3. Click the Developers tab.\n4. Click on the \"New Access Token\" button.\n5. Set the appropriate Scopes for the integration.\n6. Copy the generated Access Token.\n", "Create Record": "Record Maken", "Update Record": "Update Record", "Find Record": "Vind Record", "Create List Entry": "Lijst invoer aanmaken", "Update List Entry": "Lijstitem bijwerken", "Find List Entry": "Lijstinvoer zoeken", "Custom API Call": "Custom API Call", "Creates a new record such as peron,company or deal.": "Maakt een nieuw record aan zoals peron,bedrijf of deal.", "Update an existing record with new attribute values.": "<PERSON><PERSON> bestaand record bij<PERSON><PERSON> met ni<PERSON><PERSON> attribuutwaarden.", "Search for records in Attio using filters and return matching results.": "Zoeken naar records in Attio met behulp van filters en resultaat overeenkomende resultaten.", "Add a record to a specified list.": "Voeg een record toe aan een opgegeven lijst.", "Update the attributes of an existing entry in a list.": "Werk de kenmerken van een bestaand item in een lijst bij.", "Search for entries in a specific list in Attio using filters and return matching results.": "Zoeken naar items in een specifieke lijst in Attio met behulp van filters en overeenkomende resultaten retourneren.", "Make a custom API call to a specific endpoint": "Maak een aangepaste API call naar een specifiek eindpunt", "Object": "Object", "Object Attributes": "Object Attributen", "Record ID": "Record ID", "List": "Klantenlijst", "Parent Object": "Bovenliggend object", "Parent Record ID": "Bovenliggende Record ID", "List Attributes": "Lijst kenmerken", "Entry ID": "Invoer ID", "Method": "<PERSON>e", "Headers": "Kopteksten", "Query Parameters": "Query parameters", "Body": "<PERSON><PERSON><PERSON>", "No Error on Failure": "<PERSON><PERSON> fout bij fout", "Timeout (in seconds)": "Time-out (in seconden)", "The unique identifier of the record to update.": "De unieke identifier van de bij te werken record", "The unique identifier of the entry to update.": "De unieke id van de bij te werken invoer.", "Authorization headers are injected automatically from your connection.": "Autorisatie headers worden automatisch geïnjecteerd vanuit uw verbinding.", "GET": "KRIJG", "POST": "POSTE", "PATCH": "BEKIJK", "PUT": "PUT", "DELETE": "VERWIJDEREN", "HEAD": "HOOFD", "Record Created": "Record gemaakt", "Record Updated": "Record bijgewerkt", "List Entry Created": "Lijst item aangemaakt", "List Entry Updated": "Lijst invoer bijgewerkt", "Triggers when a new record such as person,company or deal is created.": "<PERSON><PERSON>t wanneer een nieuw record zoals per<PERSON>jk, bedrijf of deal wordt gemaakt.", "Triggers when an existing record is updated (people, companies, deals, etc.).": "<PERSON><PERSON> wanneer een bestaand record wordt bijgewerkt (personen, bedrijven, deals, etc.).", "Triggers when a new entry is added.": "<PERSON>ggert wanneer een nieuwe invoer wordt toegevoegd.", "Triggers when an existing entry is updated.": "<PERSON>ggert wanneer een bestaande invoer wordt bijgewerkt."}
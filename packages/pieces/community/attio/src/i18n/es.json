{"Attio": "<PERSON><PERSON><PERSON>", "Modern, collaborative CRM platform built to be fully customizable and real-time.": "Plataforma CRM moderna y colaborativa construida para ser totalmente personalizable y en tiempo real.", "\nTo use Attio, you need to generate an Access Token:\n1. Login to your Attio account at https://app.attio.com.\n2. From the dropdown beside your workspace name, click Workspace settings.\n3. Click the Developers tab.\n4. Click on the \"New Access Token\" button.\n5. Set the appropriate Scopes for the integration.\n6. Copy the generated Access Token.\n": "\nTo use Attio, you need to generate an Access Token:\n1. Login to your Attio account at https://app.attio.com.\n2. From the dropdown beside your workspace name, click Workspace settings.\n3. Click the Developers tab.\n4. Click on the \"New Access Token\" button.\n5. Set the appropriate Scopes for the integration.\n6. Copy the generated Access Token.\n", "Create Record": "<PERSON><PERSON>r registro", "Update Record": "Actualizar registro", "Find Record": "Buscar registro", "Create List Entry": "<PERSON>rear entrada de lista", "Update List Entry": "Actualizar entrada de lista", "Find List Entry": "Buscar entrada de lista", "Custom API Call": "Llamada API personalizada", "Creates a new record such as peron,company or deal.": "Crea un nuevo registro como peron, empresa o negocio.", "Update an existing record with new attribute values.": "Actualizar un registro existente con nuevos valores de atributos.", "Search for records in Attio using filters and return matching results.": "Buscar registros en Attio usando filtros y devolver resultados coincidentes.", "Add a record to a specified list.": "Añadir un registro a una lista especificada.", "Update the attributes of an existing entry in a list.": "Actualizar los atributos de una entrada existente en una lista.", "Search for entries in a specific list in Attio using filters and return matching results.": "Buscar entradas en una lista específica en Attio usando filtros y devolver resultados coincidentes.", "Make a custom API call to a specific endpoint": "Hacer una llamada API personalizada a un extremo específico", "Object": "<PERSON><PERSON><PERSON><PERSON>", "Object Attributes": "Atributos del objeto", "Record ID": "ID de registro", "List": "Lista", "Parent Object": "Ob<PERSON>o padre", "Parent Record ID": "ID de registro padre", "List Attributes": "Atributos de lista", "Entry ID": "ID de entrada", "Method": "<PERSON><PERSON><PERSON><PERSON>", "Headers": "Encabezados", "Query Parameters": "Parámetros de consulta", "Body": "<PERSON><PERSON><PERSON>", "No Error on Failure": "No hay ningún error en fallo", "Timeout (in seconds)": "Tiempo de espera (en segundos)", "The unique identifier of the record to update.": "El identificador único del registro a actualizar.", "The unique identifier of the entry to update.": "El identificador único de la entrada a actualizar.", "Authorization headers are injected automatically from your connection.": "Las cabeceras de autorización se inyectan automáticamente desde tu conexión.", "GET": "RECOGER", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "BORRAR", "HEAD": "LIMPIO", "Record Created": "<PERSON><PERSON>", "Record Updated": "Registro actualizado", "List Entry Created": "Lista de entrada creada", "List Entry Updated": "Lista de entrada actualizada", "Triggers when a new record such as person,company or deal is created.": "Dispara cuando se crea un nuevo registro como persona, empresa o negocio.", "Triggers when an existing record is updated (people, companies, deals, etc.).": "Desencadena cuando se actualiza un registro existente (personas, empresas, operaciones, etc.).", "Triggers when a new entry is added.": "Se activa cuando se añade una nueva entrada.", "Triggers when an existing entry is updated.": "Se activa cuando se actualiza una entrada existente."}
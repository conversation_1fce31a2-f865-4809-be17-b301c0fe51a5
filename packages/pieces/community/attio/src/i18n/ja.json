{"Attio": "アティーオ", "Modern, collaborative CRM platform built to be fully customizable and real-time.": "最新の共同CRMプラットフォームは、完全にカスタマイズ可能でリアルタイムに構築されています。", "\nTo use Attio, you need to generate an Access Token:\n1. Login to your Attio account at https://app.attio.com.\n2. From the dropdown beside your workspace name, click Workspace settings.\n3. Click the Developers tab.\n4. Click on the \"New Access Token\" button.\n5. Set the appropriate Scopes for the integration.\n6. Copy the generated Access Token.\n": "\nTo use Attio, you need to generate an Access Token:\n1. Login to your Attio account at https://app.attio.com.\n2. From the dropdown beside your workspace name, click Workspace settings.\n3. Click the Developers tab.\n4. Click on the \"New Access Token\" button.\n5. Set the appropriate Scopes for the integration.\n6. Copy the generated Access Token.\n", "Create Record": "レコードを作成", "Update Record": "更新記録", "Find Record": "レコードを検索", "Create List Entry": "リストエントリを作成", "Update List Entry": "リストエントリを更新", "Find List Entry": "リストエントリを検索", "Custom API Call": "カスタムAPI通話", "Creates a new record such as peron,company or deal.": "peron、company、dealなどの新しいレコードを作成します。", "Update an existing record with new attribute values.": "新しい属性値を持つ既存のレコードを更新します。", "Search for records in Attio using filters and return matching results.": "フィルタを使用してAttioのレコードを検索し、一致する結果を返します。", "Add a record to a specified list.": "指定したリストにレコードを追加します。", "Update the attributes of an existing entry in a list.": "リスト内の既存のエントリの属性を更新します。", "Search for entries in a specific list in Attio using filters and return matching results.": "フィルタを使用してAttioの特定のリストのエントリを検索し、一致する結果を返します。", "Make a custom API call to a specific endpoint": "特定のエンドポイントへのカスタム API コールを実行します。", "Object": "オブジェクト", "Object Attributes": "オブジェクトの属性", "Record ID": "レコードID", "List": "リスト", "Parent Object": "親オブジェクト", "Parent Record ID": "親レコードID", "List Attributes": "リスト属性", "Entry ID": "エントリ ID", "Method": "方法", "Headers": "ヘッダー", "Query Parameters": "クエリパラメータ", "Body": "本文", "No Error on Failure": "失敗時にエラーはありません", "Timeout (in seconds)": "タイムアウト（秒）", "The unique identifier of the record to update.": "更新するレコードの一意の識別子", "The unique identifier of the entry to update.": "更新するエントリの一意の識別子", "Authorization headers are injected automatically from your connection.": "認証ヘッダは接続から自動的に注入されます。", "GET": "取得", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "削除", "HEAD": "頭", "Record Created": "レコードが作成されました", "Record Updated": "レコードが更新されました", "List Entry Created": "リストエントリが作成されました", "List Entry Updated": "リストエントリが更新されました", "Triggers when a new record such as person,company or deal is created.": "人、会社、取引などの新しいレコードが作成されたときにトリガーされます。", "Triggers when an existing record is updated (people, companies, deals, etc.).": "既存のレコードが更新されたときにトリガーされます (人、会社、取引など)。", "Triggers when a new entry is added.": "新しいエントリが追加されたときにトリガーします。", "Triggers when an existing entry is updated.": "既存のエントリが更新されたときにトリガーします。"}
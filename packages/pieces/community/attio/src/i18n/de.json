{"Attio": "<PERSON>", "Modern, collaborative CRM platform built to be fully customizable and real-time.": "Moderne, kollaborative CRM-Plattform entwickelt, um vollständig anpassbar und in Echtzeit zu sein.", "\nTo use Attio, you need to generate an Access Token:\n1. Login to your Attio account at https://app.attio.com.\n2. From the dropdown beside your workspace name, click Workspace settings.\n3. Click the Developers tab.\n4. Click on the \"New Access Token\" button.\n5. Set the appropriate Scopes for the integration.\n6. Copy the generated Access Token.\n": "\nUm Attio nutzen zu können, müssen Si<PERSON> ein Zugangs-Token generieren:\n1. Melden Sie sich bei Ihrem Attio-Ko<PERSON> unter https://app an. ttio.com.\n2. Klicken Sie im Dropdown-Menü neben Ihrem Arbeitsbereichsnamen auf die Einstellungen des Arbeitsbereichs.\n3. Klicken Sie auf die Registerkarte Entwickler.\n4. Klicken Sie auf den Button \"Neues Zugangs-Token\".\nLegen Sie die passenden Bereiche für die Integration fest.\n6. Kopieren Sie das generierte Zugriffstoken\n", "Create Record": "Datensatz erstellen", "Update Record": "Datensatz aktualisieren", "Find Record": "Datensatz finden", "Create List Entry": "Listeneintrag erstellen", "Update List Entry": "Listeneintrag aktualisieren", "Find List Entry": "Listeneintrag suchen", "Custom API Call": "Eigener API-Aufruf", "Creates a new record such as peron,company or deal.": "<PERSON>rst<PERSON>t einen neuen Rekord wie Peron, Unternehmen oder Deal.", "Update an existing record with new attribute values.": "Aktualisiere einen vorhandenen Datensatz mit neuen Attributwerten.", "Search for records in Attio using filters and return matching results.": "Suchen Sie nach Datensätzen in Attio mit Filtern und geben Sie die passenden Ergebnisse zurück.", "Add a record to a specified list.": "Fügen Sie einen Eintrag zu einer bestimmten Liste hinzu.", "Update the attributes of an existing entry in a list.": "Aktualisieren Sie die Attribute eines vorhandenen Eintrags in einer Liste.", "Search for entries in a specific list in Attio using filters and return matching results.": "Suchen Sie nach Einträgen in einer bestimmten Liste in Attio mithil<PERSON> von <PERSON> und geben Sie passende Ergebnisse zurück.", "Make a custom API call to a specific endpoint": "Einen benutzerdefinierten API-Aufruf an einen bestimmten Endpunkt machen", "Object": "Objekt", "Object Attributes": "Objektattribute", "Record ID": "Datensatz-ID", "List": "Liste", "Parent Object": "Übergeordnetes Objekt", "Parent Record ID": "Eltern-Datensatz-ID", "List Attributes": "Listenattribute", "Entry ID": "Eintrag-ID", "Method": "<PERSON>e", "Headers": "Kopfzeilen", "Query Parameters": "Abfrageparameter", "Body": "<PERSON><PERSON><PERSON><PERSON>", "No Error on Failure": "<PERSON><PERSON> bei <PERSON>hler", "Timeout (in seconds)": "Timeout (in Sekunden)", "The unique identifier of the record to update.": "Der eindeutige Bezeichner des zu aktualisierenden Datensatzes.", "The unique identifier of the entry to update.": "Der eindeutige Bezeichner des zu aktualisierenden Eintrags.", "Authorization headers are injected automatically from your connection.": "Autorisierungs-Header werden automatisch von Ihrer Verbindung injiziert.", "GET": "ERHALTEN", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "LÖSCHEN", "HEAD": "HEAD", "Record Created": "Datensatz erstellt", "Record Updated": "Datensatz aktualisiert", "List Entry Created": "Listeneintrag erstellt", "List Entry Updated": "Listeneintrag aktualisiert", "Triggers when a new record such as person,company or deal is created.": "Wird au<PERSON><PERSON><PERSON>, wenn ein neuer Rekord wie z.B. Person, Unternehmen oder Deal erstellt wird.", "Triggers when an existing record is updated (people, companies, deals, etc.).": "Wird au<PERSON><PERSON>, wenn ein existierender Datensatz aktualisiert wird (Personen, Unternehmen, Geschäfte etc.).", "Triggers when a new entry is added.": "Wird aus<PERSON><PERSON>, wenn ein neuer Eintrag hinzugefügt wird.", "Triggers when an existing entry is updated.": "Wird aus<PERSON>, wenn ein existierender Eintrag aktualisiert wird."}
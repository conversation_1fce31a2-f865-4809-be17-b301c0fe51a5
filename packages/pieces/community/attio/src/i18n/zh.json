{"Attio": "<PERSON><PERSON><PERSON>", "Modern, collaborative CRM platform built to be fully customizable and real-time.": "Modern, collaborative CRM platform built to be fully customizable and real-time.", "\nTo use Attio, you need to generate an Access Token:\n1. Login to your Attio account at https://app.attio.com.\n2. From the dropdown beside your workspace name, click Workspace settings.\n3. Click the Developers tab.\n4. Click on the \"New Access Token\" button.\n5. Set the appropriate Scopes for the integration.\n6. Copy the generated Access Token.\n": "\nTo use Attio, you need to generate an Access Token:\n1. Login to your Attio account at https://app.attio.com.\n2. From the dropdown beside your workspace name, click Workspace settings.\n3. Click the Developers tab.\n4. Click on the \"New Access Token\" button.\n5. Set the appropriate Scopes for the integration.\n6. Copy the generated Access Token.\n", "Create Record": "Create Record", "Update Record": "Update Record", "Find Record": "Find Record", "Create List Entry": "Create List Entry", "Update List Entry": "Update List Entry", "Find List Entry": "Find List Entry", "Custom API Call": "自定义 API 呼叫", "Creates a new record such as peron,company or deal.": "Creates a new record such as peron,company or deal.", "Update an existing record with new attribute values.": "Update an existing record with new attribute values.", "Search for records in Attio using filters and return matching results.": "Search for records in Attio using filters and return matching results.", "Add a record to a specified list.": "Add a record to a specified list.", "Update the attributes of an existing entry in a list.": "Update the attributes of an existing entry in a list.", "Search for entries in a specific list in Attio using filters and return matching results.": "Search for entries in a specific list in Attio using filters and return matching results.", "Make a custom API call to a specific endpoint": "将一个自定义 API 调用到一个特定的终点", "Object": "Object", "Object Attributes": "Object Attributes", "Record ID": "Record ID", "List": "List", "Parent Object": "Parent Object", "Parent Record ID": "Parent Record ID", "List Attributes": "List Attributes", "Entry ID": "Entry ID", "Method": "方法", "Headers": "信头", "Query Parameters": "查询参数", "Body": "正文内容", "No Error on Failure": "失败时没有错误", "Timeout (in seconds)": "超时(秒)", "The unique identifier of the record to update.": "The unique identifier of the record to update.", "The unique identifier of the entry to update.": "The unique identifier of the entry to update.", "Authorization headers are injected automatically from your connection.": "授权头自动从您的连接中注入。", "GET": "获取", "POST": "帖子", "PATCH": "PATCH", "PUT": "弹出", "DELETE": "删除", "HEAD": "黑色", "Record Created": "Record Created", "Record Updated": "Record Updated", "List Entry Created": "List Entry Created", "List Entry Updated": "List Entry Updated", "Triggers when a new record such as person,company or deal is created.": "Triggers when a new record such as person,company or deal is created.", "Triggers when an existing record is updated (people, companies, deals, etc.).": "Triggers when an existing record is updated (people, companies, deals, etc.).", "Triggers when a new entry is added.": "Triggers when a new entry is added.", "Triggers when an existing entry is updated.": "Triggers when an existing entry is updated."}
{"Attio": "<PERSON><PERSON><PERSON>", "Modern, collaborative CRM platform built to be fully customizable and real-time.": "Plateforme CRM moderne et collaborative conçue pour être entièrement personnalisable et en temps réel.", "\nTo use Attio, you need to generate an Access Token:\n1. Login to your Attio account at https://app.attio.com.\n2. From the dropdown beside your workspace name, click Workspace settings.\n3. Click the Developers tab.\n4. Click on the \"New Access Token\" button.\n5. Set the appropriate Scopes for the integration.\n6. Copy the generated Access Token.\n": "\nTo use Attio, you need to generate an Access Token:\n1. Login to your Attio account at https://app.attio.com.\n2. From the dropdown beside your workspace name, click Workspace settings.\n3. Click the Developers tab.\n4. Click on the \"New Access Token\" button.\n5. Set the appropriate Scopes for the integration.\n6. Copy the generated Access Token.\n", "Create Record": "<PERSON><PERSON>er un enregistrement", "Update Record": "Mettre à jour l'enregistrement", "Find Record": "Rechercher un enregistrement", "Create List Entry": "<PERSON><PERSON><PERSON> une entrée de liste", "Update List Entry": "Mettre à jour l'entrée de la liste", "Find List Entry": "Rechercher une entrée de liste", "Custom API Call": "Appel API personnalisé", "Creates a new record such as peron,company or deal.": "Crée un nouveau record comme peron, entreprise ou affaire.", "Update an existing record with new attribute values.": "Mettre à jour un enregistrement existant avec de nouvelles valeurs d'attribut.", "Search for records in Attio using filters and return matching results.": "Rechercher des enregistrements dans Attio à l'aide de filtres et retourner les résultats correspondants.", "Add a record to a specified list.": "Ajouter un enregistrement à une liste spécifiée.", "Update the attributes of an existing entry in a list.": "Mettre à jour les attributs d'une entrée existante dans une liste.", "Search for entries in a specific list in Attio using filters and return matching results.": "Recherchez les entrées dans une liste spécifique d'Attio en utilisant des filtres et retournez les résultats correspondants.", "Make a custom API call to a specific endpoint": "Passez un appel API personnalisé à un point de terminaison spécifique", "Object": "Objet", "Object Attributes": "Attributs d'objet", "Record ID": "ID de l'enregistrement", "List": "Liste", "Parent Object": "Objet parent", "Parent Record ID": "ID de l'enregistrement parent", "List Attributes": "Liste des attributs", "Entry ID": "ID de l'entrée", "Method": "Méthode", "Headers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Query Parameters": "Paramètres de requête", "Body": "Corps", "No Error on Failure": "Aucune erreur en cas d'échec", "Timeout (in seconds)": "<PERSON><PERSON><PERSON>atten<PERSON> (en secondes)", "The unique identifier of the record to update.": "L'identifiant unique de l'enregistrement à mettre à jour.", "The unique identifier of the entry to update.": "L'identifiant unique de l'entrée à mettre à jour.", "Authorization headers are injected automatically from your connection.": "Les en-têtes d'autorisation sont injectés automatiquement à partir de votre connexion.", "GET": "OBTENIR", "POST": "POSTER", "PATCH": "PATCH", "PUT": "EFFACER", "DELETE": "SUPPRIMER", "HEAD": "TÊTE", "Record Created": "Enregistrement créé", "Record Updated": "Enregistrement mis à jour", "List Entry Created": "Entrée de liste créée", "List Entry Updated": "Entrée de liste mise à jour", "Triggers when a new record such as person,company or deal is created.": "Déclenche lorsqu'un nouvel enregistrement tel que la personne, la société ou la transaction est créée.", "Triggers when an existing record is updated (people, companies, deals, etc.).": "Déclenche lorsqu'un enregistrement existant est mis à jour (personnes, sociétés, transactions, etc.).", "Triggers when a new entry is added.": "<PERSON>é<PERSON><PERSON><PERSON> quand une nouvelle entrée est ajoutée.", "Triggers when an existing entry is updated.": "<PERSON>é<PERSON>nche lorsqu'une entrée existante est mise à jour."}
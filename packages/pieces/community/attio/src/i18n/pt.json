{"Attio": "<PERSON><PERSON><PERSON>", "Modern, collaborative CRM platform built to be fully customizable and real-time.": "Plataforma de CRM moderna e colaborativa construída para ser totalmente personalizável e em tempo real.", "\nTo use Attio, you need to generate an Access Token:\n1. Login to your Attio account at https://app.attio.com.\n2. From the dropdown beside your workspace name, click Workspace settings.\n3. Click the Developers tab.\n4. Click on the \"New Access Token\" button.\n5. Set the appropriate Scopes for the integration.\n6. Copy the generated Access Token.\n": "\nTo use Attio, you need to generate an Access Token:\n1. Login to your Attio account at https://app.attio.com.\n2. From the dropdown beside your workspace name, click Workspace settings.\n3. Click the Developers tab.\n4. Click on the \"New Access Token\" button.\n5. Set the appropriate Scopes for the integration.\n6. Copy the generated Access Token.\n", "Create Record": "<PERSON><PERSON><PERSON>", "Update Record": "<PERSON><PERSON><PERSON><PERSON>", "Find Record": "Localizar Registro", "Create List Entry": "Criar entrada de lista", "Update List Entry": "Atualizar entrada da lista", "Find List Entry": "Encontrar entrada da lista", "Custom API Call": "Chamada de API personalizada", "Creates a new record such as peron,company or deal.": "Cria um novo registro como peron, empresa ou negócio.", "Update an existing record with new attribute values.": "Atualizar um registro existente com novos valores de atributo.", "Search for records in Attio using filters and return matching results.": "Pesquisa por registros no Attio usando filtros e resultados correspondentes.", "Add a record to a specified list.": "Adicionar um registro a uma lista especificada.", "Update the attributes of an existing entry in a list.": "Atualizar os atributos de uma entrada existente em uma lista.", "Search for entries in a specific list in Attio using filters and return matching results.": "Pesquise por entradas em uma lista específica em Attio usando filtros e retorna resultados correspondentes.", "Make a custom API call to a specific endpoint": "Faça uma chamada de API personalizada para um ponto de extremidade específico", "Object": "<PERSON><PERSON><PERSON><PERSON>", "Object Attributes": "Atributos do objeto", "Record ID": "ID do Registro", "List": "Lista", "Parent Object": "<PERSON>b<PERSON><PERSON> pai", "Parent Record ID": "ID Registro Pai", "List Attributes": "Listar Atributos", "Entry ID": "ID da postagem", "Method": "<PERSON><PERSON><PERSON><PERSON>", "Headers": "Cabeçalhos", "Query Parameters": "Parâ<PERSON><PERSON> da consult<PERSON>", "Body": "<PERSON><PERSON><PERSON><PERSON>", "No Error on Failure": "Nenhum erro no Failure", "Timeout (in seconds)": "Tempo limite (em segundos)", "The unique identifier of the record to update.": "O identificador exclusivo do registro a ser atualizado.", "The unique identifier of the entry to update.": "O identificador exclusivo da entrada a ser atualizada.", "Authorization headers are injected automatically from your connection.": "Os cabeçalhos de autorização são inseridos automaticamente a partir da sua conexão.", "GET": "OBTER", "POST": "POSTAR", "PATCH": "COMPRAR", "PUT": "COLOCAR", "DELETE": "EXCLUIR", "HEAD": "CABEÇA", "Record Created": "<PERSON>tro criado", "Record Updated": "Registro Atualizado", "List Entry Created": "Entrada de Lista Criada", "List Entry Updated": "Lista de itens atualizados", "Triggers when a new record such as person,company or deal is created.": "Aciona quando um novo registro como pessoa, empresa ou negócio é criado.", "Triggers when an existing record is updated (people, companies, deals, etc.).": "Aciona quando um registro existente é atualizado (pessoas, empresas, negócios, etc.).", "Triggers when a new entry is added.": "Dispara quando uma nova entrada for adicionada.", "Triggers when an existing entry is updated.": "Dispara quando uma entrada existente é atualizada."}
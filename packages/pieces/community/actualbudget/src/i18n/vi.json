{"2015": "2015", "2016": "2016", "2017": "2017", "2018": "2018", "2019": "2019", "2020": "2020", "2021": "2021", "2022": "2022", "2023": "2023", "2024": "2024", "2025": "2025", "2026": "2026", "2027": "2027", "2028": "2028", "2029": "2029", "2030": "2030", "Actual Budget": "Actual Budget", "Personal finance app": "Personal finance app", "Server URL": "Server URL", "Password": "Password", "Sync ID": "Sync ID", "End-to-end encryption password": "End-to-end encryption password", "This is the URL of your running server": "This is the URL of your running server", "This is the password you use to log into the server": "This is the password you use to log into the server", "This is the ID from Settings → Show advanced settings → Sync ID": "This is the ID from Settings → Show advanced settings → Sync ID", "if you have end-to-end encryption enabled": "if you have end-to-end encryption enabled", "Enter authentication details": "Enter authentication details", "Get Budget": "Get Budget", "Import Transaction": "Import Transaction", "Import Transactions": "Import Transactions", "Get Categories": "Get Categories", "Get Accounts": "Get Accounts", "Get your monthly budget": "Get your monthly budget", "Add a transaction": "Add a transaction", "Get your categories": "Get your categories", "Get your accounts": "Get your accounts", "Month": "Month", "Year": "Year", "Account ID": "Account ID", "Date": "Date", "Payee Name": "Payee Name", "Amount": "Amount", "Category ID": "Category ID", "Notes": "Notes", "Imported ID": "Imported ID", "Transfer ID": "Transfer ID", "Cleared": "Cleared", "Imported Payee": "Imported Payee", "Transactions": "Transactions", "The month of the budget you want to get": "The month of the budget you want to get", "The year of the budget you want to get": "The year of the budget you want to get", "ID of the account you want to import a transaction to": "ID of the account you want to import a transaction to", "Date the transaction took place": "Date the transaction took place", "Name of the payee": "Name of the payee", "The dollar value of the transaction": "The dollar value of the transaction", "ID of the transaction category": "ID of the transaction category", "Additional notes about the transaction": "Additional notes about the transaction", "Unique ID given by the bank for importing": "Unique ID given by the bank for importing", "ID of the transaction in the other account for the transfer": "ID of the transaction in the other account for the transfer", "Flag indicating if the transaction has cleared or not": "Flag indicating if the transaction has cleared or not", "Raw description when importing, representing the original value": "Raw description when importing, representing the original value", "A json array of the transaction object": "A json array of the transaction object", "January": "January", "February": "February", "March": "March", "April": "April", "May": "May", "June": "June", "July": "July", "August": "August", "September": "September", "October": "October", "November": "November", "December": "December"}
{"2015": "2015", "2016": "2016", "2017": "2017", "2018": "2018", "2019": "2019", "2020": "2020", "2021": "2021", "2022": "2022", "2023": "2023", "2024": "2024", "2025": "2025", "2026": "2026", "2027": "2027", "2028": "2028", "2029": "2029", "2030": "2030", "Actual Budget": "Фактический бюджет", "Personal finance app": "Личное финансовое приложение", "Server URL": "URL сервера", "Password": "Пароль", "Sync ID": "Синхр. ID", "End-to-end encryption password": "Конечный пароль шифрования", "This is the URL of your running server": "Это URL вашего запущенного сервера", "This is the password you use to log into the server": "Пароль для входа на сервер", "This is the ID from Settings → Show advanced settings → Sync ID": "Это ID из настроек → Показать расширенные настройки → Sync ID", "if you have end-to-end encryption enabled": "если у вас включено сквозное шифрование", "Enter authentication details": "Введите данные проверки подлинности", "Get Budget": "Получить бюджет", "Import Transaction": "Импорт транзакции", "Import Transactions": "Импорт проводок", "Get Categories": "Получить категории", "Get Accounts": "Получить аккаунты", "Get your monthly budget": "Получите свой ежемесячный бюджет", "Add a transaction": "Добавить транзакцию", "Get your categories": "Получите свои категории", "Get your accounts": "Получить аккаунты", "Month": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Year": "Год", "Account ID": "ID клиента", "Date": "Дата", "Payee Name": "Имя получателя", "Amount": "Сумма", "Category ID": "ID категории", "Notes": "Примечания", "Imported ID": "Импортированный ID", "Transfer ID": "ID перевода", "Cleared": "Очищено", "Imported Payee": "Импортированный получатель", "Transactions": "Транзакции", "The month of the budget you want to get": "Месяц бюджета, который вы хотите получить", "The year of the budget you want to get": "Год бюджета, который вы хотите получить", "ID of the account you want to import a transaction to": "ID аккаунта, который вы хотите импортировать транзакцию в", "Date the transaction took place": "Дата проведения транзакции", "Name of the payee": "Имя получателя", "The dollar value of the transaction": "Долговая стоимость транзакции", "ID of the transaction category": "ID категории транзакций", "Additional notes about the transaction": "Дополнительные заметки о транзакции", "Unique ID given by the bank for importing": "Уникальный идентификатор, присвоенный банком для импорта", "ID of the transaction in the other account for the transfer": "ID транзакции на другом счете для перевода", "Flag indicating if the transaction has cleared or not": "Флаг, указывающий, если транзакция очищена или нет", "Raw description when importing, representing the original value": "Исходное описание при импорте, представляющее оригинальное значение", "A json array of the transaction object": "<PERSON><PERSON> массив объекта транзакции", "January": "Январь", "February": "Февраль", "March": "Ма<PERSON><PERSON>", "April": "Апрель", "May": "<PERSON><PERSON><PERSON>", "June": "Июнь", "July": "Июль", "August": "Август", "September": "Сентябрь", "October": "Октябрь", "November": "Ноябрь", "December": "Декабрь"}
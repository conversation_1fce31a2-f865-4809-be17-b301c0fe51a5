{"name": "pieces-actualbudget", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/pieces/community/actualbudget/src", "projectType": "library", "release": {"version": {"generatorOptions": {"packageRoot": "dist/{projectRoot}", "currentVersionResolver": "git-tag"}}}, "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/pieces/community/actualbudget", "tsConfig": "packages/pieces/community/actualbudget/tsconfig.lib.json", "packageJson": "packages/pieces/community/actualbudget/package.json", "main": "packages/pieces/community/actualbudget/src/index.ts", "assets": ["packages/pieces/community/actualbudget/*.md", {"input": "packages/pieces/community/actualbudget/src/i18n", "output": "./src/i18n", "glob": "**/!(i18n.json)"}], "buildableProjectDepsInPackageJsonType": "dependencies", "updateBuildableProjectDepsInPackageJson": true}, "dependsOn": ["prebuild", "^build"]}, "prebuild": {"executor": "nx:run-commands", "options": {"cwd": "packages/pieces/community/actualbudget", "command": "npm ci"}}, "build-with-deps": {"executor": "nx:run-commands", "options": {"commands": ["nx run pieces-actualbudget:prebuild", "nx run pieces-actualbudget:build", "nx run pieces-actualbudget:postbuild"], "parallel": false}}, "postbuild": {"executor": "nx:run-commands", "options": {"cwd": "dist/packages/pieces/community/actualbudget", "command": "npm install"}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}}
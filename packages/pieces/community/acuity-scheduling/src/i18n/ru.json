{"Acuity Scheduling": "Планирование доступности", "Add Blocked Off Time": "Добавить заблокированное время", "Create Appointment": "Назначить встречу", "Create Client": "Создать клиента", "Reschedule Appointment": "Перенос назначения", "Update Client": "Обновить клиента", "Find Appointment(s)": "Найти назначение", "Find Client": "Найти клиента", "Custom API Call": "Пользовательский вызов API", "Block off a specific time range on a calendar.": "Блокировать из определенного диапазона времени в календаре.", "Creates a new appointment.": "Создает новое назначение.", "Creates a new client.": "Создает нового клиента.", "Reschedules an existing appointment to a new date/time.": "Перераспределяет существующую встречу на новую дату/время.", "Updates an existing client.": "Обновление существующего клиента.", "Find appointments based on various criteria, including client information.": "Найдите встречи, основанные на различных критериях, включая информацию о клиенте.", "Finds client based on seach term.": "Ищет клиент на основе условия для себя.", "Make a custom API call to a specific endpoint": "Сделать пользовательский API вызов к определенной конечной точке", "Start Time": "Время начала", "End Time": "Конец времени", "Calendar ID": "ID календаря", "Notes": "Примечания", "DateTime": "Дата", "Appointment Type": "Тип встречи", "First Name": "First Name", "Last Name": "Last Name", "Email": "Почта", "Phone": "Телефон", "Timezone": "Timezone", "Book as Admin": "Книга как администратор", "Suppress Confirmation Email/SMS": "Отключить подтверждение Email/SMS", "Certificate Code": "Код сертификата", "SMS Opt-In": "SMS-заявка", "Addons": "Addons", "Label": "Метка", "Appointment ID": "Назначение ID", "New Calendar ID": "Новый ID календаря", "Reschedule as Admin": "Перенести звонок как Администратор", "Suppress Rescheduling Email/SMS": "Отключить перепланировку эл.почты/SMS", "Current First Name (Identifier)": "Текущее имя (идентификатор)", "Current Last Name (Identifier)": "Текущая Фамилия (идентификатор)", "Current Phone (Identifier, Optional)": "Текущий телефон (идентификатор, необязательно)", "New First Name": "Новое имя", "New Last Name": "Новая фамилия", "New Email": "Новое письмо", "New Phone": "Новый телефон", "New Notes": "Новые заметки", "Client First Name": "Имя клиента", "Client Last Name": "Фамилия клиента", "Client Email": "<PERSON><PERSON> клиента", "Client Phone": "Телефон клиента", "Min Date": "Мин. дата", "Max Date": "Макс. Дата", "Appointment Status": "Статус назначения", "Max Results": "Макс. результатов", "Sort Direction": "Направление сортировки", "Search Term": "Поиск термина", "Method": "Метод", "Headers": "Заголовки", "Query Parameters": "Параметры запроса", "Body": "Тело", "No Error on Failure": "Нет ошибок при ошибке", "Timeout (in seconds)": "Таймаут (в секундах)", "The start date and time for the block (ISO 8601 format).": "Дата и время начала блока (формат ISO 8601).", "The end date and time for the block (ISO 8601 format).": "Дата и время окончания блока (формат ISO 8601).", "The numeric ID of the calendar to add this block to.": "Цифровой идентификатор календаря для добавления этого блока.", "Optional notes for the blocked off time.": "Необязательные заметки для заблокированного времени.", "Date and time of the appointment.": "Дата и время назначения.", "Select the type of appointment.": "Выберите тип встречи.", "Client's first name.": "Имя клиента.", "Client's last name.": "Фамилия клиента.", "Client's email address. (Optional if booking as admin).": "Адрес электронной почты клиента. (опционально, если бронирование осуществляется администратором).", "Client's phone number.": "Номер телефона клиента.", "Client's timezone (e.g., America/New_York). Required for accurate availability checking.": "Часовой пояс клиента (наприм<PERSON><PERSON>, Америка/Нью-Йорк). Требуется для точной проверки доступности.", "Set to true to book as an admin. Disables availability/attribute validations, allows setting notes, and makes Calendar ID required.": "Отметьте для книги как администратор. Отключает проверку доступности/атрибутов, позволяет установить заметки и сделать идентификатор календаря.", "Numeric ID of the calendar. Required if booking as admin. If not provided, Acuity tries to find an available calendar automatically for non-admin bookings.": "Числовой идентификатор календаря. Обязательно если бронирование производится как администратор. Если оно не указано, Acuity попытается автоматически найти календарь для бронирования не-администратора.", "If true, confirmation emails or SMS will not be sent.": "Если введено значение true, сообщения с подтверждением или SMS не будут отправлены.", "Package or coupon certificate code.": "Код сертификата на пакет или купон.", "Appointment notes. Only settable if booking as admin.": "Назначение заметки. Задайте значение только при бронировании как admin.", "Indicates whether the client has explicitly given permission to receive SMS messages.": "Указывает, предоставил ли клиент явное разрешение на получение SMS-сообщений.", "Select addons for the appointment. Addons are filtered by selected Appointment Type if available.": "Выберите аддоны для назначения. Аддоны фильтруются по выбранному типу назначения, если таковые имеются.", "Apply a label to the appointment. The API currently supports one label.": "Применить метку к назначению. В настоящее время API поддерживает одну метку.", "Client's email address.": "Адрес электронной почты клиента.", "Notes about the client.": "Примечания о клиенте.", "The ID of the appointment to reschedule.": "Идентификатор записи на прием для перепланировки.", "Select the type of appointment (used for finding new available slots).": "Выберите тип назначения (используется для поиска новых доступных слотов).", "New Date and time of the appointment.": "Новая дата и время назначения.", "Client's timezone (e.g., America/New_York).": "Часовой пояс клиента (например, Америка/Нью-Йорк).", "Numeric ID of the new calendar to reschedule to. If blank, stays on current calendar. Submit 0 to auto-assign.": "Числовой идентификатор нового календаря для изменения планировки. Если пустой, остаётся в текущем календаре. Отправьте 0 для автоматического назначения.", "Set to true to reschedule as an admin. Disables availability validations.": "Отметьте значение true, чтобы перепланировать как администратор. Отключает проверки доступности.", "If true, rescheduling emails or SMS will not be sent.": "Если включено, перепланировка писем или SMS не будет отправлена.", "The current first name of the client to update.": "Текущее имя обновления клиента.", "The current last name of the client to update.": "Текущее имя обновления клиента.", "The current phone number of the client to update. Helps identify the client if names are not unique.": "Текущий номер телефона клиента для обновления. Помогает идентифицировать клиента, если имена не уникальны.", "Client's new first name. Leave blank to keep current.": "Новое имя клиента. Оставьте пустым для сохранения.", "Client's new last name. Leave blank to keep current.": "Фамилия клиента. Оставьте пустым, чтобы сохранять актуальность.", "Client's new email address. Leave blank to keep current.": "Новый адрес электронной почты клиента. Оставьте пустым, чтобы держать в курсе событий.", "Client's new phone number. Leave blank to keep current.": "Новый номер телефона клиента. Оставьте пустым, чтобы оставаться в курсе.", "New notes about the client. Leave blank to keep current.": "Новые заметки о клиенте. Оставьте пустым для сохранения.", "Filter appointments by client first name.": "Фильтровать встречи по имени клиента.", "Filter appointments by client last name.": "Фильтровать встречи по фамилии клиента.", "Filter appointments by client e-mail address.": "Фильтровать встречи по электронной почте клиента.", "Filter appointments by client phone number. URL encode '+' if using country codes (e.g., %2B1234567890).": "Фильтровать встречи по номеру телефона. URL кодировать '+', если используется код страны (например, %2B1234567890).", "Only get appointments on or after this date.": "Получать встречи только на или после этой даты.", "Only get appointments on or before this date.": "Получать только встречи на или раньше этой даты.", "Show only appointments on the calendar with this ID.": "Показывать только встречи в календаре с этим идентификатором.", "Show only appointments of this type.": "Показать только встречи этого типа.", "Filter by appointment status.": "Фильтр по статусу назначения.", "Maximum number of results to return (default 100).": "Максимальное количество результатов возврата (по умолчанию 100).", "Sort direction for the results.": "Направление сортировки результатов.", "Filter client list by first name, last name, or phone number.": "Фильтровать список клиентов по имени, фамилии, или номеру телефона.", "Authorization headers are injected automatically from your connection.": "Заголовки авторизации включаются автоматически из вашего соединения.", "Scheduled": "Запланированный", "Canceled": "Отменено", "All (Scheduled & Canceled)": "Все (Спланировано & Отменено)", "Descending (DESC)": "По убыванию (DESC)", "Ascending (ASC)": "По возрастанию (ASC)", "GET": "ПОЛУЧИТЬ", "POST": "ПОСТ", "PATCH": "ПАТЧ", "PUT": "ПОКУПИТЬ", "DELETE": "УДАЛИТЬ", "HEAD": "HEAD", "Appointment Canceled": "Назначение отменено", "New Appointment": "Новая встреча", "Triggers when an appointment is canceled.": "Срабатывает при отмене назначения.", "Triggers when a new appointment is scheduled.": "Вызывает, когда запланирована новая запись.", "Calendar": "Календарь"}
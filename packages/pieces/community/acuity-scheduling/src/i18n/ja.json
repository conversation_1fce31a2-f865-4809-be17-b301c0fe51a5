{"Acuity Scheduling": "酸性度スケジュール", "Add Blocked Off Time": "ブロックオフ時間を追加", "Create Appointment": "予約を作成", "Create Client": "クライアントを作成", "Reschedule Appointment": "予定の再スケジュール", "Update Client": "クライアントを更新", "Find Appointment(s)": "予約を検索", "Find Client": "クライアントを検索", "Custom API Call": "カスタムAPI通話", "Block off a specific time range on a calendar.": "カレンダー上の特定の時間範囲をブロックします。", "Creates a new appointment.": "新しい予定を作成します。", "Creates a new client.": "新しいクライアントを作成します。", "Reschedules an existing appointment to a new date/time.": "既存の予定を新しい日時に変更します。", "Updates an existing client.": "既存のクライアントを更新します。", "Find appointments based on various criteria, including client information.": "クライアント情報を含むさまざまな基準に基づいて予定を検索します。", "Finds client based on seach term.": "seach term に基づいてクライアントを検索します。", "Make a custom API call to a specific endpoint": "特定のエンドポイントへのカスタム API コールを実行します。", "Start Time": "開始時刻", "End Time": "終了時刻", "Calendar ID": "カレンダー ID", "Notes": "メモ", "DateTime": "日時", "Appointment Type": "予約タイプ", "First Name": "名", "Last Name": "Last Name", "Email": "Eメールアドレス", "Phone": "電話番号", "Timezone": "Timezone", "Book as Admin": "ブックを管理者として表示", "Suppress Confirmation Email/SMS": "確認メール/SMSを無効にする", "Certificate Code": "Certificate Code", "SMS Opt-In": "SMSオプトイン", "Addons": "Addons", "Label": "ラベル", "Appointment ID": "予約ID", "New Calendar ID": "新しいカレンダー ID", "Reschedule as Admin": "管理者として再スケジュール", "Suppress Rescheduling Email/SMS": "メール/SMSの変更を抑制する", "Current First Name (Identifier)": "現在の姓（識別子）", "Current Last Name (Identifier)": "現在の姓 (識別子)", "Current Phone (Identifier, Optional)": "現在の電話番号 (ID、オプション)", "New First Name": "新しい名", "New Last Name": "新しい姓", "New Email": "新しいメール", "New Phone": "新しい電話番号", "New Notes": "新しいメモ", "Client First Name": "クライアント名", "Client Last Name": "クライアントの姓", "Client Email": "クライアントのメールアドレス", "Client Phone": "クライアント電話", "Min Date": "最小日付", "Max Date": "最大日付", "Appointment Status": "予約状況", "Max Results": "最大結果", "Sort Direction": "並べ替え方向", "Search Term": "検索用語", "Method": "方法", "Headers": "ヘッダー", "Query Parameters": "クエリパラメータ", "Body": "本文", "No Error on Failure": "失敗時にエラーはありません", "Timeout (in seconds)": "タイムアウト（秒）", "The start date and time for the block (ISO 8601 format).": "ブロックの開始日時（ISO 8601形式）。", "The end date and time for the block (ISO 8601 format).": "ブロックの終了日時（ISO 8601形式）。", "The numeric ID of the calendar to add this block to.": "このブロックを追加するカレンダーの数値ID。", "Optional notes for the blocked off time.": "ブロックされたオフ時間のためのオプションのノート。", "Date and time of the appointment.": "任命日時。", "Select the type of appointment.": "予定の種類を選択します。", "Client's first name.": "クライアントの名", "Client's last name.": "クライアントの姓。", "Client's email address. (Optional if booking as admin).": "クライアントのメールアドレス（管理者として予約する場合はオプション）。", "Client's phone number.": "クライアントの電話番号", "Client's timezone (e.g., America/New_York). Required for accurate availability checking.": "クライアントのタイムゾーン（例：アメリカ/ニュー_ヨーク）。正確な可用性のチェックに必要です。", "Set to true to book as an admin. Disables availability/attribute validations, allows setting notes, and makes Calendar ID required.": "管理者としてブックにtrueを設定します。可用性/属性のバリデーションを無効にし、メモの設定を許可し、カレンダーIDを必須にします。", "Numeric ID of the calendar. Required if booking as admin. If not provided, Acuity tries to find an available calendar automatically for non-admin bookings.": "カレンダーの数値ID。管理者として予約する場合に必要です。指定されていない場合、Acuity は管理者以外の予約に対して自動的に利用可能なカレンダーを検索しようとします。", "If true, confirmation emails or SMS will not be sent.": "true の場合、確認メールまたは SMS は送信されません。", "Package or coupon certificate code.": "パッケージまたはクーポン証明書コード。", "Appointment notes. Only settable if booking as admin.": "予約メモ 予約が管理者の場合のみ設定可能です。", "Indicates whether the client has explicitly given permission to receive SMS messages.": "SMSメッセージを受信する権限をクライアントに明示的に与えたかどうかを示します。", "Select addons for the appointment. Addons are filtered by selected Appointment Type if available.": "予定のアドオンを選択します。アドオンは、可能な場合は選択した予定タイプでフィルタリングされます。", "Apply a label to the appointment. The API currently supports one label.": "予定にラベルを適用します。APIは現在1つのラベルをサポートしています。", "Client's email address.": "クライアントのメールアドレス。", "Notes about the client.": "クライアントに関する注意事項。", "The ID of the appointment to reschedule.": "スケジュールを変更する予定の ID", "Select the type of appointment (used for finding new available slots).": "予定の種類を選択します (新しい利用可能なスロットを見つけるために使用します)。", "New Date and time of the appointment.": "予定の新しい日付と時刻", "Client's timezone (e.g., America/New_York).": "クライアントのタイムゾーン（例：アメリカ/ニュー_ヨーク）。", "Numeric ID of the new calendar to reschedule to. If blank, stays on current calendar. Submit 0 to auto-assign.": "再スケジュールする新しいカレンダーの数値ID。空白の場合はカレンダーのままです。0を自動割り当てに送信します。", "Set to true to reschedule as an admin. Disables availability validations.": "管理者としてリスケジュールする場合は true に設定します。可用性の検証を無効にします。", "If true, rescheduling emails or SMS will not be sent.": "true の場合、メールやSMSのスケジュール変更は送信されません。", "The current first name of the client to update.": "更新するクライアントの現在の名", "The current last name of the client to update.": "更新するクライアントの現在の姓", "The current phone number of the client to update. Helps identify the client if names are not unique.": "更新するクライアントの現在の電話番号。名前が一意でない場合、クライアントを識別するのに役立ちます。", "Client's new first name. Leave blank to keep current.": "クライアントの新しい名称です。空白のままにすると最新の名称になります。", "Client's new last name. Leave blank to keep current.": "クライアントの新しい苗字です。空白のままにすると最新のままになります。", "Client's new email address. Leave blank to keep current.": "クライアントの新しいメールアドレス。空白のままにしてください。", "Client's new phone number. Leave blank to keep current.": "クライアントの新しい電話番号。空白のままにすると最新の状態になります。", "New notes about the client. Leave blank to keep current.": "クライアントに関する新しいメモ。現在のままにするには空白のままにしてください。", "Filter appointments by client first name.": "クライアントの名で予定をフィルタリングします。", "Filter appointments by client last name.": "クライアントの姓で予定をフィルタリングします。", "Filter appointments by client e-mail address.": "クライアントの電子メール アドレスで予定をフィルタリングします。", "Filter appointments by client phone number. URL encode '+' if using country codes (e.g., %2B1234567890).": "クライアントの電話番号で予定をフィルタリングします。国コードを使用する場合、URLは「+」をエンコードします（例： %2B1234567890）。", "Only get appointments on or after this date.": "この日付以降の予約のみを取得します。", "Only get appointments on or before this date.": "この日付以前の予約のみを取得します。", "Show only appointments on the calendar with this ID.": "このIDでカレンダーに予定のみを表示します。", "Show only appointments of this type.": "このタイプの予定のみを表示します。", "Filter by appointment status.": "予約ステータスで絞り込みます。", "Maximum number of results to return (default 100).": "返す結果の最大数（デフォルトは100）。", "Sort direction for the results.": "結果の並べ替え方向。", "Filter client list by first name, last name, or phone number.": "クライアントのリストを姓、姓、電話番号で絞り込みます。", "Authorization headers are injected automatically from your connection.": "認証ヘッダは接続から自動的に注入されます。", "Scheduled": "スケジュール済み", "Canceled": "キャンセルしました", "All (Scheduled & Canceled)": "すべて (スケジュールとキャンセル)", "Descending (DESC)": "降順", "Ascending (ASC)": "昇順", "GET": "取得", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "削除", "HEAD": "頭", "Appointment Canceled": "予約がキャンセルされました", "New Appointment": "新しい予約", "Triggers when an appointment is canceled.": "予約がキャンセルされたときにトリガーします。", "Triggers when a new appointment is scheduled.": "新しい予約がスケジュールされたときにトリガーします。", "Calendar": "カレンダー"}
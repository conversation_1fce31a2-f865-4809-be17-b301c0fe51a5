{"Acuity Scheduling": "Acuity Scheduling", "Add Blocked Off Time": "Add Blocked Off Time", "Create Appointment": "Create Appointment", "Create Client": "Create Client", "Reschedule Appointment": "Reschedule Appointment", "Update Client": "Update Client", "Find Appointment(s)": "Find Appointment(s)", "Find Client": "Find Client", "Custom API Call": "Custom API Call", "Block off a specific time range on a calendar.": "Block off a specific time range on a calendar.", "Creates a new appointment.": "Creates a new appointment.", "Creates a new client.": "Creates a new client.", "Reschedules an existing appointment to a new date/time.": "Reschedules an existing appointment to a new date/time.", "Updates an existing client.": "Updates an existing client.", "Find appointments based on various criteria, including client information.": "Find appointments based on various criteria, including client information.", "Finds client based on seach term.": "Finds client based on seach term.", "Make a custom API call to a specific endpoint": "Make a custom API call to a specific endpoint", "Start Time": "Start Time", "End Time": "End Time", "Calendar ID": "Calendar ID", "Notes": "Notes", "DateTime": "DateTime", "Appointment Type": "Appointment Type", "First Name": "First Name", "Last Name": "Last Name", "Email": "Email", "Phone": "Phone", "Timezone": "Timezone", "Book as Admin": "Book as Admin", "Suppress Confirmation Email/SMS": "Suppress Confirmation Email/SMS", "Certificate Code": "Certificate Code", "SMS Opt-In": "SMS Opt-In", "Addons": "Addons", "Label": "Label", "Appointment ID": "Appointment ID", "New Calendar ID": "New Calendar ID", "Reschedule as Admin": "Reschedule as <PERSON><PERSON>", "Suppress Rescheduling Email/SMS": "Suppress Rescheduling Email/SMS", "Current First Name (Identifier)": "Current First Name (Identifier)", "Current Last Name (Identifier)": "Current Last Name (Identifier)", "Current Phone (Identifier, Optional)": "Current Phone (Identifier, Optional)", "New First Name": "New First Name", "New Last Name": "New Last Name", "New Email": "New Email", "New Phone": "New Phone", "New Notes": "New Notes", "Client First Name": "Client First Name", "Client Last Name": "Client Last Name", "Client Email": "Client Email", "Client Phone": "Client Phone", "Min Date": "Min Date", "Max Date": "Max Date", "Appointment Status": "Appointment Status", "Max Results": "Max Results", "Sort Direction": "Sort Direction", "Search Term": "Search Term", "Method": "Method", "Headers": "Headers", "Query Parameters": "Query Parameters", "Body": "Body", "No Error on Failure": "No Error on Failure", "Timeout (in seconds)": "Timeout (in seconds)", "The start date and time for the block (ISO 8601 format).": "The start date and time for the block (ISO 8601 format).", "The end date and time for the block (ISO 8601 format).": "The end date and time for the block (ISO 8601 format).", "The numeric ID of the calendar to add this block to.": "The numeric ID of the calendar to add this block to.", "Optional notes for the blocked off time.": "Optional notes for the blocked off time.", "Date and time of the appointment.": "Date and time of the appointment.", "Select the type of appointment.": "Select the type of appointment.", "Client's first name.": "<PERSON><PERSON>'s first name.", "Client's last name.": "<PERSON><PERSON>'s last name.", "Client's email address. (Optional if booking as admin).": "Client's email address. (Optional if booking as admin).", "Client's phone number.": "<PERSON><PERSON>'s phone number.", "Client's timezone (e.g., America/New_York). Required for accurate availability checking.": "Client's timezone (e.g., America/New_York). Required for accurate availability checking.", "Set to true to book as an admin. Disables availability/attribute validations, allows setting notes, and makes Calendar ID required.": "Set to true to book as an admin. Disables availability/attribute validations, allows setting notes, and makes Calendar ID required.", "Numeric ID of the calendar. Required if booking as admin. If not provided, Acuity tries to find an available calendar automatically for non-admin bookings.": "Numeric ID of the calendar. Required if booking as admin. If not provided, <PERSON><PERSON><PERSON> tries to find an available calendar automatically for non-admin bookings.", "If true, confirmation emails or SMS will not be sent.": "If true, confirmation emails or SMS will not be sent.", "Package or coupon certificate code.": "Package or coupon certificate code.", "Appointment notes. Only settable if booking as admin.": "Appointment notes. Only settable if booking as admin.", "Indicates whether the client has explicitly given permission to receive SMS messages.": "Indicates whether the client has explicitly given permission to receive SMS messages.", "Select addons for the appointment. Addons are filtered by selected Appointment Type if available.": "Select addons for the appointment. Addons are filtered by selected Appointment Type if available.", "Apply a label to the appointment. The API currently supports one label.": "Apply a label to the appointment. The API currently supports one label.", "Client's email address.": "Client's email address.", "Notes about the client.": "Notes about the client.", "The ID of the appointment to reschedule.": "The ID of the appointment to reschedule.", "Select the type of appointment (used for finding new available slots).": "Select the type of appointment (used for finding new available slots).", "New Date and time of the appointment.": "New Date and time of the appointment.", "Client's timezone (e.g., America/New_York).": "Client's timezone (e.g., America/New_York).", "Numeric ID of the new calendar to reschedule to. If blank, stays on current calendar. Submit 0 to auto-assign.": "Numeric ID of the new calendar to reschedule to. If blank, stays on current calendar. Submit 0 to auto-assign.", "Set to true to reschedule as an admin. Disables availability validations.": "Set to true to reschedule as an admin. Disables availability validations.", "If true, rescheduling emails or SMS will not be sent.": "If true, rescheduling emails or SMS will not be sent.", "The current first name of the client to update.": "The current first name of the client to update.", "The current last name of the client to update.": "The current last name of the client to update.", "The current phone number of the client to update. Helps identify the client if names are not unique.": "The current phone number of the client to update. Helps identify the client if names are not unique.", "Client's new first name. Leave blank to keep current.": "<PERSON><PERSON>'s new first name. Leave blank to keep current.", "Client's new last name. Leave blank to keep current.": "<PERSON><PERSON>'s new last name. Leave blank to keep current.", "Client's new email address. Leave blank to keep current.": "Client's new email address. Leave blank to keep current.", "Client's new phone number. Leave blank to keep current.": "<PERSON><PERSON>'s new phone number. Leave blank to keep current.", "New notes about the client. Leave blank to keep current.": "New notes about the client. Leave blank to keep current.", "Filter appointments by client first name.": "Filter appointments by client first name.", "Filter appointments by client last name.": "Filter appointments by client last name.", "Filter appointments by client e-mail address.": "Filter appointments by client e-mail address.", "Filter appointments by client phone number. URL encode '+' if using country codes (e.g., %2B1234567890).": "Filter appointments by client phone number. URL encode '+' if using country codes (e.g., %2B1234567890).", "Only get appointments on or after this date.": "Only get appointments on or after this date.", "Only get appointments on or before this date.": "Only get appointments on or before this date.", "Show only appointments on the calendar with this ID.": "Show only appointments on the calendar with this ID.", "Show only appointments of this type.": "Show only appointments of this type.", "Filter by appointment status.": "Filter by appointment status.", "Maximum number of results to return (default 100).": "Maximum number of results to return (default 100).", "Sort direction for the results.": "Sort direction for the results.", "Filter client list by first name, last name, or phone number.": "Filter client list by first name, last name, or phone number.", "Authorization headers are injected automatically from your connection.": "Authorization headers are injected automatically from your connection.", "Scheduled": "Scheduled", "Canceled": "Canceled", "All (Scheduled & Canceled)": "All (Scheduled & Canceled)", "Descending (DESC)": "Descending (DESC)", "Ascending (ASC)": "Ascending (ASC)", "GET": "GET", "POST": "POST", "PATCH": "PATCH", "PUT": "PUT", "DELETE": "DELETE", "HEAD": "HEAD", "Appointment Canceled": "Appointment Canceled", "New Appointment": "New Appointment", "Triggers when an appointment is canceled.": "Triggers when an appointment is canceled.", "Triggers when a new appointment is scheduled.": "Triggers when a new appointment is scheduled.", "Calendar": "Calendar"}
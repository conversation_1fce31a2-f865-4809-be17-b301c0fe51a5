{"name": "pieces-acuity-scheduling", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/pieces/community/acuity-scheduling/src", "projectType": "library", "release": {"version": {"generatorOptions": {"packageRoot": "dist/{projectRoot}", "currentVersionResolver": "git-tag"}}}, "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/pieces/community/acuity-scheduling", "tsConfig": "packages/pieces/community/acuity-scheduling/tsconfig.lib.json", "packageJson": "packages/pieces/community/acuity-scheduling/package.json", "main": "packages/pieces/community/acuity-scheduling/src/index.ts", "assets": ["packages/pieces/community/acuity-scheduling/*.md", {"input": "packages/pieces/community/acuity-scheduling/src/i18n", "output": "./src/i18n", "glob": "**/!(i18n.json)"}], "buildableProjectDepsInPackageJsonType": "dependencies", "updateBuildableProjectDepsInPackageJson": true}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}}
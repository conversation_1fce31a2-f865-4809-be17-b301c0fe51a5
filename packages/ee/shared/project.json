{"name": "ee-shared", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/ee/shared/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/ee/shared", "main": "packages/ee/shared/src/index.ts", "tsConfig": "packages/ee/shared/tsconfig.lib.json", "assets": ["packages/ee/shared/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}, "tags": []}
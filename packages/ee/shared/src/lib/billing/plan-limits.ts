import { AiOverageState, PlatformPlanLimits } from '@activepieces/shared'

export type PlatformPlanWithOnlyLimits = Omit<PlatformPlanLimits, 'stripeSubscriptionStartDate' | 'stripeSubscriptionEndDate'>

export const FREE_CLOUD_PLAN: PlatformPlanWithOnlyLimits = {
    plan: 'free',
    tasksLimit: 1000,
    includedAiCredits: 200,
    aiCreditsOverageLimit: undefined,
    aiCreditsOverageState: AiOverageState.NOT_ALLOWED,
    activeFlowsLimit: 2,
    eligibleForTrial: true,
    userSeatsLimit: 1,
    projectsLimit: 1,
    tablesLimit: 1,
    mcpLimit: 1,
    agentsLimit: 0,

    agentsEnabled: true,
    tablesEnabled: true,
    todosEnabled: true,
    mcpsEnabled: true,

    embeddingEnabled: false,
    globalConnectionsEnabled: false,
    customRolesEnabled: false,
    environmentsEnabled: false,
    analyticsEnabled: false,
    showPoweredBy: false,
    auditLogEnabled: false,
    managePiecesEnabled: false,
    manageTemplatesEnabled: false,
    customAppearanceEnabled: false,
    manageProjectsEnabled: false,
    projectRolesEnabled: false,
    customDomainsEnabled: false,
    apiKeysEnabled: false,
    ssoEnabled: false,
}


export const APPSUMO_PLAN = ({ planName: planname, tasksLimit, userSeatsLimit, agentsLimit, tablesLimit, mcpLimit }: { planName: string, tasksLimit: number, userSeatsLimit: number, agentsLimit: number, tablesLimit: number, mcpLimit: number }): PlatformPlanWithOnlyLimits => {
    return {
        plan: planname,
        tasksLimit,
        userSeatsLimit,
        includedAiCredits: 200,
        aiCreditsOverageState: AiOverageState.ALLOWED_BUT_OFF,
        aiCreditsOverageLimit: undefined,
        activeFlowsLimit: undefined,
        projectsLimit: 1,
        mcpLimit,
        tablesLimit,
        agentsLimit,
        eligibleForTrial: false,

        agentsEnabled: true,
        tablesEnabled: true,
        todosEnabled: true,
        mcpsEnabled: true,

        embeddingEnabled: false,
        globalConnectionsEnabled: false,
        customRolesEnabled: false,
        environmentsEnabled: false,
        analyticsEnabled: false,
        showPoweredBy: false,
        auditLogEnabled: false,
        managePiecesEnabled: false,
        manageTemplatesEnabled: false,
        customAppearanceEnabled: false,
        manageProjectsEnabled: false,
        projectRolesEnabled: true,
        customDomainsEnabled: false,
        apiKeysEnabled: false,
        ssoEnabled: false,

    }
}
export const PLUS_CLOUD_PLAN: PlatformPlanWithOnlyLimits = {
    plan: 'plus',
    tasksLimit: undefined,
    includedAiCredits: 500,
    aiCreditsOverageLimit: undefined,
    aiCreditsOverageState: AiOverageState.ALLOWED_BUT_OFF,
    eligibleForTrial: false,
    activeFlowsLimit: 10,
    userSeatsLimit: 1,
    projectsLimit: 1,
    mcpLimit: undefined,
    tablesLimit: undefined,
    agentsLimit: undefined,

    agentsEnabled: true,
    tablesEnabled: true,
    todosEnabled: true,
    mcpsEnabled: true,

    embeddingEnabled: false,
    globalConnectionsEnabled: false,
    customRolesEnabled: false,
    environmentsEnabled: false,
    analyticsEnabled: false,
    managePiecesEnabled: false,
    manageTemplatesEnabled: false,
    customAppearanceEnabled: false,
    manageProjectsEnabled: false,
    projectRolesEnabled: false,
    customDomainsEnabled: false,
    apiKeysEnabled: false,
    ssoEnabled: false,
    showPoweredBy: false,
    auditLogEnabled: false,
}


export const BUSINESS_CLOUD_PLAN: PlatformPlanWithOnlyLimits = {
    plan: 'business',
    tasksLimit: undefined,
    includedAiCredits: 1000,
    aiCreditsOverageLimit: undefined,
    aiCreditsOverageState: AiOverageState.ALLOWED_BUT_OFF,
    eligibleForTrial: false,
    activeFlowsLimit: 50,
    userSeatsLimit: 5,
    projectsLimit: 10,
    mcpLimit: undefined,
    tablesLimit: undefined,
    agentsLimit: undefined,

    agentsEnabled: true,
    tablesEnabled: true,
    todosEnabled: true,
    mcpsEnabled: true,

    embeddingEnabled: false,
    globalConnectionsEnabled: false,
    customRolesEnabled: false,
    environmentsEnabled: false,
    analyticsEnabled: true,
    managePiecesEnabled: false,
    manageTemplatesEnabled: false,
    customAppearanceEnabled: false,
    manageProjectsEnabled: true,
    projectRolesEnabled: true,
    customDomainsEnabled: false,
    apiKeysEnabled: true,
    ssoEnabled: true,
    showPoweredBy: false,
    auditLogEnabled: false,

}

export const OPEN_SOURCE_PLAN: PlatformPlanWithOnlyLimits = {
    eligibleForTrial: false,
    embeddingEnabled: false,

    globalConnectionsEnabled: false,
    customRolesEnabled: false,
    tasksLimit: undefined,

    mcpsEnabled: true,
    tablesEnabled: true,
    todosEnabled: true,
    agentsEnabled: true,
    includedAiCredits: 0,
    aiCreditsOverageLimit: undefined,
    aiCreditsOverageState: AiOverageState.ALLOWED_BUT_OFF,
    environmentsEnabled: false,
    agentsLimit: undefined,
    analyticsEnabled: false,
    showPoweredBy: false,

    auditLogEnabled: false,
    managePiecesEnabled: false,
    manageTemplatesEnabled: false,
    customAppearanceEnabled: false,
    manageProjectsEnabled: false,
    projectRolesEnabled: false,
    customDomainsEnabled: false,
    apiKeysEnabled: false,
    ssoEnabled: false,
    stripeCustomerId: undefined,
    stripeSubscriptionId: undefined,
    stripeSubscriptionStatus: undefined,
}


{
  "extends": "../../tsconfig.base.json",
  "compilerOptions": {
    "module": "commonjs",
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "noImplicitOverride": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "allowJs": true,
  },
  "files": [],
  "include": [],
  "references": [
    {
      "path": "./tsconfig.lib.json",
    },
    {
      "path": "./tsconfig.spec.json"
    }
  ]
}

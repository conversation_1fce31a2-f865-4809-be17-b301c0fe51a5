{"name": "engine", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/engine/src", "projectType": "library", "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/packages/engine", "main": "packages/engine/src/main.ts", "tsConfig": "packages/engine/tsconfig.lib.json", "assets": [], "webpackConfig": "packages/engine/webpack.config.js", "babelUpwardRootMode": true, "statsJson": false, "sourceMap": true}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false, "statsJson": false}}}, "serve": {"executor": "@nx/js:node", "options": {"buildTarget": "engine:build", "inspect": false}}, "publish": {"executor": "nx:run-commands", "options": {"command": "node tools/scripts/publish.mjs engine {args.ver} {args.tag}"}, "dependsOn": ["build"]}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/engine/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/engine/jest.config.ts", "silent": true}}}, "tags": []}
encryptionsalt: v1:icXg2cmIvSc=:v1:y8+4YhdMCPPDY26J:5cNYmimH353n8sjUDDc6srvcPgb+8Q==
config:
  activepieces:environment: "prod"
  activepieces:apEncryptionKey:  
  activepieces:apJwtSecret: 
  activepieces:deployLocalBuild: "true"
  activepieces:repoName: "activepieces-prod-repo"
  activepieces:containerCpu: "512"
  activepieces:containerMemory: "1024"
  activepieces:containerInstances: "1"
  activepieces:usePostgres: "true"
  activepieces:dbInstanceClass: "db.t3.small"
  activepieces:dbIsPublic: "false"
  activepieces:dbPassword:
    secure: v1:MXNSOcqZCp10X2PX:mU2iTrcETjdisk8FkD5yHLJYUxRei/9l
  activepieces:dbUsername: "postgres"
  activepieces:useRedis: "true"
  activepieces:redisNodeType: "cache.t3.small"
  aws:region: "us-east-1"

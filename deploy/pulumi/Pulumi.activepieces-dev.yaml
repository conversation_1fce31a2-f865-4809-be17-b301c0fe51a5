encryptionsalt: v1:wHCVNl3bj/g=:v1:mxogi9ZeBjIcxNZC:q+bjpLv9rnJnu8qq7xwKGLd/GAZOqA==
config:
  activepieces:environment: "dev"
  activepieces:apEncryptionKey:  
  activepieces:apJwtSecret: 
  activepieces:deployLocalBuild: "false"
  activepieces:repoName:
  activepieces:containerCpu: "256"
  activepieces:containerMemory: "512"
  activepieces:containerInstances: "1"
  activepieces:usePostgres: "false"
  activepieces:dbInstanceClass: "db.t3.small"
  activepieces:dbUsername: "postgres"
  activepieces:dbIsPublic: "false"
  activepieces:dbPassword:
    secure: v1:MXNSOcqZCp10X2PX:mU2iTrcETjdisk8FkD5yHLJYUxRei/9l
  activepieces:addIpToPostgresSecurityGroup:
  activepieces:useRedis: "false"
  activepieces:redisNodeType: "cache.t3.small"
  activepieces:domain:
  activepieces:subDomain:
  aws:region: "us-east-1"
